import 'package:equatable/equatable.dart';
import '../../models/notification_models.dart';

/// Base notification state
abstract class NotificationState extends Equatable {
  const NotificationState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class NotificationInitial extends NotificationState {
  const NotificationInitial();
}

/// Loading state
class NotificationLoading extends NotificationState {
  const NotificationLoading();
}

/// Notifications loaded successfully
class NotificationsLoaded extends NotificationState {
  final List<LocalNotification> notifications;
  final NotificationSettings settings;
  final int unreadCount;

  const NotificationsLoaded({
    required this.notifications,
    required this.settings,
    required this.unreadCount,
  });

  @override
  List<Object> get props => [notifications, settings, unreadCount];

  NotificationsLoaded copyWith({
    List<LocalNotification>? notifications,
    NotificationSettings? settings,
    int? unreadCount,
  }) {
    return NotificationsLoaded(
      notifications: notifications ?? this.notifications,
      settings: settings ?? this.settings,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }
}

/// Notification scheduled successfully
class NotificationScheduled extends NotificationState {
  final LocalNotification notification;

  const NotificationScheduled(this.notification);

  @override
  List<Object> get props => [notification];
}

/// Notification settings loaded
class NotificationSettingsLoaded extends NotificationState {
  final NotificationSettings settings;

  const NotificationSettingsLoaded(this.settings);

  @override
  List<Object> get props => [settings];
}

/// Notification settings updated
class NotificationSettingsUpdated extends NotificationState {
  final NotificationSettings settings;

  const NotificationSettingsUpdated(this.settings);

  @override
  List<Object> get props => [settings];
}

/// Notification marked as read
class NotificationMarkedAsRead extends NotificationState {
  final int notificationId;

  const NotificationMarkedAsRead(this.notificationId);

  @override
  List<Object> get props => [notificationId];
}

/// Notification cancelled
class NotificationCancelled extends NotificationState {
  final int notificationId;

  const NotificationCancelled(this.notificationId);

  @override
  List<Object> get props => [notificationId];
}

/// All notifications cleared
class NotificationsCleared extends NotificationState {
  const NotificationsCleared();
}

/// Notification permissions granted
class NotificationPermissionsGranted extends NotificationState {
  final bool granted;

  const NotificationPermissionsGranted(this.granted);

  @override
  List<Object> get props => [granted];
}

/// Notification service initialized
class NotificationServiceInitialized extends NotificationState {
  const NotificationServiceInitialized();
}

/// Notification tapped - navigation required
class NotificationTapped extends NotificationState {
  final int notificationId;
  final String? navigationRoute;
  final Map<String, dynamic>? navigationData;

  const NotificationTapped({
    required this.notificationId,
    this.navigationRoute,
    this.navigationData,
  });

  @override
  List<Object?> get props => [notificationId, navigationRoute, navigationData];
}

/// Notifications synced with backend
class NotificationsSynced extends NotificationState {
  final int syncedCount;

  const NotificationsSynced(this.syncedCount);

  @override
  List<Object> get props => [syncedCount];
}

/// Error state
class NotificationError extends NotificationState {
  final String message;
  final String? errorCode;

  const NotificationError(this.message, {this.errorCode});

  @override
  List<Object?> get props => [message, errorCode];
}

/// Specific error states for better UX
class NotificationPermissionDenied extends NotificationError {
  const NotificationPermissionDenied() 
      : super('Notification permissions are required for reminders and alerts');
}

class NotificationSchedulingError extends NotificationError {
  const NotificationSchedulingError(String message) 
      : super('Failed to schedule notification: $message');
}

class NotificationSyncError extends NotificationError {
  const NotificationSyncError(String message) 
      : super('Failed to sync notifications: $message');
}

class NotificationSettingsError extends NotificationError {
  const NotificationSettingsError(String message) 
      : super('Failed to update notification settings: $message');
}

/// Loading states for specific operations
class NotificationScheduling extends NotificationState {
  final String title;

  const NotificationScheduling(this.title);

  @override
  List<Object> get props => [title];
}

class NotificationSettingsUpdating extends NotificationState {
  const NotificationSettingsUpdating();
}

class NotificationsSyncing extends NotificationState {
  const NotificationsSyncing();
}

/// Success states with messages
class NotificationOperationSuccess extends NotificationState {
  final String message;
  final String? actionType;

  const NotificationOperationSuccess(this.message, {this.actionType});

  @override
  List<Object?> get props => [message, actionType];
}

/// Contextual notification states
class ContextualNotificationSuggested extends NotificationState {
  final String context;
  final String suggestion;
  final DateTime suggestedTime;

  const ContextualNotificationSuggested({
    required this.context,
    required this.suggestion,
    required this.suggestedTime,
  });

  @override
  List<Object> get props => [context, suggestion, suggestedTime];
}

/// Batch operation states
class NotificationBatchOperation extends NotificationState {
  final String operation;
  final int totalCount;
  final int processedCount;

  const NotificationBatchOperation({
    required this.operation,
    required this.totalCount,
    required this.processedCount,
  });

  @override
  List<Object> get props => [operation, totalCount, processedCount];

  double get progress => totalCount > 0 ? processedCount / totalCount : 0.0;
  bool get isComplete => processedCount >= totalCount;
}

/// Notification analytics state
class NotificationAnalyticsLoaded extends NotificationState {
  final Map<NotificationType, int> deliveryStats;
  final Map<NotificationType, double> interactionRates;
  final int totalNotifications;
  final int totalInteractions;

  const NotificationAnalyticsLoaded({
    required this.deliveryStats,
    required this.interactionRates,
    required this.totalNotifications,
    required this.totalInteractions,
  });

  @override
  List<Object> get props => [
    deliveryStats,
    interactionRates,
    totalNotifications,
    totalInteractions,
  ];

  double get overallInteractionRate => 
      totalNotifications > 0 ? totalInteractions / totalNotifications : 0.0;
}
