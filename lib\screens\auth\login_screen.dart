import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'package:darvis_app/utils/form_validators.dart';
import 'package:darvis_app/services/navigation_service.dart';
import 'package:darvis_app/blocs/auth/auth_bloc.dart';
import 'package:darvis_app/blocs/auth/auth_event.dart';
import 'package:darvis_app/blocs/auth/auth_state.dart';
import 'package:darvis_app/screens/auth/signup_screen.dart';
import 'package:darvis_app/screens/auth/forgot_password_screen.dart';

// Reusable asset path constants
const String _kDarvisLogo = 'assets/images/darvis_main.PNG';
const String _kGoogleLogo = 'assets/icons/google_logo.svg';
const String _kEyeOnIcon = 'assets/icons/eye_on.svg';
const String _kEyeOffIcon = 'assets/icons/eye_off.svg';

/// LoginScreen: A screen for existing user authentication.
/// It reuses many components from the SignUpScreen for consistency.
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _isPasswordVisible = false;
  String? _emailError;
  String? _passwordError;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _validateForm() {
    setState(() {
      _emailError = FormValidators.validateEmail(_emailController.text);
      _passwordError = _passwordController.text.isEmpty ? 'Password is required' : null;
    });
  }

  bool get _isFormValid {
    return _emailError == null &&
           _passwordError == null &&
           _emailController.text.isNotEmpty &&
           _passwordController.text.isNotEmpty;
  }

  void _handleSignIn() {
    _validateForm();
    if (_isFormValid) {
      context.read<AuthBloc>().add(
        AuthSignInRequested(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        ),
      );
    }
  }

  void _handleGoogleSignIn() {
    context.read<AuthBloc>().add(AuthSignInWithGoogleRequested());
  }

  void _navigateToSignUp() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const SignUpScreen()),
    );
  }

  void _handleForgotPassword() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const ForgotPasswordScreen()),
    );
  }

  void _onEmailChanged(String value) {
    if (_emailError != null) {
      setState(() {
        _emailError = null;
      });
    }
  }

  void _onPasswordChanged(String value) {
    if (_passwordError != null) {
      setState(() {
        _passwordError = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthAuthenticated) {
          // Navigate to home screen on successful authentication
          GetIt.instance<NavigationService>().navigateToHome();
        } else if (state is AuthError) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: DesignTokens.backgroundApp,
        body: Stack(
        children: [
          // Reusing the same background glow from the sign-up screen.
          const _BackgroundGlow(),

          LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: DesignTokens.spacingXl),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Main Darvis logo with hover animation.
                        const _AnimatedDarvisLogo(),
                        const SizedBox(height: DesignTokens.spacingXxl),

                        // Reusing the social button for Gmail login.
                        _SocialLoginButton(
                          iconPath: _kGoogleLogo,
                          text: 'Gmail', // As per the design
                          onTap: _handleGoogleSignIn,
                        ),
                        const SizedBox(height: DesignTokens.spacingXl),

                        // The main login form.
                        _LoginForm(
                          onSignIn: _handleSignIn,
                          onForgotPassword: _handleForgotPassword,
                          emailController: _emailController,
                          passwordController: _passwordController,
                          emailError: _emailError,
                          passwordError: _passwordError,
                          onEmailChanged: _onEmailChanged,
                          onPasswordChanged: _onPasswordChanged,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
      ),
    );
  }
}

/// A stateful widget to handle the subtle hover/bounce animation for the main logo.
class _AnimatedDarvisLogo extends StatefulWidget {
  const _AnimatedDarvisLogo();

  @override
  State<_AnimatedDarvisLogo> createState() => _AnimatedDarvisLogoState();
}

class _AnimatedDarvisLogoState extends State<_AnimatedDarvisLogo>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<Offset> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _animation = Tween<Offset>(
      begin: const Offset(0, -0.08),
      end: const Offset(0, 0.08),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _animation,
      child: Image.asset(
        _kDarvisLogo,
        height: 200, // Adjust height as needed for visual balance
      ),
    );
  }
}

/// The main form for the login screen.
class _LoginForm extends StatefulWidget {
  final VoidCallback onSignIn;
  final VoidCallback onForgotPassword;
  final TextEditingController emailController;
  final TextEditingController passwordController;
  final String? emailError;
  final String? passwordError;
  final ValueChanged<String> onEmailChanged;
  final ValueChanged<String> onPasswordChanged;

  const _LoginForm({
    required this.onSignIn,
    required this.onForgotPassword,
    required this.emailController,
    required this.passwordController,
    required this.emailError,
    required this.passwordError,
    required this.onEmailChanged,
    required this.onPasswordChanged,
  });

  @override
  State<_LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<_LoginForm> {
  bool _isPasswordObscured = true;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _TitledTextField(
          title: 'Email Address',
          hintText: '<EMAIL>',
          keyboardType: TextInputType.emailAddress,
          controller: widget.emailController,
          errorText: widget.emailError,
          onChanged: widget.onEmailChanged,
        ),
        const SizedBox(height: DesignTokens.spacingMd),
        _TitledTextField(
          title: 'Password',
          hintText: '••••••••',
          isObscured: _isPasswordObscured,
          controller: widget.passwordController,
          errorText: widget.passwordError,
          onChanged: widget.onPasswordChanged,
          suffixIcon: IconButton(
            icon: SvgPicture.asset(
              _isPasswordObscured ? _kEyeOffIcon : _kEyeOnIcon,
              colorFilter: const ColorFilter.mode(
                  DesignTokens.iconSecondary, BlendMode.srcIn),
            ),
            onPressed: () =>
                setState(() => _isPasswordObscured = !_isPasswordObscured),
          ),
        ),
        const SizedBox(height: DesignTokens.spacingLg),
        _ForgotPasswordLink(
          onTap: widget.onForgotPassword,
        ),
        const SizedBox(height: DesignTokens.spacingLg),
        BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            final isLoading = state is AuthLoading;
            return _PrimaryActionButton(
              text: isLoading ? 'Signing In...' : 'Sign In',
              onTap: isLoading ? () {} : widget.onSignIn,
            );
          },
        ),
      ],
    );
  }
}

// --- Reused Widgets (Adapted from your tweaked SignUpScreen code) ---

class _SocialLoginButton extends StatelessWidget {
  final String iconPath;
  final String text;
  final VoidCallback onTap;

  const _SocialLoginButton(
      {required this.iconPath, required this.text, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 150,
        height: 46,
        decoration: BoxDecoration(
          color: DesignTokens.googleButtonBackground,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(iconPath, height: 24, width: 24),
            const SizedBox(width: DesignTokens.spacingMd),
            Text(text, style: DesignTokens.googleButtonTextStyle),
          ],
        ),
      ),
    );
  }
}

class _TitledTextField extends StatelessWidget {
  final String title;
  final String hintText;
  final bool isObscured;
  final Widget? suffixIcon;
  final TextInputType? keyboardType;
  final TextEditingController? controller;
  final String? errorText;
  final ValueChanged<String>? onChanged;

  const _TitledTextField({
    required this.title,
    required this.hintText,
    this.isObscured = false,
    this.suffixIcon,
    this.keyboardType,
    this.controller,
    this.errorText,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingSm),
        TextField(
          controller: controller,
          obscureText: isObscured,
          keyboardType: keyboardType,
          onChanged: onChanged,
          style:
              DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
          decoration: InputDecoration(
            filled: true,
            fillColor: DesignTokens.formFieldBackground,
            hintText: hintText,
            hintStyle:
                DesignTokens.bodyStyle.copyWith(color: DesignTokens.textMuted),
            suffixIcon: suffixIcon,
            errorText: errorText,
            errorStyle: DesignTokens.bodyStyle.copyWith(
              color: Colors.red,
              fontSize: 12,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              borderSide: BorderSide.none,
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              borderSide: const BorderSide(color: Colors.red, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingSm,
            ),
          ),
        ),
      ],
    );
  }
}

class _ForgotPasswordLink extends StatelessWidget {
  final VoidCallback onTap;
  const _ForgotPasswordLink({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Center(
        child: Text(
          'Forgot Password?',
          style: DesignTokens.linkTextStyle
              .copyWith(color: DesignTokens.link),
        ),
      ),
    );
  }
}

class _PrimaryActionButton extends StatelessWidget {
  final String text;
  final VoidCallback onTap;
  const _PrimaryActionButton({required this.text, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: 150,
          height: 45,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: DesignTokens.primaryButtonBackground,
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          ),
          child: Text(text, style: DesignTokens.primaryButtonTextStyle),
        ),
      ),
    );
  }
}

class _BackgroundGlow extends StatelessWidget {
  const _BackgroundGlow();

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.1,
        decoration: const BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Color.fromARGB(255, 9, 38, 107),
              blurRadius: 150.0,
              spreadRadius: 50.0,
            ),
          ],
        ),
      ),
    );
  }
}
