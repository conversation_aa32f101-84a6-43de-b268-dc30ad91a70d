# App Settings Outline

This document outlines all user-configurable settings available in the Darvis app's profile section, based on the existing UI components.

---

## Account Settings (`account_settings_screen.dart`)

**Personal Information**
*   **Profile Photo**: Allows the user to change their profile picture.
    *   **UI Type**: Button / Image Picker.
*   **Name**: User's display name.
    *   **UI Type**: Text Field.
*   **What should <PERSON><PERSON> call you?**: The name the AI assistant uses to address the user.
    *   **UI Type**: Text Field.
*   **Time Zone**: Sets the user's local time zone for accurate scheduling and reminders.
    *   **UI Type**: Dropdown.
    *   **Options**: A comprehensive list of UTC offsets (e.g., 'UTC-5 (Eastern Time)', 'UTC+1 (Central Europe)').

**Authentication & Security**
*   **Change Password**: A button to navigate to a password change flow.
    *   **UI Type**: Button.
*   **Two-Factor Authentication**: Enables or disables an extra layer of security for the user's account.
    *   **UI Type**: Toggle.
*   **Biometric Login**: Allows users to sign in using Face ID or Touch ID.
    *   **UI Type**: Toggle.

**Subscription & Usage**
*   **Subscription Plan**: Displays the current plan and allows switching between available tiers.
    *   **UI Type**: PageView with interactive cards.
    *   **Options**: 'Essential', 'Unlimited'.
*   **Upgrade to Unlimited**: A button to initiate the subscription upgrade process.
    *   **UI Type**: Button.

**Account Management**
*   **Sign Out**: Logs the user out of the application.
    *   **UI Type**: Button (with confirmation dialog).
*   **Delete Account**: Permanently deletes the user's account and all associated data.
    *   **UI Type**: Button (with confirmation dialog).

---

## Privacy & Security (`privacy_security_screen.dart`)

**Data Privacy**
*   **Therapy Session Encryption**: Toggles end-to-end encryption for all therapy conversations.
    *   **UI Type**: Toggle.
*   **Anonymous Usage Analytics**: Allows the user to opt-in or out of sharing anonymous data to improve the app.
    *   **UI Type**: Toggle.

**Content Control**
*   **Auto-Delete Old Conversations**: Automatically deletes conversations older than 30 days.
    *   **UI Type**: Toggle.

**Security Features**
*   **Session Timeout**: Automatically signs the user out after a period of inactivity.
    *   **UI Type**: Dropdown.
    *   **Options**: '15 minutes', '30 minutes', '1 hour', '2 hours', 'Never'.
*   **App Lock Requirements**: Requires biometric or passcode authentication to open the app.
    *   **UI Type**: Toggle.

---

## Notifications (`notifications_screen.dart`)

**Notification Types**
*   **Therapy Reminders**: Notifications for scheduled therapy sessions.
    *   **UI Type**: Toggle.
*   **Task Notifications**: Reminders for upcoming tasks and deadlines.
    *   **UI Type**: Toggle.
*   **Daily Check-ins**: Daily wellness and mood check-in reminders.
    *   **UI Type**: Toggle.
*   **Weekly Reports**: Notifications when the weekly summary of progress and insights is ready.
    *   **UI Type**: Toggle.
*   **System Updates**: App updates and new feature announcements.
    *   **UI Type**: Toggle.

**Quiet Hours**
*   **Do Not Disturb**: Sets a time range during which no notifications will be sent.
    *   **UI Type**: Two Time Pickers (Start and End).

**Notification Frequency**
*   **Notification Frequency**: Controls the rate of non-critical notifications.
    *   **UI Type**: Dropdown.
    *   **Options**: 'Low', 'Normal', 'High'.

---

## Data & Storage (`data_storage_screen.dart`)

**Cloud Sync**
*   **Cross-Device Synchronization**: Master toggle to enable or disable data sync across all devices.
    *   **UI Type**: Toggle.
*   **Sync Frequency**: Determines how often data is synced with the cloud.
    *   **UI Type**: Dropdown.
    *   **Options**: 'Real-time', 'Hourly', 'Daily'.

**Selective Sync**
*   **Conversations**: Toggle to sync therapy and chat conversations.
    *   **UI Type**: Toggle.
*   **Notes**: Toggle to sync all notes and documents.
    *   **UI Type**: Toggle.
*   **Tasks**: Toggle to sync tasks and productivity data.
    *   **UI Type**: Toggle.
*   **Settings**: Toggle to sync app settings across devices.
    *   **UI Type**: Toggle.

**Data Management**
*   **Manage Storage**: A button to clear up storage space.
    *   **UI Type**: Button.
*   **Export Data**: Allows the user to export their data in a portable format.
    *   **UI Type**: Button.
*   **Import Data**: Allows the user to import data from a backup file.
    *   **UI Type**: Button.
*   **Clear Cache**: Clears locally cached data to free up space.
    *   **UI Type**: Button.
