import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:isar/isar.dart';
import '../../models/isar_models.dart';
import '../../services/api_service.dart';
import '../../services/sync_engine.dart';
import 'notes_event.dart';
import 'notes_state.dart';

/// BLoC for managing notes with offline-first approach
class NotesBloc extends Bloc<NotesEvent, NotesState> {
  final Isar _isar;
  final ApiService _apiService;
  final SyncEngine _syncEngine;

  NotesBloc({
    required Isar isar,
    required ApiService apiService,
    required SyncEngine syncEngine,
  })  : _isar = isar,
        _apiService = apiService,
        _syncEngine = syncEngine,
        super(const NotesInitial()) {
    
    // Register event handlers
    on<LoadNotes>(_onLoadNotes);
    on<RefreshNotes>(_onRefreshNotes);
    on<AddNote>(_onAddNote);
    on<UpdateNote>(_onUpdateNote);
    on<DeleteNote>(_onDeleteNote);
    on<TogglePinNote>(_onTogglePinNote);
    on<SearchNotes>(_onSearchNotes);
    on<FilterNotesByTag>(_onFilterNotesByTag);
    on<SortNotes>(_onSortNotes);
    on<ClearNotesFilters>(_onClearNotesFilters);
    on<ToggleNoteSelection>(_onToggleNoteSelection);
    on<ClearNoteSelections>(_onClearNoteSelections);
    on<DeleteSelectedNotes>(_onDeleteSelectedNotes);
    on<PinSelectedNotes>(_onPinSelectedNotes);
    on<UnpinSelectedNotes>(_onUnpinSelectedNotes);
    on<AddTagToSelectedNotes>(_onAddTagToSelectedNotes);
    on<ExportNotes>(_onExportNotes);
    on<SyncNotes>(_onSyncNotes);
  }

  /// Load notes from local database
  Future<void> _onLoadNotes(
    LoadNotes event,
    Emitter<NotesState> emit,
  ) async {
    emit(const NotesLoading());

    try {
      final notes = await _isar.localNotes.where().sortByUpdatedAtDesc().findAll();
      final availableTags = _extractAvailableTags(notes);
      
      emit(NotesLoaded(
        notes: notes,
        filteredNotes: notes,
        availableTags: availableTags,
      ));

      // Trigger background sync
      add(const SyncNotes());
    } catch (e) {
      emit(NotesOperationError(
        error: 'Failed to load notes: $e',
        operation: 'load',
      ));
    }
  }

  /// Refresh notes from server
  Future<void> _onRefreshNotes(
    RefreshNotes event,
    Emitter<NotesState> emit,
  ) async {
    try {
      // Force sync with server
      await _syncEngine.sync();
      
      // Reload from local database
      add(const LoadNotes());
    } catch (e) {
      emit(NotesOperationError(
        error: 'Failed to refresh notes: $e',
        operation: 'refresh',
      ));
    }
  }

  /// Add a new note
  Future<void> _onAddNote(
    AddNote event,
    Emitter<NotesState> emit,
  ) async {
    emit(const NotesOperationInProgress(operation: 'add'));

    try {
      final now = DateTime.now();
      final note = LocalNote()
        ..title = event.title
        ..content = event.content
        ..tags = event.tags
        ..isPinned = event.isPinned
        ..createdAt = now
        ..updatedAt = now
        ..needsSync = true
        ..syncStatus = 'pending'
        ..syncRetryCount = 0;

      await _isar.writeTxn(() async {
        await _isar.localNotes.put(note);
      });

      emit(NotesOperationSuccess(
        message: 'Note created successfully',
        operation: 'add',
        note: note,
      ));

      // Reload notes
      add(const LoadNotes());

      // Queue sync operation
      _syncEngine.queueCreate(
        entityType: 'note',
        data: {
          'title': note.title,
          'content': note.content,
          'tags': note.tags,
          'isPinned': note.isPinned,
        },
        localId: note.id.toString(),
      );
    } catch (e) {
      emit(NotesOperationError(
        error: 'Failed to create note: $e',
        operation: 'add',
      ));
    }
  }

  /// Update an existing note
  Future<void> _onUpdateNote(
    UpdateNote event,
    Emitter<NotesState> emit,
  ) async {
    emit(NotesOperationInProgress(operation: 'update', noteId: event.noteId));

    try {
      final noteId = int.tryParse(event.noteId);
      if (noteId == null) {
        throw Exception('Invalid note ID');
      }

      final note = await _isar.localNotes.get(noteId);
      if (note == null) {
        throw Exception('Note not found');
      }

      // Update fields
      if (event.title != null) note.title = event.title!;
      if (event.content != null) note.content = event.content!;
      if (event.tags != null) note.tags = event.tags!;
      if (event.isPinned != null) note.isPinned = event.isPinned!;
      
      note.updatedAt = DateTime.now();
      note.needsSync = true;
      note.syncStatus = 'pending';

      await _isar.writeTxn(() async {
        await _isar.localNotes.put(note);
      });

      emit(NotesOperationSuccess(
        message: 'Note updated successfully',
        operation: 'update',
        note: note,
      ));

      // Reload notes
      add(const LoadNotes());

      // Queue sync operation
      _syncEngine.queueUpdate(
        entityType: 'note',
        id: note.serverId ?? note.id.toString(),
        data: {
          'title': note.title,
          'content': note.content,
          'tags': note.tags,
          'isPinned': note.isPinned,
        },
      );
    } catch (e) {
      emit(NotesOperationError(
        error: 'Failed to update note: $e',
        operation: 'update',
        noteId: event.noteId,
      ));
    }
  }

  /// Delete a note
  Future<void> _onDeleteNote(
    DeleteNote event,
    Emitter<NotesState> emit,
  ) async {
    emit(NotesOperationInProgress(operation: 'delete', noteId: event.noteId));

    try {
      final noteId = int.tryParse(event.noteId);
      if (noteId == null) {
        throw Exception('Invalid note ID');
      }

      await _isar.writeTxn(() async {
        await _isar.localNotes.delete(noteId);
      });

      emit(const NotesOperationSuccess(
        message: 'Note deleted successfully',
        operation: 'delete',
      ));

      // Reload notes
      add(const LoadNotes());

      // Queue delete operation
      _syncEngine.queueDelete(
        entityType: 'note',
        id: event.noteId,
      );
    } catch (e) {
      emit(NotesOperationError(
        error: 'Failed to delete note: $e',
        operation: 'delete',
        noteId: event.noteId,
      ));
    }
  }

  /// Toggle pin status of a note
  Future<void> _onTogglePinNote(
    TogglePinNote event,
    Emitter<NotesState> emit,
  ) async {
    try {
      final noteId = int.tryParse(event.noteId);
      if (noteId == null) {
        throw Exception('Invalid note ID');
      }

      final note = await _isar.localNotes.get(noteId);
      if (note == null) {
        throw Exception('Note not found');
      }

      note.isPinned = !note.isPinned;
      note.updatedAt = DateTime.now();
      note.needsSync = true;
      note.syncStatus = 'pending';

      await _isar.writeTxn(() async {
        await _isar.localNotes.put(note);
      });

      // Reload notes
      add(const LoadNotes());

      // Queue update operation
      _syncEngine.queueUpdate(
        entityType: 'note',
        id: note.serverId ?? note.id.toString(),
        data: {
          'isPinned': note.isPinned,
        },
      );
    } catch (e) {
      emit(NotesOperationError(
        error: 'Failed to toggle pin: $e',
        operation: 'toggle_pin',
        noteId: event.noteId,
      ));
    }
  }

  /// Search notes by query
  Future<void> _onSearchNotes(
    SearchNotes event,
    Emitter<NotesState> emit,
  ) async {
    if (state is! NotesLoaded) return;

    final currentState = state as NotesLoaded;
    final filteredNotes = _filterAndSortNotes(
      currentState.notes,
      searchQuery: event.query,
      selectedTag: currentState.selectedTag,
      sortType: currentState.sortType,
    );

    emit(currentState.copyWith(
      filteredNotes: filteredNotes,
      searchQuery: event.query,
    ));
  }

  /// Filter notes by tag
  Future<void> _onFilterNotesByTag(
    FilterNotesByTag event,
    Emitter<NotesState> emit,
  ) async {
    if (state is! NotesLoaded) return;

    final currentState = state as NotesLoaded;
    final filteredNotes = _filterAndSortNotes(
      currentState.notes,
      searchQuery: currentState.searchQuery,
      selectedTag: event.tag,
      sortType: currentState.sortType,
    );

    emit(currentState.copyWith(
      filteredNotes: filteredNotes,
      selectedTag: event.tag,
    ));
  }

  /// Sort notes
  Future<void> _onSortNotes(
    SortNotes event,
    Emitter<NotesState> emit,
  ) async {
    if (state is! NotesLoaded) return;

    final currentState = state as NotesLoaded;
    final filteredNotes = _filterAndSortNotes(
      currentState.notes,
      searchQuery: currentState.searchQuery,
      selectedTag: currentState.selectedTag,
      sortType: event.sortType,
    );

    emit(currentState.copyWith(
      filteredNotes: filteredNotes,
      sortType: event.sortType,
    ));
  }

  /// Clear search and filters
  Future<void> _onClearNotesFilters(
    ClearNotesFilters event,
    Emitter<NotesState> emit,
  ) async {
    if (state is! NotesLoaded) return;

    final currentState = state as NotesLoaded;
    final filteredNotes = _filterAndSortNotes(
      currentState.notes,
      sortType: currentState.sortType,
    );

    emit(currentState.copyWith(
      filteredNotes: filteredNotes,
      clearSearchQuery: true,
      clearSelectedTag: true,
    ));
  }

  /// Toggle note selection for bulk operations
  Future<void> _onToggleNoteSelection(
    ToggleNoteSelection event,
    Emitter<NotesState> emit,
  ) async {
    if (state is! NotesLoaded) return;

    final currentState = state as NotesLoaded;
    final selectedIds = Set<String>.from(currentState.selectedNoteIds);

    if (selectedIds.contains(event.noteId)) {
      selectedIds.remove(event.noteId);
    } else {
      selectedIds.add(event.noteId);
    }

    emit(currentState.copyWith(
      selectedNoteIds: selectedIds,
      isSelectionMode: selectedIds.isNotEmpty,
    ));
  }

  /// Clear all note selections
  Future<void> _onClearNoteSelections(
    ClearNoteSelections event,
    Emitter<NotesState> emit,
  ) async {
    if (state is! NotesLoaded) return;

    final currentState = state as NotesLoaded;
    emit(currentState.copyWith(
      selectedNoteIds: <String>{},
      isSelectionMode: false,
    ));
  }

  /// Delete selected notes
  Future<void> _onDeleteSelectedNotes(
    DeleteSelectedNotes event,
    Emitter<NotesState> emit,
  ) async {
    if (state is! NotesLoaded) return;

    final currentState = state as NotesLoaded;
    final selectedIds = currentState.selectedNoteIds;

    if (selectedIds.isEmpty) return;

    emit(NotesBulkOperationInProgress(
      operation: 'delete',
      totalCount: selectedIds.length,
    ));

    try {
      await _isar.writeTxn(() async {
        for (final noteId in selectedIds) {
          final id = int.tryParse(noteId);
          if (id != null) {
            await _isar.localNotes.delete(id);
          }
        }
      });

      emit(NotesBulkOperationSuccess(
        operation: 'delete',
        processedCount: selectedIds.length,
        message: '${selectedIds.length} notes deleted successfully',
      ));

      // Reload notes
      add(const LoadNotes());

      // Trigger sync for bulk delete
      await _syncEngine.sync();
    } catch (e) {
      emit(NotesBulkOperationError(
        operation: 'delete',
        error: 'Failed to delete notes: $e',
      ));
    }
  }

  /// Pin selected notes
  Future<void> _onPinSelectedNotes(
    PinSelectedNotes event,
    Emitter<NotesState> emit,
  ) async {
    if (state is! NotesLoaded) return;

    final currentState = state as NotesLoaded;
    final selectedIds = currentState.selectedNoteIds;

    if (selectedIds.isEmpty) return;

    emit(NotesBulkOperationInProgress(
      operation: 'pin',
      totalCount: selectedIds.length,
    ));

    try {
      await _isar.writeTxn(() async {
        for (final noteId in selectedIds) {
          final id = int.tryParse(noteId);
          if (id != null) {
            final note = await _isar.localNotes.get(id);
            if (note != null) {
              note.isPinned = true;
              note.updatedAt = DateTime.now();
              note.needsSync = true;
              note.syncStatus = 'pending';
              await _isar.localNotes.put(note);
            }
          }
        }
      });

      emit(NotesBulkOperationSuccess(
        operation: 'pin',
        processedCount: selectedIds.length,
        message: '${selectedIds.length} notes pinned successfully',
      ));

      // Reload notes
      add(const LoadNotes());

      // Trigger sync for bulk pin
      await _syncEngine.sync();
    } catch (e) {
      emit(NotesBulkOperationError(
        operation: 'pin',
        error: 'Failed to pin notes: $e',
      ));
    }
  }

  /// Unpin selected notes
  Future<void> _onUnpinSelectedNotes(
    UnpinSelectedNotes event,
    Emitter<NotesState> emit,
  ) async {
    if (state is! NotesLoaded) return;

    final currentState = state as NotesLoaded;
    final selectedIds = currentState.selectedNoteIds;

    if (selectedIds.isEmpty) return;

    emit(NotesBulkOperationInProgress(
      operation: 'unpin',
      totalCount: selectedIds.length,
    ));

    try {
      await _isar.writeTxn(() async {
        for (final noteId in selectedIds) {
          final id = int.tryParse(noteId);
          if (id != null) {
            final note = await _isar.localNotes.get(id);
            if (note != null) {
              note.isPinned = false;
              note.updatedAt = DateTime.now();
              note.needsSync = true;
              note.syncStatus = 'pending';
              await _isar.localNotes.put(note);
            }
          }
        }
      });

      emit(NotesBulkOperationSuccess(
        operation: 'unpin',
        processedCount: selectedIds.length,
        message: '${selectedIds.length} notes unpinned successfully',
      ));

      // Reload notes
      add(const LoadNotes());

      // Trigger sync for bulk unpin
      await _syncEngine.sync();
    } catch (e) {
      emit(NotesBulkOperationError(
        operation: 'unpin',
        error: 'Failed to unpin notes: $e',
      ));
    }
  }

  /// Add tag to selected notes
  Future<void> _onAddTagToSelectedNotes(
    AddTagToSelectedNotes event,
    Emitter<NotesState> emit,
  ) async {
    if (state is! NotesLoaded) return;

    final currentState = state as NotesLoaded;
    final selectedIds = currentState.selectedNoteIds;

    if (selectedIds.isEmpty) return;

    emit(NotesBulkOperationInProgress(
      operation: 'add_tag',
      totalCount: selectedIds.length,
    ));

    try {
      await _isar.writeTxn(() async {
        for (final noteId in selectedIds) {
          final id = int.tryParse(noteId);
          if (id != null) {
            final note = await _isar.localNotes.get(id);
            if (note != null && !note.tags.contains(event.tag)) {
              note.tags = [...note.tags, event.tag];
              note.updatedAt = DateTime.now();
              note.needsSync = true;
              note.syncStatus = 'pending';
              await _isar.localNotes.put(note);
            }
          }
        }
      });

      emit(NotesBulkOperationSuccess(
        operation: 'add_tag',
        processedCount: selectedIds.length,
        message: 'Tag "${event.tag}" added to ${selectedIds.length} notes',
      ));

      // Reload notes
      add(const LoadNotes());

      // Trigger sync for bulk tag add
      await _syncEngine.sync();
    } catch (e) {
      emit(NotesBulkOperationError(
        operation: 'add_tag',
        error: 'Failed to add tag: $e',
      ));
    }
  }

  /// Export notes
  Future<void> _onExportNotes(
    ExportNotes event,
    Emitter<NotesState> emit,
  ) async {
    emit(const NotesExportInProgress());

    try {
      List<LocalNote> notesToExport;

      if (event.noteIds != null) {
        // Export specific notes
        notesToExport = [];
        for (final noteId in event.noteIds!) {
          final id = int.tryParse(noteId);
          if (id != null) {
            final note = await _isar.localNotes.get(id);
            if (note != null) {
              notesToExport.add(note);
            }
          }
        }
      } else {
        // Export all notes
        notesToExport = await _isar.localNotes.where().findAll();
      }

      // TODO: Implement actual export functionality
      // For now, just simulate success
      emit(NotesExportSuccess(
        filePath: '/path/to/exported/notes.${event.format}',
        exportedCount: notesToExport.length,
        format: event.format,
      ));
    } catch (e) {
      emit(NotesExportError('Failed to export notes: $e'));
    }
  }

  /// Sync notes with server
  Future<void> _onSyncNotes(
    SyncNotes event,
    Emitter<NotesState> emit,
  ) async {
    emit(const NotesSyncInProgress());

    try {
      await _syncEngine.sync();

      final syncedCount = await _isar.localNotes.where().syncStatusEqualTo('synced').count();

      emit(NotesSyncSuccess(
        syncedCount: syncedCount,
        message: 'Notes synchronized successfully',
      ));

      // Reload notes after sync
      add(const LoadNotes());
    } catch (e) {
      emit(NotesSyncError('Failed to sync notes: $e'));
    }
  }

  /// Helper method to filter and sort notes
  List<LocalNote> _filterAndSortNotes(
    List<LocalNote> notes, {
    String? searchQuery,
    String? selectedTag,
    NotesSortType sortType = NotesSortType.updatedAtDesc,
  }) {
    var filteredNotes = List<LocalNote>.from(notes);

    // Apply search filter
    if (searchQuery != null && searchQuery.isNotEmpty) {
      filteredNotes = filteredNotes.where((note) {
        final query = searchQuery.toLowerCase();
        return note.title.toLowerCase().contains(query) ||
               note.content.toLowerCase().contains(query) ||
               note.tags.any((tag) => tag.toLowerCase().contains(query));
      }).toList();
    }

    // Apply tag filter
    if (selectedTag != null && selectedTag.isNotEmpty) {
      filteredNotes = filteredNotes.where((note) {
        return note.tags.contains(selectedTag);
      }).toList();
    }

    // Apply sorting
    switch (sortType) {
      case NotesSortType.createdAtDesc:
        filteredNotes.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case NotesSortType.createdAtAsc:
        filteredNotes.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case NotesSortType.updatedAtDesc:
        filteredNotes.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
        break;
      case NotesSortType.updatedAtAsc:
        filteredNotes.sort((a, b) => a.updatedAt.compareTo(b.updatedAt));
        break;
      case NotesSortType.titleAsc:
        filteredNotes.sort((a, b) => a.title.compareTo(b.title));
        break;
      case NotesSortType.titleDesc:
        filteredNotes.sort((a, b) => b.title.compareTo(a.title));
        break;
      case NotesSortType.pinned:
        filteredNotes.sort((a, b) {
          if (a.isPinned && !b.isPinned) return -1;
          if (!a.isPinned && b.isPinned) return 1;
          return b.updatedAt.compareTo(a.updatedAt);
        });
        break;
    }

    return filteredNotes;
  }

  /// Helper method to extract available tags from notes
  List<String> _extractAvailableTags(List<LocalNote> notes) {
    final tagSet = <String>{};
    for (final note in notes) {
      tagSet.addAll(note.tags);
    }
    final tags = tagSet.toList();
    tags.sort();
    return tags;
  }
}
