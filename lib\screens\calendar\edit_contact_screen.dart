import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'package:darvis_app/services/navigation_service.dart';
import 'package:darvis_app/services/data_service.dart';
import 'package:darvis_app/services/vcf_service.dart';
import 'package:darvis_app/services/device_contacts_service.dart';
import 'package:darvis_app/services/social_media_service.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

class EditContactScreen extends StatefulWidget {
  final Contact contact;

  const EditContactScreen({
    super.key,
    required this.contact,
  });

  @override
  State<EditContactScreen> createState() => _EditContactScreenState();
}

class _EditContactScreenState extends State<EditContactScreen> with TickerProviderStateMixin {
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _locationController = TextEditingController();
  final _notesController = TextEditingController();
  final _socialUsernameController = TextEditingController();
  
  String? _selectedImage;
  String _selectedSocialPlatform = 'Twitter';
  bool _isLoading = false;
  bool _showSocialDropdown = false;
  
  final List<String> _socialPlatforms = ['Twitter', 'Instagram', 'LinkedIn', 'WhatsApp'];
  
  late AnimationController _loadingController;
  
  // Services
  final VcfService _vcfService = VcfService();
  final DeviceContactsService _deviceContactsService = DeviceContactsService();
  final SocialMediaService _socialMediaService = SocialMediaService();

  @override
  void initState() {
    super.initState();
    
    _loadingController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    
    // Pre-populate fields with existing contact data
    _nameController.text = widget.contact.name;
    _phoneController.text = widget.contact.phone;
    _locationController.text = widget.contact.location;
    _notesController.text = widget.contact.memoryPrompt;

    // Handle social media data from new model structure
    if (widget.contact.socialMedia.isNotEmpty) {
      final firstPlatform = widget.contact.socialMedia.keys.first;
      final username = widget.contact.socialMedia.values.first;
      _selectedSocialPlatform = firstPlatform.substring(0, 1).toUpperCase() + firstPlatform.substring(1);
      _socialUsernameController.text = username;
    } else {
      _selectedSocialPlatform = 'Twitter';
    }
    _selectedImage = widget.contact.imagePath;
  }

  @override
  void dispose() {
    _loadingController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _locationController.dispose();
    _notesController.dispose();
    _socialUsernameController.dispose();
    super.dispose();
  }

  void _openCamera() async {
    final ImagePicker picker = ImagePicker();
    try {
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 800,
        maxHeight: 800,
      );

      if (image != null) {
        setState(() {
          _selectedImage = image.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to access camera. Please check permissions.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _clearForm() {
    _nameController.clear();
    _phoneController.clear();
    _locationController.clear();
    _notesController.clear();
    _socialUsernameController.clear();
    setState(() {
      _selectedImage = null;
      _selectedSocialPlatform = 'Twitter';
    });
  }

  Future<void> _updateContact() async {
    // Validation
    final errors = <String>[];
    if (_nameController.text.trim().isEmpty) {
      errors.add('Name is required');
    }
    if (_phoneController.text.trim().isEmpty) {
      errors.add('Phone number is required');
    }

    if (errors.isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.white),
              const SizedBox(width: DesignTokens.spacingSm),
              Expanded(
                child: Text(
                  errors.length == 1 
                    ? errors.first
                    : 'Please fix the following:\n• ${errors.join('\n• ')}',
                  style: DesignTokens.bodyStyle.copyWith(color: Colors.white),
                ),
              ),
            ],
          ),
          backgroundColor: DesignTokens.priorityHigh,
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }
    
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DesignTokens.backgroundCard,
        title: const Text('Update Contact?', style: DesignTokens.cardTitleStyle),
        content: Text(
          'Update ${_nameController.text}\'s information?',
          style: DesignTokens.bodyStyle,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel', style: DesignTokens.linkTextStyle),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Update', style: DesignTokens.linkTextStyle),
          ),
        ],
      ),
    );
    
    if (confirmed != true) return;
    
    setState(() {
      _isLoading = true;
    });
    
    _loadingController.repeat();
    
    try {
      // Prepare social media data
      final socialMedia = <String, String>{};
      if (_selectedSocialPlatform.isNotEmpty && _socialUsernameController.text.trim().isNotEmpty) {
        socialMedia[_selectedSocialPlatform.toLowerCase()] = _socialUsernameController.text.trim();
      }

      // Create updated contact with new model structure
      final updatedContact = Contact(
        id: widget.contact.id, // Keep same ID
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        email: widget.contact.email, // Keep existing email
        location: _locationController.text.trim(),
        metAt: _locationController.text.trim(), // Use location as "met at"
        socialMedia: socialMedia,
        memoryPrompt: _notesController.text.trim(),
        imagePath: _selectedImage,
        imagePublicId: widget.contact.imagePublicId, // Keep existing image public ID
        deviceSyncStatus: widget.contact.deviceSyncStatus, // Keep existing sync status
        createdAt: widget.contact.createdAt, // Keep original creation date
        updatedAt: DateTime.now(), // Update timestamp
      );

      // Update via data service (backend integrated)
      final result = await DataService().updateContact(updatedContact);

      if (result == null) {
        throw Exception('Failed to update contact');
      }
      
      _loadingController.stop();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Contact updated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        
        // Navigate back
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      _loadingController.stop();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update contact: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteContact() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DesignTokens.backgroundCard,
        title: const Text('Delete Contact?', style: DesignTokens.cardTitleStyle),
        content: Text(
          'Are you sure you want to delete ${widget.contact.name}? This action cannot be undone.',
          style: DesignTokens.bodyStyle,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel', style: DesignTokens.linkTextStyle),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text(
              'Delete', 
              style: TextStyle(color: DesignTokens.priorityHigh),
            ),
          ),
        ],
      ),
    );
    
    if (confirmed != true) return;
    
    try {
      // Delete via backend-integrated data service
      await DataService().removeContact(widget.contact.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Contact deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back to contact list
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete contact: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _Header(
              onBackTap: () => Navigator.of(context).pop(),
              onDeleteTap: _deleteContact,
            ),
            
            // Form Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
                child: Column(
                  children: [
                    const SizedBox(height: DesignTokens.spacingSm),

                    // Image Upload Section
                    _ImageUploadSection(
                      selectedImage: _selectedImage,
                      onTap: _openCamera,
                    ),

                    const SizedBox(height: DesignTokens.spacingMd),

                    // Form Fields
                    _FormField(
                      label: "What's your Name?",
                      controller: _nameController,
                      isRequired: true,
                    ),
                    const SizedBox(height: DesignTokens.spacingXs),

                    _FormField(
                      label: "Phone Number",
                      controller: _phoneController,
                      isRequired: true,
                      keyboardType: TextInputType.phone,
                    ),
                    const SizedBox(height: DesignTokens.spacingXs),

                    _FormField(
                      label: "Where did our paths cross today",
                      controller: _locationController,
                    ),
                    const SizedBox(height: DesignTokens.spacingXs),

                    _SocialMediaField(
                      controller: _socialUsernameController,
                      selectedPlatform: _selectedSocialPlatform,
                      platforms: _socialPlatforms,
                      showDropdown: _showSocialDropdown,
                      onPlatformChanged: (platform) {
                        setState(() {
                          _selectedSocialPlatform = platform;
                          _showSocialDropdown = false;
                        });
                      },
                      onDropdownToggle: () {
                        setState(() {
                          _showSocialDropdown = !_showSocialDropdown;
                        });
                      },
                    ),
                    const SizedBox(height: DesignTokens.spacingXs),

                    _ExpandableFormField(
                      label: "What should I remember you by?",
                      controller: _notesController,
                    ),

                    const SizedBox(height: DesignTokens.spacingLg),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: _SecondaryButton(
                            text: 'Clear All',
                            onTap: _clearForm,
                          ),
                        ),
                        const SizedBox(width: DesignTokens.spacingMd),
                        Expanded(
                          flex: 2,
                          child: _PrimaryButton(
                            text: 'Update Contact',
                            onTap: _updateContact,
                            isLoading: _isLoading,
                            loadingController: _loadingController,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: DesignTokens.spacingLg),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Header Component
class _Header extends StatelessWidget {
  final VoidCallback onBackTap;
  final VoidCallback onDeleteTap;

  const _Header({
    required this.onBackTap,
    required this.onDeleteTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: onBackTap,
            child: Container(
              padding: const EdgeInsets.all(DesignTokens.spacingSm),
              decoration: BoxDecoration(
                color: DesignTokens.backgroundCard,
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          const Text(
            'Edit Contact',
            style: DesignTokens.cardTitleStyle,
          ),
          GestureDetector(
            onTap: onDeleteTap,
            child: Container(
              padding: const EdgeInsets.all(DesignTokens.spacingSm),
              decoration: BoxDecoration(
                color: DesignTokens.priorityHigh,
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              ),
              child: const Icon(
                Icons.delete_outline,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Image Upload Section Component
class _ImageUploadSection extends StatelessWidget {
  final String? selectedImage;
  final VoidCallback onTap;

  const _ImageUploadSection({
    required this.selectedImage,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          color: DesignTokens.formFieldBackground,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          border: Border.all(
            color: DesignTokens.borderInteractiveElement,
            width: 2,
          ),
        ),
        child: selectedImage != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
              child: Image.file(
                File(selectedImage!),
                fit: BoxFit.cover,
              ),
            )
          : Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  'assets/icons/camera.svg',
                  width: 32,
                  height: 32,
                  colorFilter: const ColorFilter.mode(
                    DesignTokens.textMuted,
                    BlendMode.srcIn,
                  ),
                ),
                const SizedBox(height: DesignTokens.spacingSm),
                Text(
                  'Update Photo',
                  style: DesignTokens.formLabelStyle.copyWith(
                    color: DesignTokens.textMuted,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
      ),
    );
  }
}

// Form Field Component
class _FormField extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final bool isRequired;
  final TextInputType? keyboardType;

  const _FormField({
    required this.label,
    required this.controller,
    this.isRequired = false,
    this.keyboardType,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: label,
            style: DesignTokens.formLabelStyle,
            children: isRequired
              ? [
                  const TextSpan(
                    text: ' *',
                    style: TextStyle(color: DesignTokens.priorityHigh),
                  ),
                ]
              : null,
          ),
        ),
        const SizedBox(height: DesignTokens.spacingXs),
        Container(
          decoration: BoxDecoration(
            color: DesignTokens.formFieldBackground,
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            border: Border.all(
              color: DesignTokens.borderInteractiveElement,
              width: 1,
            ),
          ),
          child: TextField(
            controller: controller,
            keyboardType: keyboardType,
            style: DesignTokens.bodyStyle.copyWith(
              color: DesignTokens.textPrimary,
            ),
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(DesignTokens.spacingMd),
            ),
          ),
        ),
      ],
    );
  }
}

// Social Media Field Component
class _SocialMediaField extends StatelessWidget {
  final TextEditingController controller;
  final String selectedPlatform;
  final List<String> platforms;
  final bool showDropdown;
  final Function(String) onPlatformChanged;
  final VoidCallback onDropdownToggle;

  const _SocialMediaField({
    required this.controller,
    required this.selectedPlatform,
    required this.platforms,
    required this.showDropdown,
    required this.onPlatformChanged,
    required this.onDropdownToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Social Media',
          style: DesignTokens.formLabelStyle,
        ),
        const SizedBox(height: DesignTokens.spacingXs),
        Row(
          children: [
            // Platform Selector
            GestureDetector(
              onTap: onDropdownToggle,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: DesignTokens.spacingMd,
                  vertical: DesignTokens.spacingSm,
                ),
                decoration: BoxDecoration(
                  color: DesignTokens.formFieldBackground,
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                  border: Border.all(
                    color: DesignTokens.borderInteractiveElement,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      selectedPlatform,
                      style: DesignTokens.bodyStyle.copyWith(
                        color: DesignTokens.textPrimary,
                      ),
                    ),
                    const SizedBox(width: DesignTokens.spacingSm),
                    Icon(
                      showDropdown ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                      color: DesignTokens.textMuted,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: DesignTokens.spacingSm),
            // Username Field
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: DesignTokens.formFieldBackground,
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                  border: Border.all(
                    color: DesignTokens.borderInteractiveElement,
                    width: 1,
                  ),
                ),
                child: TextField(
                  controller: controller,
                  style: DesignTokens.bodyStyle.copyWith(
                    color: DesignTokens.textPrimary,
                  ),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.all(DesignTokens.spacingMd),
                    hintText: 'Username',
                  ),
                ),
              ),
            ),
          ],
        ),
        // Dropdown Menu
        if (showDropdown)
          Container(
            margin: const EdgeInsets.only(top: DesignTokens.spacingXs),
            decoration: BoxDecoration(
              color: DesignTokens.backgroundCard,
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              border: Border.all(
                color: DesignTokens.borderInteractiveElement,
                width: 1,
              ),
            ),
            child: Column(
              children: platforms.map((platform) {
                return GestureDetector(
                  onTap: () => onPlatformChanged(platform),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(DesignTokens.spacingMd),
                    decoration: BoxDecoration(
                      color: platform == selectedPlatform
                        ? DesignTokens.primaryInteractiveBlue.withOpacity(0.1)
                        : Colors.transparent,
                    ),
                    child: Text(
                      platform,
                      style: DesignTokens.bodyStyle.copyWith(
                        color: platform == selectedPlatform
                          ? DesignTokens.primaryInteractiveBlue
                          : DesignTokens.textPrimary,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
      ],
    );
  }
}

// Expandable Form Field Component
class _ExpandableFormField extends StatelessWidget {
  final String label;
  final TextEditingController controller;

  const _ExpandableFormField({
    required this.label,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: DesignTokens.formLabelStyle,
        ),
        const SizedBox(height: DesignTokens.spacingXs),
        Container(
          decoration: BoxDecoration(
            color: DesignTokens.formFieldBackground,
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            border: Border.all(
              color: DesignTokens.borderInteractiveElement,
              width: 1,
            ),
          ),
          child: TextField(
            controller: controller,
            maxLines: 4,
            style: DesignTokens.bodyStyle.copyWith(
              color: DesignTokens.textPrimary,
            ),
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(DesignTokens.spacingMd),
              hintText: 'Add notes about this contact...',
            ),
          ),
        ),
      ],
    );
  }
}

// Primary Button Component
class _PrimaryButton extends StatelessWidget {
  final String text;
  final VoidCallback onTap;
  final bool isLoading;
  final AnimationController loadingController;

  const _PrimaryButton({
    required this.text,
    required this.onTap,
    required this.isLoading,
    required this.loadingController,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isLoading ? null : onTap,
      child: Container(
        height: 56,
        decoration: BoxDecoration(
          color: isLoading
            ? DesignTokens.primaryInteractiveBlue.withOpacity(0.6)
            : DesignTokens.primaryInteractiveBlue,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        ),
        child: Center(
          child: isLoading
            ? RotationTransition(
                turns: loadingController,
                child: const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              )
            : Text(
                text,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
        ),
      ),
    );
  }
}

// Secondary Button Component
class _SecondaryButton extends StatelessWidget {
  final String text;
  final VoidCallback onTap;

  const _SecondaryButton({
    required this.text,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 56,
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          border: Border.all(
            color: DesignTokens.borderInteractiveElement,
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            text,
            style: DesignTokens.linkTextStyle,
          ),
        ),
      ),
    );
  }
}
