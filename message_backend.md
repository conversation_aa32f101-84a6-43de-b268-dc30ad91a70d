# Backend Requirements: Phase 3 Contact Management

## Overview
The frontend contact management features are 75% complete but require comprehensive backend API support for full functionality. This document outlines the specific backend requirements needed to complete Phase 3 implementation.

## Current Frontend State
- ✅ UI/UX: Complete for both Add Contact and View Contacts screens
- ✅ Local Data Management: Mock DataService with in-memory storage
- ✅ Navigation: Full integration with calendar screen
- ❌ Backend Integration: Currently using mock data, no real API calls
- ❌ Device Integration: VCF generation and local contacts saving mocked

## Required Backend APIs

### 1. Contact CRUD Operations

#### **POST /api/contacts** - Create Contact
```typescript
// Request
{
  "name": "string (required)",
  "phone": "string (required)",
  "email": "string? (optional)",
  "location": "string? (optional)",
  "socialPlatform": "string? (optional)",
  "socialUsername": "string? (optional)",
  "notes": "string? (optional)",
  "imageData": "base64? (optional)" // For image upload
}

// Response
{
  "success": true,
  "contact": {
    "id": "string",
    "userId": "string",
    "name": "string",
    "phone": "string",
    "email": "string?",
    "location": "string?",
    "socialPlatform": "string?",
    "socialUsername": "string?",
    "notes": "string?",
    "imageUrl": "string?",
    "createdAt": "DateTime",
    "updatedAt": "DateTime",
    "isSynced": true
  }
}
```

#### **GET /api/contacts** - List Contacts
```typescript
// Query Parameters
{
  "page": "number? (default: 1)",
  "limit": "number? (default: 20)",
  "search": "string? (optional search query)",
  "platform": "string? (optional platform filter)",
  "sortBy": "string? (default: 'createdAt')", // 'name', 'createdAt', 'location'
  "sortOrder": "string? (default: 'desc')" // 'asc', 'desc'
}

// Response
{
  "success": true,
  "contacts": [Contact[]],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8
  }
}
```

#### **GET /api/contacts/{id}** - Get Contact Details
```typescript
// Response
{
  "success": true,
  "contact": Contact
}
```

#### **PUT /api/contacts/{id}** - Update Contact
```typescript
// Request (same as create, all fields optional except id)
{
  "name": "string?",
  "phone": "string?",
  "email": "string?",
  "location": "string?",
  "socialPlatform": "string?",
  "socialUsername": "string?",
  "notes": "string?",
  "imageData": "base64?"
}

// Response
{
  "success": true,
  "contact": Contact
}
```

#### **DELETE /api/contacts/{id}** - Delete Contact
```typescript
// Response
{
  "success": true,
  "message": "Contact deleted successfully"
}
```

### 2. Image Management

#### **POST /api/contacts/{id}/image** - Upload Contact Image
```typescript
// Request: Multipart form data
{
  "image": "File (image file)",
  "compression": "string? (default: 'medium')" // 'low', 'medium', 'high'
}

// Response
{
  "success": true,
  "imageUrl": "string",
  "thumbnailUrl": "string?"
}
```

### 3. Advanced Search & Filtering

#### **GET /api/contacts/search** - Advanced Search
```typescript
// Query Parameters
{
  "query": "string? (full-text search)",
  "platforms": "string[]? (filter by social platforms)",
  "dateFrom": "DateTime? (filter by creation date)",
  "dateTo": "DateTime? (filter by creation date)",
  "hasImage": "boolean? (filter contacts with/without images)",
  "page": "number? (default: 1)",
  "limit": "number? (default: 20)"
}

// Response: Same as GET /api/contacts
```

### 4. Bulk Operations

#### **POST /api/contacts/bulk-delete** - Bulk Delete
```typescript
// Request
{
  "contactIds": "string[] (required)"
}

// Response
{
  "success": true,
  "deletedCount": 5,
  "failedIds": "string[]?" // If some deletions failed
}
```

#### **POST /api/contacts/export** - Export Contacts
```typescript
// Request
{
  "format": "string (required)", // 'csv', 'vcf', 'json'
  "contactIds": "string[]? (optional, all if not provided)",
  "includeImages": "boolean? (default: false)"
}

// Response
{
  "success": true,
  "downloadUrl": "string", // Temporary download URL
  "expiresAt": "DateTime"
}
```

## Data Models

### Contact Model
```typescript
interface Contact {
  id: string;
  userId: string;           // Associates contact with user
  name: string;
  phone: string;
  email?: string;
  location?: string;
  socialPlatform?: string;
  socialUsername?: string;
  notes?: string;
  imageUrl?: string;
  thumbnailUrl?: string;
  createdAt: DateTime;
  updatedAt: DateTime;
  isSynced: boolean;
  version: number;          // For conflict resolution
}
```

### Search/Filter Models
```typescript
interface ContactSearchRequest {
  query?: string;
  platforms?: string[];
  dateFrom?: DateTime;
  dateTo?: DateTime;
  hasImage?: boolean;
  page: number;
  limit: number;
  sortBy: 'name' | 'createdAt' | 'location' | 'updatedAt';
  sortOrder: 'asc' | 'desc';
}

interface ContactFilters {
  platforms: string[];
  dateRange: {
    from?: DateTime;
    to?: DateTime;
  };
  hasImage?: boolean;
}
```

## Technical Requirements

### Performance Requirements
- **Search Response Time:** <200ms for searches with <1000 contacts
- **List Loading:** <500ms for initial page load (20 contacts)
- **Image Upload:** <3 seconds for typical contact photo (<2MB)
- **Pagination:** Support for 10,000+ contacts per user
- **Concurrent Users:** Handle 1000+ concurrent users

### Search Implementation
- **Full-Text Search:** Implement efficient full-text search across name, location, notes, socialUsername
- **Platform Filtering:** Fast filtering by social media platform
- **Date Range Queries:** Efficient date range filtering
- **Pagination:** Cursor-based or offset-based pagination
- **Search Indexing:** Consider Elasticsearch or similar for large datasets

### Image Management
- **Storage:** Cloud storage (AWS S3, Google Cloud Storage, or Cloudinary)
- **Optimization:** Automatic image compression and resizing
- **Formats:** Support JPEG, PNG, WebP
- **Size Limits:** 5MB max upload, automatic compression to <500KB
- **Thumbnails:** Generate 100x100 and 300x300 thumbnails
- **CDN:** Global CDN for fast image delivery

### Security Requirements
- **User Isolation:** Contacts must be strictly user-specific
- **Access Control:** Users can only access their own contacts
- **Data Encryption:** Encrypt sensitive contact data at rest
- **API Authentication:** JWT-based authentication for all endpoints
- **Rate Limiting:** Implement rate limiting to prevent abuse
- **Audit Logging:** Log all contact creation, modification, deletion

### Data Validation
- **Phone Numbers:** Validate international phone number formats
- **Email Addresses:** Proper email format validation
- **Social Usernames:** Platform-specific username validation
- **Required Fields:** Enforce name and phone as required
- **Data Sanitization:** Sanitize all user inputs to prevent XSS

## Database Schema

### Contacts Table
```sql
CREATE TABLE contacts (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20) NOT NULL,
  email VARCHAR(255),
  location TEXT,
  social_platform VARCHAR(50),
  social_username VARCHAR(255),
  notes TEXT,
  image_url TEXT,
  thumbnail_url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_synced BOOLEAN DEFAULT TRUE,
  version INT DEFAULT 1,
  
  INDEX idx_user_id (user_id),
  INDEX idx_name (name),
  INDEX idx_phone (phone),
  INDEX idx_social_platform (social_platform),
  INDEX idx_created_at (created_at),
  FULLTEXT idx_search (name, location, notes, social_username)
);
```

## Integration Points

### Existing Systems
- **Authentication:** Integrate with existing Firebase Auth
- **User Management:** Use existing user ID system
- **API Framework:** Follow existing API patterns and error handling
- **Database:** Use existing Isar/SQLite for local storage sync

### Frontend Integration
- **API Client:** Extend existing ApiService for contact operations
- **Error Handling:** Use existing error handling patterns
- **Loading States:** Integrate with existing loading state management
- **Offline Support:** Implement offline-first with sync when online

## Implementation Priority

### Phase 1: Core CRUD (Week 1-2)
1. Contact creation, reading, updating, deletion
2. Basic search functionality
3. Image upload and storage
4. User isolation and security

### Phase 2: Advanced Features (Week 3-4)
1. Advanced search and filtering
2. Bulk operations
3. Export functionality
4. Performance optimization

### Phase 3: Polish & Scale (Week 5-6)
1. Search optimization for large datasets
2. Advanced image processing
3. Analytics and monitoring
4. Performance monitoring and alerting

## Testing Requirements

### Unit Tests
- Contact model validation
- Search query building
- Image processing logic
- Permission checking

### Integration Tests
- API endpoint functionality
- Database operations
- Image upload/download
- Search performance

### End-to-End Tests
- Complete contact workflows
- Cross-platform compatibility
- Offline/online synchronization
- Error scenario handling

## Monitoring & Analytics

### Key Metrics
- API response times
- Search query performance
- Image upload success rates
- User engagement with contact features
- Error rates by endpoint

### Logging Requirements
- All API requests and responses
- Search query patterns
- Image processing operations
- User permission interactions
- Synchronization events

## Deployment Considerations

### Database Migration
- Create contacts table with proper indexes
- Set up full-text search capabilities
- Configure image storage buckets
- Set up CDN for image delivery

### Environment Configuration
- Database connection settings
- Image storage credentials
- CDN configuration
- Search service configuration

### Rollback Strategy
- Database backup procedures
- API versioning strategy
- Feature flag implementation
- Gradual rollout plan

This backend implementation will enable the frontend to deliver the complete contact management experience specified in the functional plan, with full device integration and seamless user experience.</content>
<parameter name="filePath">c:\Users\<USER>\Documents\DAVID.DEV\darvis-app\message_backend.md
