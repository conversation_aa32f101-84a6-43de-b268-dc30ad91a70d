Target of URI doesn't exist: 'package:firebase_auth/firebase_auth.dart'.
Try creating the file referenced by the URI, or try using a URI for a file that does exist.
Undefined class 'FirebaseAuth'.
Try changing the name to the name of an existing class, or creating a class with the name 'FirebaseAuth'.
Undefined class 'FirebaseAuth'.
Try changing the name to the name of an existing class, or creating a class with the name 'FirebaseAuth'.
Undefined name 'FirebaseAuth'.
Try correcting the name to one that is defined, or defining the name.
The name 'User' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'User'.
Undefined class 'User'.
Try changing the name to the name of an existing class, or creating a class with the name 'User'.
The name 'UserCredential' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'UserCredential'.
The name 'FirebaseAuthException' isn't a type and can't be used in an on-catch clause.
Try correcting the name to match an existing class.
The name 'UserCredential' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'UserCredential'.
The name 'FirebaseAuthException' isn't a type and can't be used in an on-catch clause.
Try correcting the name to match an existing class.
Undefined class 'FirebaseAuthException'.
Try changing the name to the name of an existing class, or creating a class with the name 'FirebaseAuthException'.
Target of URI doesn't exist: 'package:firebase_core/firebase_core.dart'.
Try creating the file referenced by the URI, or try using a URI for a file that does exist.
Undefined name 'Firebase'.
Try correcting the name to one that is defined, or defining the name.
Target of URI doesn't exist: 'package:firebase_auth/firebase_auth.dart'.
Try creating the file referenced by the URI, or try using a URI for a file that does exist.
Undefined class 'User'.
Try changing the name to the name of an existing class, or creating a class with the name 'User'.
The name 'User' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'User'.
The name 'User' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'User'.
The name 'User' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'User'.
Undefined class 'User'.
Try changing the name to the name of an existing class, or creating a class with the name 'User'.
Undefined class 'User'.
Try changing the name to the name of an existing class, or creating a class with the name 'User'.
The name 'UserCredential' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'UserCredential'.
The name 'UserCredential' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'UserCredential'.
Classes and mixins can only implement other classes and mixins.
Try specifying a class or mixin, or remove the name from the list.
Undefined class 'UserMetadata'.
Try changing the name to the name of an existing class, or creating a class with the name 'UserMetadata'.
The name 'UserInfo' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'UserInfo'.
The name 'IdTokenResult' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'IdTokenResult'.
Undefined class 'PhoneAuthCredential'.
Try changing the name to the name of an existing class, or creating a class with the name 'PhoneAuthCredential'.
Undefined class 'ActionCodeSettings'.
Try changing the name to the name of an existing class, or creating a class with the name 'ActionCodeSettings'.
Undefined class 'ActionCodeSettings'.
Try changing the name to the name of an existing class, or creating a class with the name 'ActionCodeSettings'.
The name 'UserCredential' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'UserCredential'.
Undefined class 'AuthCredential'.
Try changing the name to the name of an existing class, or creating a class with the name 'AuthCredential'.
The name 'UserCredential' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'UserCredential'.
Undefined class 'AuthCredential'.
Try changing the name to the name of an existing class, or creating a class with the name 'AuthCredential'.
The name 'User' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'User'.
Undefined class 'MultiFactor'.
Try changing the name to the name of an existing class, or creating a class with the name 'MultiFactor'.
Classes and mixins can only implement other classes and mixins.
Try specifying a class or mixin, or remove the name from the list.
Undefined class 'User'.
Try changing the name to the name of an existing class, or creating a class with the name 'User'.
Undefined class 'AdditionalUserInfo'.
Try changing the name to the name of an existing class, or creating a class with the name 'AdditionalUserInfo'.
Undefined class 'AuthCredential'.
Try changing the name to the name of an existing class, or creating a class with the name 'AuthCredential'.
Classes and mixins can only implement other classes and mixins.
Try specifying a class or mixin, or remove the name from the list.
Classes and mixins can only implement other classes and mixins.
Try specifying a class or mixin, or remove the name from the list.
Classes and mixins can only implement other classes and mixins.
Try specifying a class or mixin, or remove the name from the list.
The name 'MultiFactorInfo' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'MultiFactorInfo'.
The name 'MultiFactorSession' isn't a type, so it can't be used as a type argument.
Try correcting the name to an existing type, or defining a type named 'MultiFactorSession'.
Undefined class 'MultiFactorAssertion'.
Try changing the name to the name of an existing class, or creating a class with the name 'MultiFactorAssertion'.
Undefined class 'MultiFactorSession'.
Try changing the name to the name of an existing class, or creating a class with the name 'MultiFactorSession'.
Undefined class 'MultiFactorInfo'.
Try changing the name to the name of an existing class, or creating a class with the name 'MultiFactorInfo'.