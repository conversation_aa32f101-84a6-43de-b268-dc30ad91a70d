import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import '../../utils/design_tokens.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with TickerProviderStateMixin {
  
  late AnimationController _pageAnimationController;
  late Animation<Offset> _pageSlideAnimation;
  
  // State variables
  bool _therapyReminders = true;
  bool _taskNotifications = true;
  bool _dailyCheckIns = false;
  bool _weeklyReports = true;
  bool _systemUpdates = false;
  
  TimeOfDay _quietHoursStart = const TimeOfDay(hour: 22, minute: 0);
  TimeOfDay _quietHoursEnd = const TimeOfDay(hour: 8, minute: 0);
  String _notificationFrequency = 'Normal';

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 350),
      vsync: this,
    );

    _pageSlideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _pageAnimationController,
      curve: Curves.easeOut,
    ));

    // Delay animation start until after page route transition completes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _pageAnimationController.forward();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: SafeArea(
        child: SlideTransition(
          position: _pageSlideAnimation,
          child: CustomScrollView(
            physics: const ClampingScrollPhysics(),
            slivers: [
              _buildHeader(),
              _buildNotificationTypesSection(),
              _buildQuietHoursSection(),
              _buildFrequencySection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return SliverToBoxAdapter(
      child: Container(
        height: 80,
        padding: const EdgeInsets.fromLTRB(
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingSm,
        ),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                Icons.arrow_back,
                color: DesignTokens.iconPrimary,
              ),
            ),
            Expanded(
              child: Center(
                child: ShaderMask(
                  shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
                  child: Text(
                    'Notifications',
                    style: DesignTokens.therapySessionTitleStyle.copyWith(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 48), // Balance the back button
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationTypesSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Types',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Therapy reminders toggle
            _buildToggleItem(
              'Therapy Reminders',
              'Get notified about scheduled therapy sessions',
              _therapyReminders,
              (value) {
                setState(() {
                  _therapyReminders = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
            
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Task notifications toggle
            _buildToggleItem(
              'Task Notifications',
              'Reminders for upcoming tasks and deadlines',
              _taskNotifications,
              (value) {
                setState(() {
                  _taskNotifications = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
            
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Daily check-ins toggle
            _buildToggleItem(
              'Daily Check-ins',
              'Daily wellness and mood check-in reminders',
              _dailyCheckIns,
              (value) {
                setState(() {
                  _dailyCheckIns = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
            
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Weekly reports toggle
            _buildToggleItem(
              'Weekly Reports',
              'Summary of your progress and insights',
              _weeklyReports,
              (value) {
                setState(() {
                  _weeklyReports = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
            
            const SizedBox(height: DesignTokens.spacingMd),
            
            // System updates toggle
            _buildToggleItem(
              'System Updates',
              'App updates and new feature announcements',
              _systemUpdates,
              (value) {
                setState(() {
                  _systemUpdates = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuietHoursSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quiet Hours',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Quiet hours time picker
            Container(
              padding: const EdgeInsets.all(DesignTokens.spacingMd),
              decoration: BoxDecoration(
                color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.08),
                  width: 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Do Not Disturb',
                        style: DesignTokens.cardTitleStyle.copyWith(fontSize: 16),
                      ),
                      const SizedBox(height: DesignTokens.spacingXs),
                      Text(
                        'No notifications during these hours',
                        style: DesignTokens.bodyStyle.copyWith(
                          color: DesignTokens.textSecondary,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: DesignTokens.spacingMd),
                      
                      Row(
                        children: [
                          Expanded(
                            child: _buildTimePickerButton(
                              'Start',
                              _quietHoursStart,
                              () => _selectTime(true),
                            ),
                          ),
                          const SizedBox(width: DesignTokens.spacingMd),
                          Expanded(
                            child: _buildTimePickerButton(
                              'End',
                              _quietHoursEnd,
                              () => _selectTime(false),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFrequencySection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Frequency',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Frequency dropdown
            _buildDropdownField(
              'Frequency Limit',
              'Control how often you receive notifications',
              _notificationFrequency,
              ['Minimal', 'Normal', 'Frequent'],
              (value) {
                setState(() {
                  _notificationFrequency = value!;
                });
              },
            ),
            
            const SizedBox(height: DesignTokens.spacingXl),
          ],
        ),
      ),
    );
  }

  Widget _buildTimePickerButton(String label, TimeOfDay time, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        decoration: BoxDecoration(
          color: DesignTokens.backgroundAccentCard,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        ),
        child: Column(
          children: [
            Text(
              label,
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textSecondary,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: DesignTokens.spacingXs),
            Text(
              time.format(context),
              style: DesignTokens.cardTitleStyle.copyWith(
                fontSize: 16,
                color: DesignTokens.textPrimary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectTime(bool isStart) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStart ? _quietHoursStart : _quietHoursEnd,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: DesignTokens.primaryInteractiveBlue,
              surface: DesignTokens.backgroundCard,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (picked != null) {
      setState(() {
        if (isStart) {
          _quietHoursStart = picked;
        } else {
          _quietHoursEnd = picked;
        }
      });
      HapticFeedback.mediumImpact();
    }
  }

  Widget _buildToggleItem(String title, String subtitle, bool value, Function(bool) onChanged) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: const EdgeInsets.all(DesignTokens.spacingSm),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: DesignTokens.cardTitleStyle.copyWith(fontSize: 16),
                      ),
                      const SizedBox(height: DesignTokens.spacingXs),
                      Text(
                        subtitle,
                        style: DesignTokens.bodyStyle.copyWith(
                          color: DesignTokens.textSecondary,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: value,
                  onChanged: onChanged,
                  activeColor: DesignTokens.primaryInteractiveBlue,
                  inactiveTrackColor: DesignTokens.navigationInactive,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownField(String title, String subtitle, String value, List<String> options, Function(String?) onChanged) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: DesignTokens.cardTitleStyle.copyWith(fontSize: 16),
              ),
              const SizedBox(height: DesignTokens.spacingXs),
              Text(
                subtitle,
                style: DesignTokens.bodyStyle.copyWith(
                  color: DesignTokens.textSecondary,
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: DesignTokens.spacingMd),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
                decoration: BoxDecoration(
                  color: DesignTokens.backgroundAccentCard,
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: value,
                    isExpanded: true,
                    icon: const Icon(Icons.keyboard_arrow_down, color: DesignTokens.iconPrimary),
                    style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
                    dropdownColor: DesignTokens.backgroundCard,
                    onChanged: onChanged,
                    items: options.map((String option) {
                      return DropdownMenuItem<String>(
                        value: option,
                        child: Text(option),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _pageAnimationController.dispose();
    super.dispose();
  }
}
