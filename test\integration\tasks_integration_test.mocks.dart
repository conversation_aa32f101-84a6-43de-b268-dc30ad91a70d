// Mocks generated by <PERSON><PERSON><PERSON> 5.4.4 from annotations
// in darvis_app/test/integration/tasks_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:darvis_app/blocs/tasks/tasks_bloc.dart' as _i3;
import 'package:darvis_app/blocs/tasks/tasks_event.dart' as _i5;
import 'package:darvis_app/blocs/tasks/tasks_state.dart' as _i2;
import 'package:darvis_app/services/navigation_service.dart' as _i7;
import 'package:flutter/material.dart' as _i8;
import 'package:flutter_bloc/flutter_bloc.dart' as _i6;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeTasksState_0 extends _i1.SmartFake implements _i2.TasksState {
  _FakeTasksState_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [TasksBloc].
///
/// See the documentation for Mockito's code generation for more information.
class MockTasksBloc extends _i1.Mock implements _i3.TasksBloc {
  MockTasksBloc() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.TasksState get state => (super.noSuchMethod(
        Invocation.getter(#state),
        returnValue: _FakeTasksState_0(
          this,
          Invocation.getter(#state),
        ),
      ) as _i2.TasksState);

  @override
  _i4.Stream<_i2.TasksState> get stream => (super.noSuchMethod(
        Invocation.getter(#stream),
        returnValue: _i4.Stream<_i2.TasksState>.empty(),
      ) as _i4.Stream<_i2.TasksState>);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
      ) as bool);

  @override
  void add(_i5.TasksEvent? event) => super.noSuchMethod(
        Invocation.method(
          #add,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onEvent(_i5.TasksEvent? event) => super.noSuchMethod(
        Invocation.method(
          #onEvent,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void emit(_i2.TasksState? state) => super.noSuchMethod(
        Invocation.method(
          #emit,
          [state],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void on<E extends _i5.TasksEvent>(
    _i6.EventHandler<E, _i2.TasksState>? handler, {
    _i6.EventTransformer<E>? transformer,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #on,
          [handler],
          {#transformer: transformer},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onTransition(
          _i6.Transition<_i5.TasksEvent, _i2.TasksState>? transition) =>
      super.noSuchMethod(
        Invocation.method(
          #onTransition,
          [transition],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  void onChange(_i6.Change<_i2.TasksState>? change) => super.noSuchMethod(
        Invocation.method(
          #onChange,
          [change],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addError(
    Object? error, [
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #addError,
          [
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onError(
    Object? error,
    StackTrace? stackTrace,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #onError,
          [
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i7.NavigationService {
  MockNavigationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  int get currentTab => (super.noSuchMethod(
        Invocation.getter(#currentTab),
        returnValue: 0,
      ) as int);

  @override
  void navigateToTab(int? tabIndex) => super.noSuchMethod(
        Invocation.method(
          #navigateToTab,
          [tabIndex],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateToScreen(
    _i8.Widget? screen, {
    bool? replace = false,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #navigateToScreen,
          [screen],
          {#replace: replace},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateBack() => super.noSuchMethod(
        Invocation.method(
          #navigateBack,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateToHome() => super.noSuchMethod(
        Invocation.method(
          #navigateToHome,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateToPersonalSpace() => super.noSuchMethod(
        Invocation.method(
          #navigateToPersonalSpace,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateToChat() => super.noSuchMethod(
        Invocation.method(
          #navigateToChat,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateToProductivityOverlay() => super.noSuchMethod(
        Invocation.method(
          #navigateToProductivityOverlay,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateToUpcomingEvents() => super.noSuchMethod(
        Invocation.method(
          #navigateToUpcomingEvents,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateToTherapyVoice() => super.noSuchMethod(
        Invocation.method(
          #navigateToTherapyVoice,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateToTherapyProgress() => super.noSuchMethod(
        Invocation.method(
          #navigateToTherapyProgress,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateToVoice() => super.noSuchMethod(
        Invocation.method(
          #navigateToVoice,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateToAddEvent() => super.noSuchMethod(
        Invocation.method(
          #navigateToAddEvent,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateToAddContact() => super.noSuchMethod(
        Invocation.method(
          #navigateToAddContact,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateToContactList() => super.noSuchMethod(
        Invocation.method(
          #navigateToContactList,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateToNotes() => super.noSuchMethod(
        Invocation.method(
          #navigateToNotes,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void navigateToTasks() => super.noSuchMethod(
        Invocation.method(
          #navigateToTasks,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<bool> onWillPop() => (super.noSuchMethod(
        Invocation.method(
          #onWillPop,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  void reset() => super.noSuchMethod(
        Invocation.method(
          #reset,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
