import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'package:darvis_app/utils/form_validators.dart';
import 'package:darvis_app/services/navigation_service.dart';
import 'package:darvis_app/blocs/auth/auth_bloc.dart';
import 'package:darvis_app/blocs/auth/auth_event.dart';
import 'package:darvis_app/blocs/auth/auth_state.dart';
import 'package:darvis_app/screens/auth/login_screen.dart';

// Reusable asset path constants
const String _kGoogleLogo = 'assets/icons/google_logo.svg';
const String _kEyeOnIcon = 'assets/icons/eye_on.svg';
const String _kEyeOffIcon = 'assets/icons/eye_off.svg';

/// SignUpScreen: A screen for new user registration.
/// Accepts callbacks for user actions to be handled by a parent BLoC.
class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  String? _nameError;
  String? _emailError;
  String? _passwordError;
  String? _confirmPasswordError;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _validateForm() {
    setState(() {
      _nameError = FormValidators.validateName(_nameController.text);
      _emailError = FormValidators.validateEmail(_emailController.text);
      _passwordError = FormValidators.validatePassword(_passwordController.text);
      _confirmPasswordError = FormValidators.validateConfirmPassword(
        _confirmPasswordController.text,
        _passwordController.text,
      );
    });
  }

  bool get _isFormValid {
    return _nameError == null &&
           _emailError == null &&
           _passwordError == null &&
           _confirmPasswordError == null &&
           _nameController.text.isNotEmpty &&
           _emailController.text.isNotEmpty &&
           _passwordController.text.isNotEmpty &&
           _confirmPasswordController.text.isNotEmpty;
  }

  void _handleSignUp() {
    _validateForm();
    if (_isFormValid) {
      context.read<AuthBloc>().add(
        AuthSignUpRequested(
          email: _emailController.text.trim(),
          password: _passwordController.text,
          confirmPassword: _confirmPasswordController.text,
          name: _nameController.text.trim(),
        ),
      );
    }
  }

  void _handleGoogleSignUp() {
    context.read<AuthBloc>().add(AuthSignUpWithGoogleRequested());
  }

  void _navigateToLogin() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const LoginScreen()),
    );
  }

  void _onNameChanged(String value) {
    if (_nameError != null) {
      setState(() {
        _nameError = null;
      });
    }
  }

  void _onEmailChanged(String value) {
    if (_emailError != null) {
      setState(() {
        _emailError = null;
      });
    }
  }

  void _onPasswordChanged(String value) {
    if (_passwordError != null) {
      setState(() {
        _passwordError = null;
      });
    }
  }

  void _onConfirmPasswordChanged(String value) {
    if (_confirmPasswordError != null) {
      setState(() {
        _confirmPasswordError = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthAuthenticated) {
          // Navigate to home screen on successful authentication
          GetIt.instance<NavigationService>().navigateToHome();
        } else if (state is AuthError) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: DesignTokens.backgroundApp,
        body: Stack(
        children: [
          // The background glow effect, positioned at the bottom.
          // Adjusted to be more subtle and compact as per feedback.
          const _BackgroundGlow(),

          // A LayoutBuilder ensures the form can scroll when the keyboard appears.
          LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: DesignTokens.spacingXl),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Reduced top spacing to pull content higher.
                        const SizedBox(height: DesignTokens.spacingXxl),

                        // Main "Sign Up" title with gradient text.
                        const _GradientTitle(text: 'Sign Up'),
                        // Reduced spacing.
                        const SizedBox(height: DesignTokens.spacingLg),

                        // Google sign-up button.
                        _SocialSignUpButton(
                          iconPath: _kGoogleLogo,
                          text: 'Google',
                          onTap: _handleGoogleSignUp,
                        ),
                        // Reduced spacing.
                        const SizedBox(height: DesignTokens.spacingXl),

                        // The main registration form.
                        _SignUpForm(
                          onSignUp: _handleSignUp,
                          onNavigateToLogin: _navigateToLogin,
                          nameController: _nameController,
                          emailController: _emailController,
                          passwordController: _passwordController,
                          confirmPasswordController: _confirmPasswordController,
                          nameError: _nameError,
                          emailError: _emailError,
                          passwordError: _passwordError,
                          confirmPasswordError: _confirmPasswordError,
                          onNameChanged: _onNameChanged,
                          onEmailChanged: _onEmailChanged,
                          onPasswordChanged: _onPasswordChanged,
                          onConfirmPasswordChanged: _onConfirmPasswordChanged,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
      ),
    );
  }
}

/// A specialized widget to render text with a gradient fill.
class _GradientTitle extends StatelessWidget {
  final String text;
  const _GradientTitle({required this.text});

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      blendMode: BlendMode.srcIn,
      shaderCallback: (bounds) =>
          DesignTokens.appNameHeaderGradient.createShader(
        Rect.fromLTWH(0, 0, bounds.width, bounds.height),
      ),
      child: Text(
        text,
        style: DesignTokens.appNameStyle.copyWith(fontSize: 50), // Increase as needed
      ),
    );
  }
}

/// A reusable button for social media sign-up options.
class _SocialSignUpButton extends StatelessWidget {
  final String iconPath;
  final String text;
  final VoidCallback onTap;

  const _SocialSignUpButton({
    required this.iconPath,
    required this.text,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 46,
        decoration: BoxDecoration(
          color: DesignTokens.googleButtonBackground,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(iconPath, height: 24, width: 24),
            const SizedBox(width: DesignTokens.spacingMd),
            Text(text, style: DesignTokens.googleButtonTextStyle),
          ],
        ),
      ),
    );
  }
}

/// The main form widget, containing all the text fields and buttons.
/// It is a StatefulWidget to manage the state of the password visibility.
class _SignUpForm extends StatefulWidget {
  final VoidCallback onSignUp;
  final VoidCallback onNavigateToLogin;
  final TextEditingController nameController;
  final TextEditingController emailController;
  final TextEditingController passwordController;
  final TextEditingController confirmPasswordController;
  final String? nameError;
  final String? emailError;
  final String? passwordError;
  final String? confirmPasswordError;
  final ValueChanged<String> onNameChanged;
  final ValueChanged<String> onEmailChanged;
  final ValueChanged<String> onPasswordChanged;
  final ValueChanged<String> onConfirmPasswordChanged;

  const _SignUpForm({
    required this.onSignUp,
    required this.onNavigateToLogin,
    required this.nameController,
    required this.emailController,
    required this.passwordController,
    required this.confirmPasswordController,
    required this.nameError,
    required this.emailError,
    required this.passwordError,
    required this.confirmPasswordError,
    required this.onNameChanged,
    required this.onEmailChanged,
    required this.onPasswordChanged,
    required this.onConfirmPasswordChanged,
  });

  @override
  State<_SignUpForm> createState() => _SignUpFormState();
}

class _SignUpFormState extends State<_SignUpForm> {
  bool _isPasswordObscured = true;
  bool _isConfirmPasswordObscured = true;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // First Name and Last Name fields in a row.
        Row(
          children: [
            Expanded(
              child: _TitledTextField(
                title: 'First Name',
                hintText: '',
              ),
            ),
            const SizedBox(width: DesignTokens.spacingMd),
            Expanded(
              child: _TitledTextField(
                title: 'Last Name',
                hintText: '',
              ),
            ),
          ],
        ),
        // Reduced spacing.
        const SizedBox(height: DesignTokens.spacingMd),

        // "What Should Darvis Call You" field.
        _TitledTextField(
          title: 'What Should Drix Call You',
          hintText: 'e.g., Elon Altman',
          controller: widget.nameController,
          errorText: widget.nameError,
          onChanged: widget.onNameChanged,
        ),
        // Reduced spacing.
        const SizedBox(height: DesignTokens.spacingMd),

        // Email Address field.
        _TitledTextField(
          title: 'Email Address',
          hintText: '<EMAIL>',
          keyboardType: TextInputType.emailAddress,
          controller: widget.emailController,
          errorText: widget.emailError,
          onChanged: widget.onEmailChanged,
        ),
        // Reduced spacing.
        const SizedBox(height: DesignTokens.spacingMd),

        // Password field with visibility toggle.
        _TitledTextField(
          title: 'Password',
          hintText: '••••••••',
          isObscured: _isPasswordObscured,
          controller: widget.passwordController,
          errorText: widget.passwordError,
          onChanged: widget.onPasswordChanged,
          suffixIcon: IconButton(
            icon: SvgPicture.asset(
              _isPasswordObscured ? _kEyeOffIcon : _kEyeOnIcon,
              colorFilter: const ColorFilter.mode(
                  DesignTokens.iconSecondary, BlendMode.srcIn),
            ),
            onPressed: () =>
                setState(() => _isPasswordObscured = !_isPasswordObscured),
          ),
        ),
        // Reduced spacing.
        const SizedBox(height: DesignTokens.spacingMd),

        // Confirm Password field with visibility toggle.
        _TitledTextField(
          title: 'Confirm Password',
          hintText: '••••••••',
          isObscured: _isConfirmPasswordObscured,
          controller: widget.confirmPasswordController,
          errorText: widget.confirmPasswordError,
          onChanged: widget.onConfirmPasswordChanged,
          suffixIcon: IconButton(
            icon: SvgPicture.asset(
              _isConfirmPasswordObscured ? _kEyeOffIcon : _kEyeOnIcon,
              colorFilter: const ColorFilter.mode(
                  DesignTokens.iconSecondary, BlendMode.srcIn),
            ),
            onPressed: () => setState(
                () => _isConfirmPasswordObscured = !_isConfirmPasswordObscured),
          ),
        ),
        // Reduced spacing.
        const SizedBox(height: DesignTokens.spacingLg),

        // Link to the login screen.
        _SignInLink(
          onTap: widget.onNavigateToLogin,
        ),
        // Reduced spacing.
        const SizedBox(height: DesignTokens.spacingLg),

        // Main "Sign Up" action button.
        BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            final isLoading = state is AuthLoading;
            return _PrimaryActionButton(
              text: isLoading ? 'Creating Account...' : 'Sign Up',
              onTap: isLoading ? () {} : widget.onSignUp,
            );
          },
        ),
        const SizedBox(height: DesignTokens.spacingXl),
      ],
    );
  }
}

/// A reusable widget combining a title label and a text input field.
class _TitledTextField extends StatelessWidget {
  final String title;
  final String hintText;
  final bool isObscured;
  final Widget? suffixIcon;
  final TextInputType? keyboardType;
  final TextEditingController? controller;
  final String? errorText;
  final ValueChanged<String>? onChanged;

  const _TitledTextField({
    required this.title,
    required this.hintText,
    this.isObscured = false,
    this.suffixIcon,
    this.keyboardType,
    this.controller,
    this.errorText,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingSm),
        TextField(
          controller: controller,
          obscureText: isObscured,
          keyboardType: keyboardType,
          onChanged: onChanged,
          style:
              DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
          decoration: InputDecoration(
            filled: true,
            fillColor: DesignTokens.formFieldBackground,
            hintText: hintText,
            hintStyle:
                DesignTokens.bodyStyle.copyWith(color: DesignTokens.textMuted),
            suffixIcon: suffixIcon,
            errorText: errorText,
            errorStyle: DesignTokens.bodyStyle.copyWith(
              color: Colors.red,
              fontSize: 12,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              borderSide: BorderSide.none,
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              borderSide: const BorderSide(color: Colors.red, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            // Reduced content padding for a more compact field.
            contentPadding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingSm, // 12px vertical padding
            ),
          ),
        ),
      ],
    );
  }
}

/// A reusable widget for the "Already have an account? Log in" text.
class _SignInLink extends StatelessWidget {
  final VoidCallback onTap;
  const _SignInLink({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Center(
        child: RichText(
        text: TextSpan(
          style: DesignTokens.bodyStyle,
          children: [
            const TextSpan(text: 'Already Have An Account? '),
            TextSpan(
              text: 'Log In.',
              style: DesignTokens.linkTextStyle
                  .copyWith(color: DesignTokens.link),
            ),
          ],
        ),
       ),
      ),
    );
  }
}

/// The main call-to-action button for the screen.
class _PrimaryActionButton extends StatelessWidget {
  final String text;
  final VoidCallback onTap;
  const _PrimaryActionButton({required this.text, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Center (
    child: GestureDetector(
      onTap: onTap,
      child: Container(
        width: 150,
        // Increased height for more prominence.
        height: 45,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: DesignTokens.primaryButtonBackground,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        ),
        child: Text(text, style: DesignTokens.primaryButtonTextStyle),
        ),
      ),
    );
  }
}

/// The decorative background glow effect at the bottom of the screen.
class _BackgroundGlow extends StatelessWidget {
  const _BackgroundGlow();

  @override
  Widget build(BuildContext context) {
    // This Align widget positions the glow at the bottom of its parent.
    return Align(
      alignment: Alignment.bottomCenter,
      // This Container's only job is to hold the shadow. It has no color or child.
      child: Container(
        // We define a large area for the glow to appear in.
        // These can be adjusted based on how far up the screen you want the glow to reach.
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.1,
        decoration: const BoxDecoration(
          // The boxShadow property is what creates the glow.
          boxShadow: [
            BoxShadow(
              // The color of your glow.
              color: Color.fromARGB(255, 9, 38, 107),
              // blurRadius controls the softness. Higher is softer.
              blurRadius: 150.0,
              // spreadRadius makes the glow bigger before blurring.
              spreadRadius: 50.0,
            ),
          ],
        ),
      ),
    );
  }
}
