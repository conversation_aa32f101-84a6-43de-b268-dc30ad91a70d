import 'package:flutter/material.dart';

class TaskCategory {
  final String id;
  final String name;
  final Color color;
  final String iconPath;

  const TaskCategory({
    required this.id,
    required this.name,
    required this.color,
    required this.iconPath,
  });

  TaskCategory copyWith({
    String? id,
    String? name,
    Color? color,
    String? iconPath,
  }) {
    return TaskCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      color: color ?? this.color,
      iconPath: iconPath ?? this.iconPath,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TaskCategory && 
      runtimeType == other.runtimeType && 
      id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TaskCategory{id: $id, name: $name}';
  }
}

// Predefined categories with design token colors
class TaskCategories {
  static const TaskCategory university = TaskCategory(
    id: 'university',
    name: 'University',
    color: Color(0xFF8B5CF6), // Purple
    iconPath: 'assets/icons/categories/category_school.svg',
  );

  static const TaskCategory food = TaskCategory(
    id: 'food',
    name: 'Food',
    color: Color(0xFF10B981), // Green
    iconPath: 'assets/icons/categories/category_food.svg',
  );

  static const TaskCategory exercise = TaskCategory(
    id: 'exercise',
    name: 'Exercise',
    color: Color(0xFFEF4444), // Red
    iconPath: 'assets/icons/categories/category_exercise.svg',
  );

  static const TaskCategory work = TaskCategory(
    id: 'work',
    name: 'Work',
    color: Color(0xFF3B82F6), // Blue
    iconPath: 'assets/icons/categories/category_work.svg',
  );

  static const TaskCategory family = TaskCategory(
    id: 'family',
    name: 'Family',
    color: Color(0xFFF59E0B), // Amber
    iconPath: 'assets/icons/categories/category_family.svg',
  );

  static const TaskCategory health = TaskCategory(
    id: 'health',
    name: 'Health',
    color: Color(0xFFEF4444), // Red
    iconPath: 'assets/icons/categories/category_health.svg',
  );

  static const TaskCategory home = TaskCategory(
    id: 'home',
    name: 'Home',
    color: Color(0xFF6B7280), // Gray
    iconPath: 'assets/icons/categories/category_home.svg',
  );

  static const TaskCategory movie = TaskCategory(
    id: 'movie',
    name: 'Movie',
    color: Color(0xFF8B5CF6), // Purple
    iconPath: 'assets/icons/categories/category_movie.svg',
  );

  static const TaskCategory music = TaskCategory(
    id: 'music',
    name: 'Music',
    color: Color(0xFF06B6D4), // Teal
    iconPath: 'assets/icons/categories/category_music.svg',
  );

  static const TaskCategory school = TaskCategory(
    id: 'school',
    name: 'School',
    color: Color(0xFF3B82F6), // Blue
    iconPath: 'assets/icons/categories/category_school.svg',
  );

  static const TaskCategory social = TaskCategory(
    id: 'social',
    name: 'Social',
    color: Color(0xFF10B981), // Green
    iconPath: 'assets/icons/categories/category_social.svg',
  );

  static const List<TaskCategory> all = [
    university,
    food,
    exercise,
    work,
    family,
    health,
    home,
    movie,
    music,
    school,
    social,
  ];

  static TaskCategory? findById(String id, [List<TaskCategory>? customCategories]) {
    try {
      // First check predefined categories
      return all.firstWhere((category) => category.id == id);
    } catch (e) {
      // Then check custom categories if provided
      if (customCategories != null) {
        try {
          return customCategories.firstWhere((category) => category.id == id);
        } catch (e) {
          return null;
        }
      }
      return null;
    }
  }

  static List<TaskCategory> getAllCategories([List<TaskCategory>? customCategories]) {
    final allCategories = List<TaskCategory>.from(all);
    if (customCategories != null) {
      allCategories.addAll(customCategories);
    }
    return allCategories;
  }

  static TaskCategory get defaultCategory => university;
}
