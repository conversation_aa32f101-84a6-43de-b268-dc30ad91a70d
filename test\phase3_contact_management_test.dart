import 'package:flutter_test/flutter_test.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:darvis_app/services/vcf_service.dart';
import 'package:darvis_app/services/social_media_service.dart';
import 'package:darvis_app/services/contact_permission_service.dart';
import 'package:darvis_app/services/data_service.dart';

void main() {
  group('Phase 3: Contact Management Tests', () {
    late VcfService vcfService;
    late SocialMediaService socialMediaService;
    late ContactPermissionService permissionService;
    late Contact testContact;

    setUp(() {
      vcfService = VcfService();
      socialMediaService = SocialMediaService();
      permissionService = ContactPermissionService();

      testContact = Contact(
        id: 'test-123',
        name: '<PERSON>',
        phone: '+1234567890',
        email: '<EMAIL>',
        location: 'Coffee Shop Downtown',
        metAt: 'Coffee Shop Downtown',
        socialMedia: {'twitter': 'johndoe'},
        memoryPrompt: 'Met at networking event',
        createdAt: DateTime.now(),
      );
    });

    group('VCF Service Tests', () {
      test('should generate valid VCF content', () async {
        // Act
        final vcfContent = await vcfService.generateVcfContent(testContact);

        // Assert
        expect(vcfContent, isNotEmpty);
        expect(vcfContent, contains('BEGIN:VCARD'));
        expect(vcfContent, contains('END:VCARD'));
        expect(vcfContent, contains('FN:John Doe'));
        expect(vcfContent, contains('TEL;TYPE=CELL:+1234567890'));
        expect(vcfContent, contains('EMAIL;TYPE=HOME:<EMAIL>'));
        expect(vcfContent, contains('ORG:Met at: Coffee Shop Downtown'));
        expect(vcfContent, contains('URL:https://twitter.com/johndoe'));
        expect(vcfContent, contains('NOTE:Met at networking event'));
      });

      test('should handle contact with minimal information', () async {
        // Arrange
        final minimalContact = Contact(
          id: 'minimal-123',
          name: 'Jane Smith',
          phone: '+9876543210',
          email: '',
          location: '',
          metAt: '',
          socialMedia: {},
          memoryPrompt: '',
          createdAt: DateTime.now(),
        );

        // Act
        final vcfContent = await vcfService.generateVcfContent(minimalContact);
        
        // Assert
        expect(vcfContent, isNotEmpty);
        expect(vcfContent, contains('FN:Jane Smith'));
        expect(vcfContent, contains('TEL;TYPE=CELL:+9876543210'));
        expect(vcfContent, isNot(contains('EMAIL:')));
        expect(vcfContent, isNot(contains('ORG:')));
        expect(vcfContent, isNot(contains('URL:')));
        expect(vcfContent, isNot(contains('NOTE:')));
      });

      test('should validate VCF content correctly', () async {
        // Arrange
        final validVcf = await vcfService.generateVcfContent(testContact);
        const invalidVcf = 'This is not a valid VCF';

        // Act & Assert
        expect(vcfService.validateVcfContent(validVcf), isTrue);
        expect(vcfService.validateVcfContent(invalidVcf), isFalse);
      });

      test('should generate social media URLs correctly', () async {
        // Test different platforms
        final platforms = [
          {'platform': 'Twitter', 'username': 'johndoe', 'expected': 'https://twitter.com/johndoe'},
          {'platform': 'Instagram', 'username': 'johndoe', 'expected': 'https://instagram.com/johndoe'},
          {'platform': 'LinkedIn', 'username': 'johndoe', 'expected': 'https://linkedin.com/in/johndoe'},
          {'platform': 'WhatsApp', 'username': '+1234567890', 'expected': 'https://wa.me/+1234567890'},
        ];

        for (final platform in platforms) {
          final contact = Contact(
            id: 'test-${platform['platform']}',
            name: 'Test User',
            phone: '+1234567890',
            email: '',
            location: '',
            metAt: '',
            socialMedia: {platform['platform']!.toLowerCase(): platform['username']!},
            memoryPrompt: '',
            createdAt: DateTime.now(),
          );

          final vcfContent = await vcfService.generateVcfContent(contact);
          expect(vcfContent, contains('URL:${platform['expected']}'));
        }
      });
    });

    group('Social Media Service Tests', () {
      test('should validate usernames correctly', () {
        // Twitter validation
        expect(socialMediaService.isValidUsername('twitter', 'johndoe'), isTrue);
        expect(socialMediaService.isValidUsername('twitter', 'john_doe123'), isTrue);
        expect(socialMediaService.isValidUsername('twitter', 'toolongusernamethatexceedslimit'), isFalse);
        expect(socialMediaService.isValidUsername('twitter', 'john-doe'), isFalse); // No hyphens in Twitter

        // Instagram validation
        expect(socialMediaService.isValidUsername('instagram', 'john.doe'), isTrue);
        expect(socialMediaService.isValidUsername('instagram', 'john_doe123'), isTrue);
        expect(socialMediaService.isValidUsername('instagram', 'toolongusernamethatexceedslimitfortesting'), isFalse);

        // LinkedIn validation
        expect(socialMediaService.isValidUsername('linkedin', 'john-doe'), isTrue);
        expect(socialMediaService.isValidUsername('linkedin', 'johndoe123'), isTrue);
        expect(socialMediaService.isValidUsername('linkedin', 'john_doe'), isFalse); // No underscores in LinkedIn

        // WhatsApp validation (phone numbers)
        expect(socialMediaService.isValidUsername('whatsapp', '+1234567890'), isTrue);
        expect(socialMediaService.isValidUsername('whatsapp', '1234567890'), isTrue);
        expect(socialMediaService.isValidUsername('whatsapp', 'notaphonenumber'), isFalse);
      });

      test('should generate correct platform URLs', () {
        expect(socialMediaService.generateShareableLink('twitter', 'johndoe'),
               equals('https://twitter.com/johndoe'));
        expect(socialMediaService.generateShareableLink('instagram', 'johndoe'),
               equals('https://instagram.com/johndoe'));
        expect(socialMediaService.generateShareableLink('linkedin', 'johndoe'),
               equals('https://linkedin.com/in/johndoe'));
        expect(socialMediaService.generateShareableLink('whatsapp', '+1234567890'),
               equals('https://wa.me/+1234567890'));
      });

      test('should provide correct username placeholders', () {
        expect(socialMediaService.getUsernamePlaceholder('twitter'), equals('@username'));
        expect(socialMediaService.getUsernamePlaceholder('instagram'), equals('@username'));
        expect(socialMediaService.getUsernamePlaceholder('linkedin'), equals('profile-name'));
        expect(socialMediaService.getUsernamePlaceholder('whatsapp'), equals('+1234567890'));
      });

      test('should return supported platforms', () {
        final platforms = socialMediaService.getSupportedPlatforms();
        expect(platforms, contains('twitter'));
        expect(platforms, contains('instagram'));
        expect(platforms, contains('linkedin'));
        expect(platforms, contains('whatsapp'));
        expect(platforms.length, equals(4));
      });
    });

    group('Contact Permission Service Tests', () {
      test('should provide user-friendly permission messages', () {
        expect(permissionService.getPermissionStatusMessage(PermissionStatus.granted),
               equals('Permission granted'));
        expect(permissionService.getPermissionStatusMessage(PermissionStatus.denied),
               contains('Permission denied'));
        expect(permissionService.getPermissionStatusMessage(PermissionStatus.permanentlyDenied),
               contains('permanently denied'));
      });

      test('should check if permission is permanently denied', () {
        expect(permissionService.isPermissionPermanentlyDenied(PermissionStatus.permanentlyDenied), isTrue);
        expect(permissionService.isPermissionPermanentlyDenied(PermissionStatus.granted), isFalse);
        expect(permissionService.isPermissionPermanentlyDenied(PermissionStatus.denied), isFalse);
      });
    });

    group('Integration Tests', () {
      test('should handle contact update workflow', () async {
        // Arrange
        final dataService = DataService();
        await dataService.addContact(testContact);

        final updatedContact = Contact(
          id: testContact.id,
          name: 'John Updated',
          phone: testContact.phone,
          email: testContact.email,
          location: 'New Location',
          metAt: 'New Location',
          socialMedia: testContact.socialMedia,
          memoryPrompt: 'Updated notes',
          createdAt: testContact.createdAt,
        );

        // Act
        await dataService.updateContact(updatedContact);
        final retrievedContact = await dataService.getContactById(testContact.id);

        // Assert
        expect(retrievedContact, isNotNull);
        expect(retrievedContact!.name, equals('John Updated'));
        expect(retrievedContact.location, equals('New Location'));
        expect(retrievedContact.memoryPrompt, equals('Updated notes'));
      });

      test('should handle contact deletion workflow', () async {
        // Arrange
        final dataService = DataService();
        await dataService.addContact(testContact);
        expect(dataService.contacts, contains(testContact));

        // Act
        dataService.removeContact(testContact.id);

        // Assert
        expect(dataService.contacts, isNot(contains(testContact)));
        expect(dataService.getContactById(testContact.id), isNull);
      });

      test('should generate VCF and manage contacts together', () async {
        // Arrange
        final dataService = DataService();

        // Act
        await dataService.addContact(testContact);
        final vcfContent = await vcfService.generateVcfContent(testContact);

        // Assert
        expect(dataService.contacts, contains(testContact));
        expect(vcfContent, isNotEmpty);
        expect(vcfContent, contains('FN:John Doe'));
        expect(vcfContent, contains('TEL;TYPE=CELL:+1234567890'));
      });
    });
  });
}
