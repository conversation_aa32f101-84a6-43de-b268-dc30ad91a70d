import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../../utils/design_tokens.dart';

/// Comprehensive error handling for Smart Capture functionality
class SmartCaptureErrorHandler {
  
  /// Handle API errors and return user-friendly messages
  static String handleApiError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return 'Connection timeout. Please check your internet connection and try again.';
          
        case DioExceptionType.connectionError:
          return 'Unable to connect to server. Please check your internet connection.';
          
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          switch (statusCode) {
            case 400:
              return 'Invalid request. Please check the content and try again.';
            case 401:
              return 'Authentication failed. Please log in again.';
            case 403:
              return 'Access denied. You don\'t have permission to perform this action.';
            case 404:
              return 'Content not found. It may have been deleted or moved.';
            case 409:
              return 'Content already exists or conflicts with existing data.';
            case 413:
              return 'Content is too large. Please try with smaller content.';
            case 429:
              return 'Too many requests. Please wait a moment and try again.';
            case 500:
              return 'Server error. Please try again later.';
            case 502:
            case 503:
            case 504:
              return 'Service temporarily unavailable. Please try again later.';
            default:
              return 'Server error (${statusCode ?? 'Unknown'}). Please try again.';
          }
          
        case DioExceptionType.cancel:
          return 'Request was cancelled.';
          
        case DioExceptionType.unknown:
        default:
          return 'Network error. Please check your connection and try again.';
      }
    }
    
    // Handle other types of errors
    if (error.toString().toLowerCase().contains('timeout')) {
      return 'Operation timed out. Please try again.';
    }
    
    if (error.toString().toLowerCase().contains('network')) {
      return 'Network error. Please check your connection.';
    }
    
    return 'An unexpected error occurred. Please try again.';
  }

  /// Handle content processing errors
  static String handleContentProcessingError(dynamic error, String contentType) {
    final errorMessage = error.toString().toLowerCase();
    
    if (errorMessage.contains('invalid url') || errorMessage.contains('malformed')) {
      return 'Invalid URL format. Please check the URL and try again.';
    }
    
    if (errorMessage.contains('not found') || errorMessage.contains('404')) {
      return 'Content not found. The URL may be broken or the content may have been removed.';
    }
    
    if (errorMessage.contains('access denied') || errorMessage.contains('forbidden')) {
      return 'Access denied. The content may be private or require authentication.';
    }
    
    if (errorMessage.contains('too large') || errorMessage.contains('size')) {
      return 'Content is too large to process. Please try with smaller content.';
    }
    
    if (errorMessage.contains('unsupported') || errorMessage.contains('format')) {
      return 'Unsupported content format. Please try with a different type of content.';
    }
    
    if (errorMessage.contains('rate limit') || errorMessage.contains('quota')) {
      return 'Processing limit reached. Please wait a moment and try again.';
    }
    
    switch (contentType.toLowerCase()) {
      case 'image':
        return 'Failed to process image. Please check the image format and try again.';
      case 'url':
      case 'link':
        return 'Failed to process URL. Please check the link and try again.';
      default:
        return 'Failed to process content. Please try again.';
    }
  }

  /// Handle share intent errors
  static String handleShareIntentError(dynamic error) {
    final errorMessage = error.toString().toLowerCase();
    
    if (errorMessage.contains('permission')) {
      return 'Permission denied. Please allow the app to receive shared content.';
    }
    
    if (errorMessage.contains('format') || errorMessage.contains('type')) {
      return 'Unsupported content format. Please share text or image content.';
    }
    
    if (errorMessage.contains('empty') || errorMessage.contains('null')) {
      return 'No content received. Please try sharing again.';
    }
    
    return 'Failed to receive shared content. Please try again.';
  }

  /// Handle authentication errors
  static String handleAuthError(dynamic error) {
    final errorMessage = error.toString().toLowerCase();
    
    if (errorMessage.contains('token') || errorMessage.contains('expired')) {
      return 'Session expired. Please log in again.';
    }
    
    if (errorMessage.contains('unauthorized') || errorMessage.contains('401')) {
      return 'Authentication failed. Please log in again.';
    }
    
    if (errorMessage.contains('forbidden') || errorMessage.contains('403')) {
      return 'Access denied. Please check your permissions.';
    }
    
    return 'Authentication error. Please log in again.';
  }

  /// Show error snackbar with consistent styling
  static void showErrorSnackBar(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration duration = const Duration(seconds: 4),
  }) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: DesignTokens.spacingSm),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: DesignTokens.priorityHigh,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        action: actionLabel != null && onAction != null
            ? SnackBarAction(
                label: actionLabel,
                textColor: Colors.white,
                onPressed: onAction,
              )
            : null,
      ),
    );
  }

  /// Show success snackbar with consistent styling
  static void showSuccessSnackBar(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration duration = const Duration(seconds: 3),
  }) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: DesignTokens.spacingSm),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
        duration: duration,
        behavior: SnackBarBehavior.floating,
        action: actionLabel != null && onAction != null
            ? SnackBarAction(
                label: actionLabel,
                textColor: Colors.white,
                onPressed: onAction,
              )
            : null,
      ),
    );
  }

  /// Show info snackbar with consistent styling
  static void showInfoSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info_outline, color: Colors.white),
            const SizedBox(width: DesignTokens.spacingSm),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.9),
        duration: duration,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Check if error is recoverable (user can retry)
  static bool isRecoverableError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
        case DioExceptionType.connectionError:
          return true;
          
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          return statusCode == 429 || // Rate limit
                 statusCode == 500 || // Server error
                 statusCode == 502 || // Bad gateway
                 statusCode == 503 || // Service unavailable
                 statusCode == 504;   // Gateway timeout
          
        default:
          return false;
      }
    }
    
    final errorMessage = error.toString().toLowerCase();
    return errorMessage.contains('timeout') ||
           errorMessage.contains('network') ||
           errorMessage.contains('connection');
  }

  /// Get retry delay based on error type
  static Duration getRetryDelay(dynamic error, int attemptNumber) {
    // Exponential backoff with jitter
    final baseDelay = Duration(seconds: 2 * attemptNumber);
    final jitter = Duration(milliseconds: (attemptNumber * 500));
    
    if (error is DioException && error.response?.statusCode == 429) {
      // Rate limit - longer delay
      return Duration(seconds: 10 * attemptNumber) + jitter;
    }
    
    return baseDelay + jitter;
  }
}
