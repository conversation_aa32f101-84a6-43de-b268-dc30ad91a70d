import 'dart:io';
import 'package:isar/isar.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import '../models/profile_models.dart';
import 'cloudinary_service.dart';
import 'api_service.dart';

/// Profile management service with Cloudinary integration
class ProfileService {
  final Isar _isar;
  final CloudinaryService _cloudinaryService;
  final ApiService _apiService;
  final ImagePicker _imagePicker;
  
  ProfileService({
    required Isar isar,
    required CloudinaryService cloudinaryService,
    required ApiService apiService,
    required ImagePicker imagePicker,
  }) : _isar = isar,
       _cloudinaryService = cloudinaryService,
       _apiService = apiService,
       _imagePicker = imagePicker;

  /// Get user profile from local storage
  Future<UserProfile?> getUserProfile(String userId) async {
    try {
      return await _isar.userProfiles
          .where()
          .userIdEqualTo(userId)
          .findFirst();
    } catch (e) {
      print('❌ Failed to get user profile: $e');
      return null;
    }
  }

  /// Create or update user profile
  Future<UserProfile> updateUserProfile(UserProfile profile) async {
    try {
      profile.updatedAt = DateTime.now();
      profile.needsSync = true;
      profile.syncStatus = 'pending';
      
      await _isar.writeTxn(() async {
        await _isar.userProfiles.put(profile);
      });
      
      // Sync with backend
      await _syncProfileToBackend(profile);
      
      print('✅ User profile updated successfully');
      return profile;
    } catch (e) {
      print('❌ Failed to update user profile: $e');
      throw Exception('Failed to update user profile: $e');
    }
  }

  /// Change profile picture with Cloudinary upload
  Future<String?> changeProfilePicture({
    required String userId,
    required ImageSource source,
  }) async {
    try {
      print('🔄 Starting profile picture change for user: $userId');
      
      // Pick image
      final pickedFile = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );
      
      if (pickedFile == null) {
        print('⚠️ No image selected');
        return null;
      }
      
      final imageFile = File(pickedFile.path);
      
      // Validate image
      if (!_cloudinaryService.validateImageFile(imageFile)) {
        throw Exception('Invalid image file');
      }
      
      // Get current profile
      final profile = await getUserProfile(userId);
      final previousPublicId = profile?.profilePicturePublicId;
      
      // Upload to Cloudinary
      final uploadResponse = await _cloudinaryService.uploadProfilePicture(
        imageFile: imageFile,
        userId: userId,
        previousPublicId: previousPublicId,
      );
      
      // Cache image locally
      final localPath = await _cacheImageLocally(uploadResponse.secureUrl, userId);
      
      // Update profile
      final updatedProfile = profile ?? UserProfile()
        ..userId = userId
        ..displayName = profile?.displayName ?? 'User'
        ..email = profile?.email ?? ''
        ..createdAt = profile?.createdAt ?? DateTime.now();
      
      updatedProfile.profilePictureUrl = uploadResponse.secureUrl;
      updatedProfile.profilePicturePublicId = uploadResponse.publicId;
      updatedProfile.localProfilePicturePath = localPath;
      
      await updateUserProfile(updatedProfile);
      
      // Save to history
      await _saveProfilePictureHistory(userId, uploadResponse, localPath, source.name);
      
      // Notify backend about profile picture change
      await _notifyBackendProfilePictureChange(userId, uploadResponse, previousPublicId);
      
      print('✅ Profile picture changed successfully');
      return uploadResponse.secureUrl;
    } catch (e) {
      print('❌ Failed to change profile picture: $e');
      throw Exception('Failed to change profile picture: $e');
    }
  }

  /// Get profile picture URL with transformations
  String? getProfilePictureUrl({
    required String? publicId,
    int width = 400,
    int height = 400,
    bool enableFaceDetection = true,
  }) {
    if (publicId == null || publicId.isEmpty) {
      return null;
    }
    
    return _cloudinaryService.generateProfilePictureUrl(
      publicId: publicId,
      width: width,
      height: height,
      enableFaceDetection: enableFaceDetection,
    );
  }

  /// Get thumbnail URL for profile picture
  String? getThumbnailUrl({
    required String? publicId,
    int size = 100,
  }) {
    if (publicId == null || publicId.isEmpty) {
      return null;
    }
    
    return _cloudinaryService.generateThumbnailUrl(
      publicId: publicId,
      size: size,
    );
  }

  /// Get responsive URLs for different screen densities
  Map<String, String>? getResponsiveUrls({
    required String? publicId,
    int baseWidth = 400,
    int baseHeight = 400,
  }) {
    if (publicId == null || publicId.isEmpty) {
      return null;
    }
    
    return _cloudinaryService.getResponsiveUrls(
      publicId: publicId,
      baseWidth: baseWidth,
      baseHeight: baseHeight,
    );
  }

  /// Delete profile picture
  Future<bool> deleteProfilePicture(String userId) async {
    try {
      final profile = await getUserProfile(userId);
      if (profile?.profilePicturePublicId == null) {
        return true; // Nothing to delete
      }
      
      // Delete from Cloudinary
      final deleted = await _cloudinaryService.deleteProfilePicture(
        profile!.profilePicturePublicId!,
      );
      
      if (deleted) {
        // Update profile
        profile.profilePictureUrl = null;
        profile.profilePicturePublicId = null;
        profile.localProfilePicturePath = null;
        
        await updateUserProfile(profile);
        
        // Notify backend
        await _notifyBackendProfilePictureChange(userId, null, profile.profilePicturePublicId);
        
        print('✅ Profile picture deleted successfully');
        return true;
      }
      
      return false;
    } catch (e) {
      print('❌ Failed to delete profile picture: $e');
      return false;
    }
  }

  /// Get profile picture history
  Future<List<ProfilePictureHistory>> getProfilePictureHistory(String userId) async {
    try {
      return await _isar.profilePictureHistorys
          .where()
          .userIdEqualTo(userId)
          .sortByUploadedAtDesc()
          .findAll();
    } catch (e) {
      print('❌ Failed to get profile picture history: $e');
      return [];
    }
  }

  /// Cache image locally for offline access
  Future<String?> _cacheImageLocally(String imageUrl, String userId) async {
    try {
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode == 200) {
        final directory = await getApplicationDocumentsDirectory();
        final fileName = 'profile_${userId}_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final file = File('${directory.path}/$fileName');
        
        await file.writeAsBytes(response.bodyBytes);
        return file.path;
      }
      return null;
    } catch (e) {
      print('⚠️ Failed to cache image locally: $e');
      return null;
    }
  }

  /// Save profile picture to history
  Future<void> _saveProfilePictureHistory(
    String userId,
    CloudinaryUploadResponse uploadResponse,
    String? localPath,
    String source,
  ) async {
    try {
      // Mark previous pictures as inactive
      final previousPictures = await _isar.profilePictureHistorys
          .where()
          .userIdEqualTo(userId)
          .findAll();
      
      for (final picture in previousPictures) {
        picture.isActive = false;
      }
      
      await _isar.writeTxn(() async {
        await _isar.profilePictureHistorys.putAll(previousPictures);
      });
      
      // Add new picture to history
      final history = ProfilePictureHistory()
        ..userId = userId
        ..cloudinaryUrl = uploadResponse.secureUrl
        ..cloudinaryPublicId = uploadResponse.publicId
        ..localPath = localPath
        ..isActive = true
        ..uploadedAt = uploadResponse.createdAt
        ..uploadSource = source
        ..needsSync = true;
      
      await _isar.writeTxn(() async {
        await _isar.profilePictureHistorys.put(history);
      });
    } catch (e) {
      print('❌ Failed to save profile picture history: $e');
    }
  }

  /// Sync profile to backend
  Future<void> _syncProfileToBackend(UserProfile profile) async {
    try {
      final dto = UserProfileDto(
        userId: profile.userId,
        displayName: profile.displayName,
        email: profile.email,
        darvisName: profile.darvisName,
        profilePictureUrl: profile.profilePictureUrl,
        profilePicturePublicId: profile.profilePicturePublicId,
        memberSince: profile.memberSince,
        currentStreak: profile.currentStreak,
        lastActivityDate: profile.lastActivityDate,
        subscriptionPlan: profile.subscriptionPlan,
        subscriptionExpiryDate: profile.subscriptionExpiryDate,
        isSubscriptionActive: profile.isSubscriptionActive,
        timeZone: profile.timeZone,
        language: profile.language,
        theme: profile.theme,
        biometricAuthEnabled: profile.biometricAuthEnabled,
        sessionTimeout: profile.sessionTimeout,
        appLockRequired: profile.appLockRequired,
        cloudSyncEnabled: profile.cloudSyncEnabled,
        autoBackupEnabled: profile.autoBackupEnabled,
        lastBackupDate: profile.lastBackupDate,
      );
      
      await _apiService.updateUserProfile(dto);
      
      profile.syncStatus = 'synced';
      profile.needsSync = false;
      
      await _isar.writeTxn(() async {
        await _isar.userProfiles.put(profile);
      });
    } catch (e) {
      print('❌ Failed to sync profile to backend: $e');
      profile.syncStatus = 'error';
      await _isar.writeTxn(() async {
        await _isar.userProfiles.put(profile);
      });
    }
  }

  /// Notify backend about profile picture change
  Future<void> _notifyBackendProfilePictureChange(
    String userId,
    CloudinaryUploadResponse? uploadResponse,
    String? previousPublicId,
  ) async {
    try {
      final request = ProfilePictureUpdateRequest(
        userId: userId,
        cloudinaryUrl: uploadResponse?.secureUrl ?? '',
        cloudinaryPublicId: uploadResponse?.publicId ?? '',
        previousPublicId: previousPublicId,
      );
      
      await _apiService.updateProfilePicture(request);
    } catch (e) {
      print('❌ Failed to notify backend about profile picture change: $e');
    }
  }
}
