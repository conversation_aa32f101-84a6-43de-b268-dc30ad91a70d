import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import 'package:path/path.dart' as path;
import '../models/profile_models.dart';

/// Cloudinary service for profile picture management
class CloudinaryService {
  final String _cloudName;
  final String _apiKey;
  final String _apiSecret;
  final String _uploadPreset;
  
  static const String _baseUrl = 'https://api.cloudinary.com/v1_1';
  static const String _profileFolder = 'drix/profiles';
  
  CloudinaryService({
    required String cloudName,
    required String apiKey,
    required String apiSecret,
    required String uploadPreset,
  }) : _cloudName = cloudName,
       _apiKey = apiKey,
       _apiSecret = apiSecret,
       _uploadPreset = uploadPreset;

  /// Upload profile picture to Cloudinary
  Future<CloudinaryUploadResponse> uploadProfilePicture({
    required File imageFile,
    required String userId,
    String? previousPublicId,
  }) async {
    try {
      print('🔄 Starting profile picture upload for user: $userId');
      
      // Generate unique public ID
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final publicId = '$_profileFolder/${userId}_$timestamp';
      
      // Prepare upload parameters
      final uploadParams = {
        'public_id': publicId,
        'folder': _profileFolder,
        'upload_preset': _uploadPreset,
        'transformation': 'c_fill,w_400,h_400,q_auto,f_auto',
        'tags': 'profile_picture,user_$userId',
        'context': 'user_id=$userId|upload_source=mobile',
        'timestamp': timestamp.toString(),
      };
      
      // Generate signature
      final signature = _generateSignature(uploadParams, _apiSecret);
      uploadParams['signature'] = signature;
      uploadParams['api_key'] = _apiKey;
      
      // Create multipart request
      final uri = Uri.parse('$_baseUrl/$_cloudName/image/upload');
      final request = http.MultipartRequest('POST', uri);
      
      // Add file
      final fileBytes = await imageFile.readAsBytes();
      final multipartFile = http.MultipartFile.fromBytes(
        'file',
        fileBytes,
        filename: path.basename(imageFile.path),
      );
      request.files.add(multipartFile);
      
      // Add parameters
      uploadParams.forEach((key, value) {
        request.fields[key] = value;
      });
      
      // Send request
      final response = await request.send();
      final responseBody = await response.stream.bytesToString();
      
      if (response.statusCode == 200) {
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        final uploadResponse = CloudinaryUploadResponse.fromJson(responseData);
        
        // Delete previous image if exists
        if (previousPublicId != null && previousPublicId.isNotEmpty) {
          await _deleteImage(previousPublicId);
        }
        
        print('✅ Profile picture uploaded successfully: ${uploadResponse.secureUrl}');
        return uploadResponse;
      } else {
        throw Exception('Upload failed with status ${response.statusCode}: $responseBody');
      }
    } catch (e) {
      print('❌ Failed to upload profile picture: $e');
      throw Exception('Failed to upload profile picture: $e');
    }
  }

  /// Generate optimized profile picture URL with transformations
  String generateProfilePictureUrl({
    required String publicId,
    int width = 400,
    int height = 400,
    String quality = 'auto',
    String format = 'auto',
    bool enableFaceDetection = true,
  }) {
    try {
      final transformations = [
        'c_fill',
        'w_$width',
        'h_$height',
        'q_$quality',
        'f_$format',
        if (enableFaceDetection) 'g_face',
        'dpr_auto',
      ].join(',');
      
      return 'https://res.cloudinary.com/$_cloudName/image/upload/$transformations/$publicId';
    } catch (e) {
      print('❌ Failed to generate profile picture URL: $e');
      return '';
    }
  }

  /// Generate thumbnail URL for profile picture
  String generateThumbnailUrl({
    required String publicId,
    int size = 100,
  }) {
    return generateProfilePictureUrl(
      publicId: publicId,
      width: size,
      height: size,
      quality: 'auto',
      format: 'webp',
    );
  }

  /// Delete image from Cloudinary
  Future<bool> deleteProfilePicture(String publicId) async {
    try {
      return await _deleteImage(publicId);
    } catch (e) {
      print('❌ Failed to delete profile picture: $e');
      return false;
    }
  }

  /// Get image details from Cloudinary
  Future<Map<String, dynamic>?> getImageDetails(String publicId) async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final signature = _generateSignature({
        'public_id': publicId,
        'timestamp': timestamp,
      }, _apiSecret);
      
      final uri = Uri.parse('$_baseUrl/$_cloudName/image/upload')
          .replace(queryParameters: {
        'public_id': publicId,
        'timestamp': timestamp,
        'api_key': _apiKey,
        'signature': signature,
      });
      
      final response = await http.get(uri);
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        print('⚠️ Failed to get image details: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('❌ Error getting image details: $e');
      return null;
    }
  }

  /// Validate image file before upload
  bool validateImageFile(File imageFile) {
    try {
      // Check file size (max 10MB)
      final fileSizeInBytes = imageFile.lengthSync();
      const maxSizeInBytes = 10 * 1024 * 1024; // 10MB
      
      if (fileSizeInBytes > maxSizeInBytes) {
        print('❌ Image file too large: ${fileSizeInBytes / (1024 * 1024)}MB');
        return false;
      }
      
      // Check file extension
      final extension = path.extension(imageFile.path).toLowerCase();
      const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
      
      if (!allowedExtensions.contains(extension)) {
        print('❌ Invalid image format: $extension');
        return false;
      }
      
      return true;
    } catch (e) {
      print('❌ Error validating image file: $e');
      return false;
    }
  }

  /// Generate upload signature for secure uploads
  String _generateSignature(Map<String, String> params, String apiSecret) {
    // Sort parameters alphabetically
    final sortedParams = Map.fromEntries(
      params.entries.toList()..sort((a, b) => a.key.compareTo(b.key))
    );
    
    // Create parameter string
    final paramString = sortedParams.entries
        .map((entry) => '${entry.key}=${entry.value}')
        .join('&');
    
    // Generate SHA1 hash
    final bytes = utf8.encode('$paramString$apiSecret');
    final digest = sha1.convert(bytes);
    
    return digest.toString();
  }

  /// Delete image from Cloudinary
  Future<bool> _deleteImage(String publicId) async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final params = {
        'public_id': publicId,
        'timestamp': timestamp,
      };
      
      final signature = _generateSignature(params, _apiSecret);
      
      final uri = Uri.parse('$_baseUrl/$_cloudName/image/destroy');
      final response = await http.post(
        uri,
        body: {
          ...params,
          'api_key': _apiKey,
          'signature': signature,
        },
      );
      
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body) as Map<String, dynamic>;
        final result = responseData['result'] as String?;
        
        if (result == 'ok') {
          print('✅ Image deleted successfully: $publicId');
          return true;
        } else {
          print('⚠️ Image deletion result: $result');
          return false;
        }
      } else {
        print('❌ Failed to delete image: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('❌ Error deleting image: $e');
      return false;
    }
  }

  /// Get optimized URLs for different screen densities
  Map<String, String> getResponsiveUrls({
    required String publicId,
    int baseWidth = 400,
    int baseHeight = 400,
  }) {
    return {
      '1x': generateProfilePictureUrl(
        publicId: publicId,
        width: baseWidth,
        height: baseHeight,
      ),
      '2x': generateProfilePictureUrl(
        publicId: publicId,
        width: baseWidth * 2,
        height: baseHeight * 2,
      ),
      '3x': generateProfilePictureUrl(
        publicId: publicId,
        width: baseWidth * 3,
        height: baseHeight * 3,
      ),
    };
  }

  /// Generate progressive JPEG URL for better loading experience
  String generateProgressiveUrl({
    required String publicId,
    int width = 400,
    int height = 400,
  }) {
    final transformations = [
      'c_fill',
      'w_$width',
      'h_$height',
      'q_auto',
      'f_jpg',
      'fl_progressive',
      'g_face',
    ].join(',');
    
    return 'https://res.cloudinary.com/$_cloudName/image/upload/$transformations/$publicId';
  }
}
