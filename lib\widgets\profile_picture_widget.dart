import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';
import '../blocs/profile/profile_bloc.dart';
import '../blocs/profile/profile_event.dart';
import '../blocs/profile/profile_state.dart';
import '../blocs/auth/auth_bloc.dart';
import '../blocs/auth/auth_state.dart';
import '../models/profile_models.dart';
import '../models/user_model.dart';
import '../utils/design_tokens.dart';

/// Shared profile picture widget that syncs between home and profile screens
class ProfilePictureWidget extends StatelessWidget {
  final double radius;
  final bool showEditIcon;
  final VoidCallback? onTap;
  final String? fallbackAsset;

  const ProfilePictureWidget({
    super.key,
    this.radius = 24,
    this.showEditIcon = false,
    this.onTap,
    this.fallbackAsset = 'assets/images/darvis_main.PNG',
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<ProfileBloc>(),
      child: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, authState) {
          if (authState is! AuthAuthenticated) {
            return _buildFallbackAvatar();
          }

          return BlocBuilder<ProfileBloc, ProfileState>(
            builder: (context, profileState) {
              // Load profile when widget is first built
              if (profileState is ProfileInitial) {
                context.read<ProfileBloc>().add(LoadUserProfile(authState.user.uid));
                return _buildLoadingAvatar();
              }

              if (profileState is ProfileLoading) {
                return _buildLoadingAvatar();
              }

              if (profileState is ProfileLoaded) {
                return _buildProfileAvatar(context, profileState, authState.user.uid);
              }

              if (profileState is ProfilePictureChanged) {
                // Reload profile after picture change
                context.read<ProfileBloc>().add(LoadUserProfile(authState.user.uid));
                return _buildProfileAvatar(
                  context, 
                  ProfileLoaded(
                    profile: UserProfile()..profilePictureUrl = profileState.newPictureUrl,
                    profilePictureUrl: profileState.newPictureUrl,
                    thumbnailUrl: profileState.thumbnailUrl,
                    responsiveUrls: profileState.responsiveUrls,
                  ), 
                  authState.user.uid,
                );
              }

              return _buildFallbackAvatar();
            },
          );
        },
      ),
    );
  }

  Widget _buildProfileAvatar(BuildContext context, ProfileLoaded state, String userId) {
    final profilePictureUrl = state.thumbnailUrl ?? state.profilePictureUrl;
    
    return GestureDetector(
      onTap: onTap ?? (showEditIcon ? () => _showImagePicker(context, userId) : null),
      child: Stack(
        children: [
          Container(
            width: radius * 2,
            height: radius * 2,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: showEditIcon ? Border.all(
                color: DesignTokens.primaryInteractiveBlue,
                width: 3,
              ) : null,
            ),
            child: ClipOval(
              child: profilePictureUrl != null && profilePictureUrl.isNotEmpty
                  ? Image.network(
                      profilePictureUrl,
                      width: radius * 2,
                      height: radius * 2,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return _buildLoadingAvatar();
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return _buildFallbackAvatar();
                      },
                    )
                  : _buildFallbackAvatar(),
            ),
          ),
          if (showEditIcon)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: radius * 0.6,
                height: radius * 0.6,
                decoration: BoxDecoration(
                  color: DesignTokens.primaryInteractiveBlue,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: DesignTokens.backgroundApp,
                    width: 2,
                  ),
                ),
                child: Icon(
                  Icons.edit,
                  color: Colors.white,
                  size: radius * 0.3,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFallbackAvatar() {
    return Container(
      width: radius * 2,
      height: radius * 2,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: showEditIcon ? Border.all(
          color: DesignTokens.primaryInteractiveBlue,
          width: 3,
        ) : null,
      ),
      child: ClipOval(
        child: Container(
          color: DesignTokens.backgroundCard,
          child: Center(
            child: fallbackAsset != null
                ? Image.asset(
                    fallbackAsset!,
                    width: radius * 1.2,
                    height: radius * 1.2,
                    fit: BoxFit.contain,
                  )
                : Icon(
                    Icons.person,
                    size: radius * 1.2,
                    color: DesignTokens.textSecondary,
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingAvatar() {
    return Container(
      width: radius * 2,
      height: radius * 2,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: showEditIcon ? Border.all(
          color: DesignTokens.primaryInteractiveBlue,
          width: 3,
        ) : null,
      ),
      child: ClipOval(
        child: Container(
          color: DesignTokens.backgroundCard,
          child: Center(
            child: SizedBox(
              width: radius * 0.8,
              height: radius * 0.8,
              child: const CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  DesignTokens.primaryInteractiveBlue,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showImagePicker(BuildContext context, String userId) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: DesignTokens.backgroundCard,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: DesignTokens.spacingMd),
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: DesignTokens.textMuted,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: DesignTokens.spacingLg),
              Text(
                'Change Profile Photo',
                style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
              ),
              const SizedBox(height: DesignTokens.spacingLg),
              ListTile(
                leading: const Icon(Icons.camera_alt, color: DesignTokens.primaryInteractiveBlue),
                title: const Text('Take Photo', style: DesignTokens.bodyStyle),
                onTap: () {
                  Navigator.pop(context);
                  _changeProfilePicture(context, userId, ImageSource.camera);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library, color: DesignTokens.primaryInteractiveBlue),
                title: const Text('Choose from Gallery', style: DesignTokens.bodyStyle),
                onTap: () {
                  Navigator.pop(context);
                  _changeProfilePicture(context, userId, ImageSource.gallery);
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Remove Photo', style: DesignTokens.bodyStyle),
                onTap: () {
                  Navigator.pop(context);
                  _deleteProfilePicture(context, userId);
                },
              ),
              const SizedBox(height: DesignTokens.spacingLg),
            ],
          ),
        ),
      ),
    );
  }

  void _changeProfilePicture(BuildContext context, String userId, ImageSource source) {
    context.read<ProfileBloc>().add(ChangeProfilePicture(
      userId: userId,
      source: source,
    ));
  }

  void _deleteProfilePicture(BuildContext context, String userId) {
    context.read<ProfileBloc>().add(DeleteProfilePicture(userId));
  }
}


