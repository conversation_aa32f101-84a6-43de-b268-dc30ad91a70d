design_system:
  name: "<PERSON><PERSON> AI Assistant"
  version: "1.2.0"
  description: "A dark, futuristic design system with blue and green accents, focusing on gradients, glows, and a clean, modern interface."

colors:
  primary:
    deep_blue: "#111D2F"
    accent_blue: "#183C8E"
    interactive_blue: "#004EEB"
  
  background:
    app: "#000000"
    card: "#111D2F"
    accent_card: "#183C8E"
    chat_bubble_user: "#0A1B3F"
    chat_bubble_bot: "#171717"
    text_input: "#DCDFE7"
    side_menu: "#020D1E"
  
  text:
    primary: "#FFFFFF"
    secondary: "#A9B4C8"
    muted: "#7B8499"
    on_light_background: "#222425"
  
  icon:
    primary: "#FFFFFF"
    secondary: "#A9B4C8"
  
  border:
    interactive_element: "#262626"

  navigation:
    inactive: "#676D75"

  # Task Management Priority Colors
  priority:
    high: "#EF4444"      # Red for high priority
    medium: "#F59E0B"    # Amber for medium priority
    low: "#3B82F6"       # Blue for low priority

  # Category Colors for Task Management
  category:
    university: "#8B5CF6"    # Purple
    food: "#10B981"          # Green
    exercise: "#EF4444"      # Red
    work: "#3B82F6"          # Blue
    family: "#F59E0B"        # Amber
    health: "#EF4444"        # Red
    home: "#6B7280"          # Gray
    movie: "#8B5CF6"         # Purple
    music: "#06B6D4"         # Teal
    school: "#3B82F6"        # Blue
    social: "#10B981"        # Green



gradients:
  app_name_header:
    type: "linear"
    direction: "180deg"
    stops:
      - color: "#DCDFE7"
        position: "0%"
      - color: "#54565D"
        position: "100%"

  background_glow_blue:
    type: "radial"
    center: "center"
    stops:
      - color: "#183C8E"
        position: "0%"
      - color: "#11272F"
        position: "80%"

  background_glow_green:
    type: "radial"
    center: "center"
    stops:
      - color: "#183C8E"
        position: "0%"
      - color: "#03411D"
        position: "80%"

  # Therapy Voice Screen Gradients
  therapy_neutral_blur:
    type: "radial"
    center: "center"
    stops:
      - color: "#2A3B4C"
        position: "0%"
      - color: "#1A2332"
        position: "80%"

  therapy_listening_blur:
    type: "radial"
    center: "center"
    stops:
      - color: "#1E3A8A"
        position: "0%"
      - color: "#3B82F6"
        position: "80%"

  therapy_speaking_blur:
    type: "radial"
    center: "center"
    stops:
      - color: "#059669"
        position: "0%"
      - color: "#10B981"
        position: "80%"

  today_card_background:
    type: "linear"
    direction: "180deg" # Assuming top-to-bottom
    stops:
      - color: "#111D2F"
        position: "0%"
      - color: "#183CBE"
        position: "100%"

typography:
  font_families:
    heading: "Outfit"
    body: "Poppins"
  
  styles:
    app_name:
      font_family: "heading"
      font_size: 36
      font_weight: 600
      use_gradient: true
      gradient_ref: "app_name_header"
    
    card_title:
      font_family: "heading"
      font_size: 18
      font_weight: 600
      color_ref: "text.primary"
    
    body:
      font_family: "body"
      font_size: 14
      font_weight: 400
      color_ref: "text.secondary"
    
    chat_message:
      font_family: "body"
      font_size: 12
      font_weight: 400
      color_ref: "text.primary"

          # ADD THESE NEW STYLES BELOW THE EXISTING ONES
    onboardingHeadline:
      font_family: "heading"  # This correctly uses 'Outfit'
      font_size: 32
      font_weight: 700        # For 'bold', it's better to use numbers. 700 is bold.
      color_ref: "text.primary" # This correctly uses your white color

    onboardingBody:
      font_family: "body"     # This correctly uses 'Poppins'
      font_size: 16
      font_weight: 400        # 400 is 'regular'
      color_ref: "text.secondary" # This uses your A9B4C8 color, which is perfect for body text

    onboardingButton:
      font_family: "body"
      font_size: 16
      font_weight: 400        # Regular for 'Skip' and 'Next'
      color_ref: "text.primary"

    onboardingButtonBold:
      font_family: "body"
      font_size: 16
      font_weight: 700        # Bold for 'Get Started'
      color_ref: "text.primary"

    formLabel:
      font_family: "heading" # Using 'Outfit' as requested
      font_size: 16
      font_weight: 400      # 400 is 'regular'
      color_ref: "text.primary"
    
    googleButtonText:
      font_family: "heading" # Using 'Outfit' as requested
      font_size: 16
      font_weight: 400      # 400 is 'regular'
      color_ref: "text.primary"

    primaryButtonText:
      font_family: "body"
      font_size: 16
      font_weight: 700      # 700 is 'bold'
      color_ref: "text.on_light_background"

    linkText:
      font_family: "body"
      font_size: 14
      font_weight: 600      # 600 is 'semibold' to make it stand out
      color_ref: "primary.interactive_blue"

    welcomeHeadline:
      font_family: "heading" # Uses 'Outfit'
      font_size: 36
      font_weight: 600      # 600 is 'semibold'
      color_ref: "text.primary"

       # New additions for the Home screen
    greetingText:
      font_family: "heading" # Outfit
      font_size: 16
      font_weight: 400      # regular
      color_ref: "text.primary"

    cardLargeHeading:
      font_family: "heading" # Outfit
      font_size: 20
      font_weight: 500      # medium
      color: "#DCDFE7"      # Using direct hex as requested

    cardSubheading:
      font_family: "heading" # Outfit
      font_size: 14         # Assuming a slightly smaller size for subheading
      font_weight: 400      # regular
      color: "#DCDFE7"      # Using direct hex as requested

    cardListItem:
      font_family: "body" # Poppins
      font_size: 14
      font_weight: 400  # regular
      color_ref: "text.primary"

    navBarLabel:
      font_family: "heading" # Outfit
      font_size: 11
      font_weight: 400      # regular
      # Color will be applied in the widget based on active/inactive state

    voiceScreenTitle:
      font_family: "heading" # Outfit
      font_size: 70
      font_weight: 500      # medium
      use_gradient: true
      gradient_ref: "app_name_header"
    
    voiceStatusText:
      font_family: "heading" # Outfit
      font_size: 15
      font_weight: 400      # regular
      color: "#DCDFE7"

        # New additions for the Chat screen
    menuSectionTitle:
      font_family: "heading" # Outfit
      font_size: 24
      font_weight: 400      # regular
      color_ref: "text.primary"

    menuItemText:
      font_family: "body" # Poppins
      font_size: 12
      font_weight: 400      # regular
      color_ref: "text.primary"

    textInputPlaceholder:
      font_family: "heading" # Outfit
      font_size: 10
      font_weight: 400      # regular
      color_ref: "text.on_light_background"


      # In design_tokens.yaml, under typography.styles:
  
  # ... (existing styles) ...

  # New additions for the Calendar screen
    calendarDropdownItem:
      font_family: "body" # Poppins
      font_size: 10
      font_weight: 400      # regular
      color: "#DCDFE7"

    calendarMonthName:
      font_family: "heading" # Outfit
      font_size: 16
      font_weight: 700      # bold
      color: "#DCDFE7"

    calendarDayOfWeek:
      font_family: "heading" # Outfit
      font_size: 17
      font_weight: 400      # regular
      color: "#DCDFE7"

    calendarDateNumber:
      font_family: "heading" # Outfit
      font_size: 16
      font_weight: 400      # regular
      color: "#DCDFE7"

    eventListItemTitle:
      font_family: "heading" # Outfit
      font_size: 13
      font_weight: 400      # regular
      color: "#DCDFE7"

    eventListItemTime:
      font_family: "body" # Poppins
      font_size: 13
      font_weight: 400      # regular
      color: "#DCDFE7"

    # Therapy Voice Screen Typography
    therapySessionTitle:
      font_family: "heading" # Outfit
      font_size: 70
      font_weight: 500      # medium
      use_gradient: true
      gradient_ref: "app_name_header"

    therapyStatusText:
      font_family: "heading" # Outfit
      font_size: 15
      font_weight: 400      # regular
      color: "#A9B4C8"      # Light gray for subtle text

spacing:
  xs: 4
  sm: 8
  md: 16
  lg: 24
  xl: 32
  xxl: 48

border_radius:
  sm: 8
  md: 16
  lg: 20
  xl: 24
  chat_bubble: 18

effects:
  glows:
    active_mic:
      blur: 15
      spread: 5
      color: "#183C8E"
      opacity: 0.6

components:
  cards:
    default:
      background_ref: "background.card"
      border_radius_ref: "lg"
    accent:
      background_ref: "background.accent_card"
      border_radius_ref: "lg"
  
  buttons:
    microphone:
      default:
        background: "transparent"
        border_color_ref: "border.interactive_element"
        border_width: 1
        icon_color_ref: "icon.secondary"
      active:
        background_ref: "primary.interactive_blue"
        border_width: 0
        icon_color_ref: "icon.primary"
        glow_ref: "active_mic"
  
  chat:
    user_bubble:
      background_ref: "background.chat_bubble_user"
      border_radius_ref: "chat_bubble"
    bot_bubble:
      background_ref: "background.chat_bubble_bot"
      border_radius_ref: "chat_bubble"
    text_input:
      background_ref: "background.text_input"
      border_radius_ref: "xl"
      text_color_ref: "text.on_light_background"

  onboarding_indicator:
    active_color_ref: "text.primary"
    inactive_color: "#333333" # Using the specific hex code as it's unique

  navigation:
    inactive_color: "#676D75"

  google_button_background: "#262626"
  form_field_background: "#262626"
  primary_button_background: "#DCDFE7"
  link: "#014FFF"
  # nav_bar_inactive: "#676D75"
  mic_button_border: "#00152D"
  mode_toggle_inactive_background: "#283140"
  mode_toggle_active_background: "#DCDFE7"
  event_list_item_background: "#283140"
  calendar_dropdown_background: "#283140"
  calendar_today_marker_background: "#DCDFE7"
  calendar_selected_marker_outline: "#183CBE" # Using a distinct blue for the ring

