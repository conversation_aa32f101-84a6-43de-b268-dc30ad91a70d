import 'package:flutter/foundation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:device_apps/device_apps.dart';
import 'package:android_intent_plus/android_intent.dart';
import 'dart:io';

/// Service for handling social media deep linking and app detection
class SocialMediaService {
  static final SocialMediaService _instance = SocialMediaService._internal();
  factory SocialMediaService() => _instance;
  SocialMediaService._internal();

  /// Social media platform configurations
  static const Map<String, Map<String, String>> _platformConfigs = {
    'twitter': {
      'packageName': 'com.twitter.android',
      'appScheme': 'twitter://user?screen_name=',
      'webUrl': 'https://twitter.com/',
      'displayName': 'Twitter',
    },
    'instagram': {
      'packageName': 'com.instagram.android',
      'appScheme': 'instagram://user?username=',
      'webUrl': 'https://instagram.com/',
      'displayName': 'Instagram',
    },
    'linkedin': {
      'packageName': 'com.linkedin.android',
      'appScheme': 'linkedin://profile/',
      'webUrl': 'https://linkedin.com/in/',
      'displayName': 'LinkedIn',
    },
    'whatsapp': {
      'packageName': 'com.whatsapp',
      'appScheme': 'whatsapp://send?phone=',
      'webUrl': 'https://wa.me/',
      'displayName': 'WhatsApp',
    },
  };

  /// Check if a social media app is installed on the device
  Future<bool> isAppInstalled(String platform) async {
    try {
      if (!Platform.isAndroid) {
        return false; // For now, only Android support
      }

      final config = _platformConfigs[platform.toLowerCase()];
      if (config == null) return false;

      final packageName = config['packageName']!;
      final app = await DeviceApps.getApp(packageName);
      return app != null;
    } catch (e) {
      debugPrint('Error checking if app is installed: $e');
      return false;
    }
  }

  /// Open social media profile using app or web fallback
  Future<bool> openProfile(String platform, String username) async {
    try {
      final config = _platformConfigs[platform.toLowerCase()];
      if (config == null) {
        debugPrint('Unsupported platform: $platform');
        return false;
      }

      // Try to open with app first
      if (Platform.isAndroid) {
        final isInstalled = await isAppInstalled(platform);
        if (isInstalled) {
          final success = await _openWithApp(platform, username);
          if (success) return true;
        }
      }

      // Fallback to web
      return await _openWithWeb(platform, username);
    } catch (e) {
      debugPrint('Error opening social media profile: $e');
      return false;
    }
  }

  /// Open profile using native app
  Future<bool> _openWithApp(String platform, String username) async {
    try {
      final config = _platformConfigs[platform.toLowerCase()]!;
      
      if (Platform.isAndroid) {
        final intent = AndroidIntent(
          package: config['packageName'],
          action: 'android.intent.action.VIEW',
          data: '${config['appScheme']}$username',
        );
        
        await intent.launch();
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Error opening with app: $e');
      return false;
    }
  }

  /// Open profile using web browser
  Future<bool> _openWithWeb(String platform, String username) async {
    try {
      final config = _platformConfigs[platform.toLowerCase()]!;
      final url = '${config['webUrl']}$username';
      
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Error opening with web: $e');
      return false;
    }
  }

  /// Get all installed social media apps
  Future<List<String>> getInstalledSocialApps() async {
    final installedApps = <String>[];
    
    for (final platform in _platformConfigs.keys) {
      final isInstalled = await isAppInstalled(platform);
      if (isInstalled) {
        installedApps.add(platform);
      }
    }
    
    return installedApps;
  }

  /// Get platform display name
  String getPlatformDisplayName(String platform) {
    final config = _platformConfigs[platform.toLowerCase()];
    return config?['displayName'] ?? platform;
  }

  /// Validate username format for platform
  bool isValidUsername(String platform, String username) {
    if (username.isEmpty) return false;
    
    switch (platform.toLowerCase()) {
      case 'twitter':
        // Twitter usernames: 1-15 characters, alphanumeric and underscore
        return RegExp(r'^[a-zA-Z0-9_]{1,15}$').hasMatch(username);
      case 'instagram':
        // Instagram usernames: 1-30 characters, alphanumeric, underscore, period
        return RegExp(r'^[a-zA-Z0-9_.]{1,30}$').hasMatch(username);
      case 'linkedin':
        // LinkedIn: alphanumeric and hyphens
        return RegExp(r'^[a-zA-Z0-9-]+$').hasMatch(username);
      case 'whatsapp':
        // WhatsApp: phone number format
        return RegExp(r'^\+?[1-9]\d{1,14}$').hasMatch(username);
      default:
        return true; // Allow any format for unknown platforms
    }
  }

  /// Get username placeholder text for platform
  String getUsernamePlaceholder(String platform) {
    switch (platform.toLowerCase()) {
      case 'twitter':
        return '@username';
      case 'instagram':
        return '@username';
      case 'linkedin':
        return 'profile-name';
      case 'whatsapp':
        return '+1234567890';
      default:
        return 'username';
    }
  }

  /// Generate shareable link for profile
  String generateShareableLink(String platform, String username) {
    final config = _platformConfigs[platform.toLowerCase()];
    if (config == null) return '';
    
    return '${config['webUrl']}$username';
  }

  /// Search for username on platform (opens search)
  Future<bool> searchUsername(String platform, String query) async {
    try {
      final config = _platformConfigs[platform.toLowerCase()];
      if (config == null) return false;

      String searchUrl;
      switch (platform.toLowerCase()) {
        case 'twitter':
          searchUrl = 'https://twitter.com/search?q=$query';
          break;
        case 'instagram':
          searchUrl = 'https://instagram.com/explore/search/keyword/?q=$query';
          break;
        case 'linkedin':
          searchUrl = 'https://linkedin.com/search/results/people/?keywords=$query';
          break;
        case 'whatsapp':
          // WhatsApp doesn't have web search, return false
          return false;
        default:
          return false;
      }

      final uri = Uri.parse(searchUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Error searching username: $e');
      return false;
    }
  }

  /// Get platform icon asset path
  String getPlatformIconPath(String platform) {
    switch (platform.toLowerCase()) {
      case 'twitter':
        return 'assets/icons/twitter.svg';
      case 'instagram':
        return 'assets/icons/instagram.svg';
      case 'linkedin':
        return 'assets/icons/linkedin.svg';
      case 'whatsapp':
        return 'assets/icons/whatsapp.svg';
      default:
        return 'assets/icons/social_media.svg';
    }
  }

  /// Get platform color
  int getPlatformColor(String platform) {
    switch (platform.toLowerCase()) {
      case 'twitter':
        return 0xFF1DA1F2;
      case 'instagram':
        return 0xFFE4405F;
      case 'linkedin':
        return 0xFF0077B5;
      case 'whatsapp':
        return 0xFF25D366;
      default:
        return 0xFF6B7280;
    }
  }

  /// Get all supported platforms
  List<String> getSupportedPlatforms() {
    return _platformConfigs.keys.toList();
  }
}
