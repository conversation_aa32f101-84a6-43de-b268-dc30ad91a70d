import 'package:flutter/material.dart';
import 'dart:ui';
import '../../../utils/design_tokens.dart';
import '../models/task.dart';
import '../models/category.dart';

enum ModalSection { none, dateTime, category, priority }

class AddTaskModal extends StatefulWidget {
  final Function(Task) onTaskCreated;
  final List<TaskCategory>? customCategories;

  const AddTaskModal({
    super.key,
    required this.onTaskCreated,
    this.customCategories,
  });

  @override
  State<AddTaskModal> createState() => _AddTaskModalState();
}

class _AddTaskModalState extends State<AddTaskModal>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final FocusNode _titleFocusNode = FocusNode();

  TaskCategory _selectedCategory = TaskCategories.university;
  TaskPriority _selectedPriority = TaskPriority.medium;
  DateTime? _selectedDueDate;
  TimeOfDay? _selectedTime;
  ModalSection _expandedSection = ModalSection.none;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _titleFocusNode.requestFocus();
    _titleController.addListener(() {
      setState(() {}); // Rebuild to update button state
    });
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    _slideController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _closeModal(),
      child: Scaffold(
        backgroundColor: Colors.black.withOpacity(0.5),
        body: Stack(
          children: [
            Positioned.fill(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                child: Container(
                  color: Colors.black.withOpacity(0.3),
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: SlideTransition(
                position: _slideAnimation,
                child: GestureDetector(
                  onTap: () {}, // Prevent closing when tapping inside
                  child: _buildModalContent(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModalContent() {
    return Container(
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(DesignTokens.borderRadiusXl),
          topRight: Radius.circular(DesignTokens.borderRadiusXl),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: Container(
            padding: const EdgeInsets.all(DesignTokens.spacingLg),
            decoration: const BoxDecoration(
              color: Color(0xFF1A1A1A),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(DesignTokens.borderRadiusXl),
                topRight: Radius.circular(DesignTokens.borderRadiusXl),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: DesignTokens.spacingMd),
                _buildTitleInput(),
                const SizedBox(height: DesignTokens.spacingMd),
                _buildDescriptionInput(),
                const SizedBox(height: DesignTokens.spacingMd),
                _buildIconRow(),
                if (_expandedSection != ModalSection.none) ...[
                  const SizedBox(height: DesignTokens.spacingMd),
                  _buildExpandedSection(),
                ],
                const SizedBox(height: DesignTokens.spacingLg),
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Text(
          'Add Task',
          style: DesignTokens.cardTitleStyle,
        ),
        const Spacer(),
        IconButton(
          onPressed: _closeModal,
          icon: const Icon(
            Icons.close,
            color: DesignTokens.iconPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildTitleInput() {
    return TextField(
      controller: _titleController,
      focusNode: _titleFocusNode,
      style: DesignTokens.bodyStyle,
      decoration: InputDecoration(
        hintText: 'Task title',
        hintStyle: DesignTokens.bodyStyle.copyWith(
          color: DesignTokens.textMuted,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          borderSide: const BorderSide(
            color: Color(0xFF333333),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          borderSide: const BorderSide(
            color: Color(0xFF333333),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          borderSide: const BorderSide(
            color: DesignTokens.primaryInteractiveBlue,
          ),
        ),
        filled: true,
        fillColor: const Color(0xFF2A2A2A),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingMd,
        ),
      ),
    );
  }

  Widget _buildDescriptionInput() {
    return TextField(
      controller: _descriptionController,
      style: DesignTokens.bodyStyle,
      maxLines: 1,
      decoration: InputDecoration(
        hintText: 'Description (optional)',
        hintStyle: DesignTokens.bodyStyle.copyWith(
          color: DesignTokens.textMuted,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          borderSide: const BorderSide(
            color: Color(0xFF333333),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          borderSide: const BorderSide(
            color: Color(0xFF333333),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          borderSide: const BorderSide(
            color: DesignTokens.primaryInteractiveBlue,
          ),
        ),
        filled: true,
        fillColor: const Color(0xFF2A2A2A),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingMd,
        ),
      ),
    );
  }

  Widget _buildIconRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildIconButton(
          icon: Icons.access_time,
          label: _getDateTimeLabel(),
          isSelected: _selectedDueDate != null || _selectedTime != null,
          onTap: () => _toggleSection(ModalSection.dateTime),
        ),
        _buildIconButton(
          icon: Icons.local_offer,
          label: _selectedCategory.name,
          isSelected: _selectedCategory != TaskCategories.university,
          onTap: () => _toggleSection(ModalSection.category),
        ),
        _buildIconButton(
          icon: Icons.flag,
          label: _selectedPriority.name.toUpperCase(),
          isSelected: _selectedPriority != TaskPriority.medium,
          onTap: () => _toggleSection(ModalSection.priority),
        ),
      ],
    );
  }

  Widget _buildIconButton({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingXs),
          padding: const EdgeInsets.symmetric(
            vertical: DesignTokens.spacingMd,
            horizontal: DesignTokens.spacingSm,
          ),
          decoration: BoxDecoration(
            color: isSelected
                ? DesignTokens.primaryInteractiveBlue.withAlpha(51)
                : const Color(0xFF2A2A2A),
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            border: Border.all(
              color: isSelected
                  ? DesignTokens.primaryInteractiveBlue
                  : const Color(0xFF333333),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: isSelected
                    ? DesignTokens.primaryInteractiveBlue
                    : DesignTokens.iconSecondary,
                size: 20,
              ),
              const SizedBox(height: DesignTokens.spacingXs),
              Text(
                label,
                style: DesignTokens.menuItemTextStyle.copyWith(
                  color: isSelected
                      ? DesignTokens.primaryInteractiveBlue
                      : DesignTokens.textSecondary,
                  fontSize: 11,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExpandedSection() {
    switch (_expandedSection) {
      case ModalSection.dateTime:
        return _buildDateTimeSection();
      case ModalSection.category:
        return _buildCategorySection();
      case ModalSection.priority:
        return _buildPrioritySection();
      case ModalSection.none:
        return const SizedBox.shrink();
    }
  }

  void _toggleSection(ModalSection section) {
    setState(() {
      _expandedSection = _expandedSection == section ? ModalSection.none : section;
    });
  }

  String _getDateTimeLabel() {
    if (_selectedDueDate != null && _selectedTime != null) {
      return '${_selectedDueDate!.day}/${_selectedDueDate!.month} ${_selectedTime!.format(context)}';
    } else if (_selectedDueDate != null) {
      return '${_selectedDueDate!.day}/${_selectedDueDate!.month}';
    } else if (_selectedTime != null) {
      return _selectedTime!.format(context);
    }
    return 'Set time';
  }

  Widget _buildDateTimeSection() {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A2A),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        border: Border.all(color: const Color(0xFF333333)),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: _selectDate,
              child: Container(
                padding: const EdgeInsets.all(DesignTokens.spacingMd),
                decoration: BoxDecoration(
                  color: const Color(0xFF1A1A1A),
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.calendar_today, color: DesignTokens.iconSecondary, size: 16),
                    const SizedBox(width: DesignTokens.spacingSm),
                    Text(
                      _selectedDueDate != null
                          ? '${_selectedDueDate!.day}/${_selectedDueDate!.month}/${_selectedDueDate!.year}'
                          : 'Date',
                      style: DesignTokens.menuItemTextStyle.copyWith(
                        color: _selectedDueDate != null ? DesignTokens.textPrimary : DesignTokens.textMuted,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: DesignTokens.spacingMd),
          Expanded(
            child: GestureDetector(
              onTap: _selectTime,
              child: Container(
                padding: const EdgeInsets.all(DesignTokens.spacingMd),
                decoration: BoxDecoration(
                  color: const Color(0xFF1A1A1A),
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.access_time, color: DesignTokens.iconSecondary, size: 16),
                    const SizedBox(width: DesignTokens.spacingSm),
                    Text(
                      _selectedTime != null ? _selectedTime!.format(context) : 'Time',
                      style: DesignTokens.menuItemTextStyle.copyWith(
                        color: _selectedTime != null ? DesignTokens.textPrimary : DesignTokens.textMuted,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySection() {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A2A),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        border: Border.all(color: const Color(0xFF333333)),
      ),
      child: Wrap(
        spacing: DesignTokens.spacingSm,
        runSpacing: DesignTokens.spacingSm,
        children: TaskCategories.getAllCategories(widget.customCategories).take(8).map((category) {
          final isSelected = _selectedCategory.id == category.id;
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedCategory = category;
                _expandedSection = ModalSection.none;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: DesignTokens.spacingMd,
                vertical: DesignTokens.spacingSm,
              ),
              decoration: BoxDecoration(
                color: isSelected
                    ? category.color.withAlpha(77)
                    : category.color.withAlpha(26),
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusXl),
                border: Border.all(
                  color: category.color.withAlpha(isSelected ? 204 : 77),
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Text(
                category.name,
                style: DesignTokens.menuItemTextStyle.copyWith(
                  color: category.color,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildPrioritySection() {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A2A),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        border: Border.all(color: const Color(0xFF333333)),
      ),
      child: Row(
        children: TaskPriority.values.map((priority) {
          final isSelected = _selectedPriority == priority;
          final color = _getPriorityColor(priority);

          return Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedPriority = priority;
                  _expandedSection = ModalSection.none;
                });
              },
              child: Container(
                margin: const EdgeInsets.only(right: DesignTokens.spacingSm),
                padding: const EdgeInsets.symmetric(
                  vertical: DesignTokens.spacingMd,
                ),
                decoration: BoxDecoration(
                  color: isSelected
                      ? color.withAlpha(77)
                      : color.withAlpha(26),
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                  border: Border.all(
                    color: color.withAlpha(isSelected ? 204 : 77),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Text(
                  _getPriorityLabel(priority),
                  style: DesignTokens.menuItemTextStyle.copyWith(
                    color: color,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildActionButtons() {
    final canCreate = _titleController.text.trim().isNotEmpty;

    return Row(
      children: [
        Expanded(
          child: TextButton(
            onPressed: _closeModal,
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: DesignTokens.spacingMd),
            ),
            child: Text(
              'Cancel',
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textSecondary,
              ),
            ),
          ),
        ),
        const SizedBox(width: DesignTokens.spacingMd),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: canCreate ? _createTask : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: canCreate
                  ? DesignTokens.primaryInteractiveBlue
                  : DesignTokens.textMuted,
              padding: const EdgeInsets.symmetric(vertical: DesignTokens.spacingMd),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              ),
            ),
            child: Text(
              'Add Task',
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return DesignTokens.priorityHigh;
      case TaskPriority.medium:
        return DesignTokens.priorityMedium;
      case TaskPriority.low:
        return DesignTokens.priorityLow;
    }
  }

  String _getPriorityLabel(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return 'High';
      case TaskPriority.medium:
        return 'Medium';
      case TaskPriority.low:
        return 'Low';
    }
  }

  void _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDueDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null) {
      setState(() {
        _selectedDueDate = date;
      });
    }
  }

  void _selectTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _selectedTime ?? TimeOfDay.now(),
    );
    
    if (time != null) {
      setState(() {
        _selectedTime = time;
      });
    }
  }

  void _createTask() {
    if (_titleController.text.trim().isEmpty) return;

    DateTime? dueDateTime;
    if (_selectedDueDate != null) {
      dueDateTime = DateTime(
        _selectedDueDate!.year,
        _selectedDueDate!.month,
        _selectedDueDate!.day,
        _selectedTime?.hour ?? 0,
        _selectedTime?.minute ?? 0,
      );
    }

    final task = Task(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      category: _selectedCategory,
      priority: _selectedPriority,
      dueDate: dueDateTime,
      createdAt: DateTime.now(),
    );

    widget.onTaskCreated(task);
    _closeModal();
  }

  void _closeModal() {
    _slideController.reverse().then((_) {
      Navigator.of(context).pop();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _titleFocusNode.dispose();
    super.dispose();
  }
}
