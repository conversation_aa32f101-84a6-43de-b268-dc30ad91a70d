import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';

/// Service for handling contact-related permissions
class ContactPermissionService {
  static final ContactPermissionService _instance = ContactPermissionService._internal();
  factory ContactPermissionService() => _instance;
  ContactPermissionService._internal();

  /// Check if contacts permission is granted
  Future<bool> hasContactsPermission() async {
    try {
      final status = await Permission.contacts.status;
      return status.isGranted;
    } catch (e) {
      debugPrint('Error checking contacts permission: $e');
      return false;
    }
  }

  /// Request contacts permission
  Future<PermissionStatus> requestContactsPermission() async {
    try {
      final status = await Permission.contacts.request();
      return status;
    } catch (e) {
      debugPrint('Error requesting contacts permission: $e');
      return PermissionStatus.denied;
    }
  }

  /// Check if phone permission is granted (for calling)
  Future<bool> hasPhonePermission() async {
    try {
      final status = await Permission.phone.status;
      return status.isGranted;
    } catch (e) {
      debugPrint('Error checking phone permission: $e');
      return false;
    }
  }

  /// Request phone permission
  Future<PermissionStatus> requestPhonePermission() async {
    try {
      final status = await Permission.phone.request();
      return status;
    } catch (e) {
      debugPrint('Error requesting phone permission: $e');
      return PermissionStatus.denied;
    }
  }

  /// Check if SMS permission is granted
  Future<bool> hasSmsPermission() async {
    try {
      final status = await Permission.sms.status;
      return status.isGranted;
    } catch (e) {
      debugPrint('Error checking SMS permission: $e');
      return false;
    }
  }

  /// Request SMS permission
  Future<PermissionStatus> requestSmsPermission() async {
    try {
      final status = await Permission.sms.request();
      return status;
    } catch (e) {
      debugPrint('Error requesting SMS permission: $e');
      return PermissionStatus.denied;
    }
  }

  /// Request all contact-related permissions at once
  Future<Map<Permission, PermissionStatus>> requestAllContactPermissions() async {
    try {
      final permissions = [
        Permission.contacts,
        Permission.phone,
        Permission.sms,
      ];
      
      final statuses = await permissions.request();
      return statuses;
    } catch (e) {
      debugPrint('Error requesting all contact permissions: $e');
      return {};
    }
  }

  /// Check if permission is permanently denied
  bool isPermissionPermanentlyDenied(PermissionStatus status) {
    return status.isPermanentlyDenied;
  }

  /// Open app settings for permission management
  Future<bool> openAppSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      debugPrint('Error opening app settings: $e');
      return false;
    }
  }

  /// Get user-friendly permission status message
  String getPermissionStatusMessage(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Permission granted';
      case PermissionStatus.denied:
        return 'Permission denied. Please grant permission to continue.';
      case PermissionStatus.restricted:
        return 'Permission restricted by device policy.';
      case PermissionStatus.limited:
        return 'Limited permission granted.';
      case PermissionStatus.permanentlyDenied:
        return 'Permission permanently denied. Please enable in app settings.';
      case PermissionStatus.provisional:
        return 'Provisional permission granted.';
      default:
        return 'Unknown permission status.';
    }
  }

  /// Check if we can proceed with contact operations
  Future<bool> canAccessContacts() async {
    final hasPermission = await hasContactsPermission();
    if (hasPermission) return true;

    final status = await requestContactsPermission();
    return status.isGranted;
  }

  /// Check if we can make phone calls
  Future<bool> canMakePhoneCalls() async {
    final hasPermission = await hasPhonePermission();
    if (hasPermission) return true;

    final status = await requestPhonePermission();
    return status.isGranted;
  }

  /// Check if we can send SMS
  Future<bool> canSendSms() async {
    final hasPermission = await hasSmsPermission();
    if (hasPermission) return true;

    final status = await requestSmsPermission();
    return status.isGranted;
  }

  /// Get detailed permission info for debugging
  Future<Map<String, dynamic>> getPermissionInfo() async {
    try {
      final contactsStatus = await Permission.contacts.status;
      final phoneStatus = await Permission.phone.status;
      final smsStatus = await Permission.sms.status;

      return {
        'contacts': {
          'status': contactsStatus.toString(),
          'isGranted': contactsStatus.isGranted,
          'isDenied': contactsStatus.isDenied,
          'isPermanentlyDenied': contactsStatus.isPermanentlyDenied,
        },
        'phone': {
          'status': phoneStatus.toString(),
          'isGranted': phoneStatus.isGranted,
          'isDenied': phoneStatus.isDenied,
          'isPermanentlyDenied': phoneStatus.isPermanentlyDenied,
        },
        'sms': {
          'status': smsStatus.toString(),
          'isGranted': smsStatus.isGranted,
          'isDenied': smsStatus.isDenied,
          'isPermanentlyDenied': smsStatus.isPermanentlyDenied,
        },
      };
    } catch (e) {
      debugPrint('Error getting permission info: $e');
      return {};
    }
  }
}
