import 'package:equatable/equatable.dart';
import '../../models/notification_models.dart';

/// Base notification event
abstract class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object?> get props => [];
}

/// Load notifications
class LoadNotifications extends NotificationEvent {
  const LoadNotifications();
}

/// Schedule a new notification
class ScheduleNotification extends NotificationEvent {
  final String title;
  final String body;
  final NotificationType type;
  final DateTime scheduledTime;
  final NotificationPriority priority;
  final String? navigationRoute;
  final Map<String, dynamic>? navigationData;

  const ScheduleNotification({
    required this.title,
    required this.body,
    required this.type,
    required this.scheduledTime,
    this.priority = NotificationPriority.normal,
    this.navigationRoute,
    this.navigationData,
  });

  @override
  List<Object?> get props => [
    title,
    body,
    type,
    scheduledTime,
    priority,
    navigationRoute,
    navigationData,
  ];
}

/// Mark notification as read
class MarkNotificationAsRead extends NotificationEvent {
  final int notificationId;

  const MarkNotificationAsRead(this.notificationId);

  @override
  List<Object> get props => [notificationId];
}

/// Cancel notification
class CancelNotification extends NotificationEvent {
  final int notificationId;

  const CancelNotification(this.notificationId);

  @override
  List<Object> get props => [notificationId];
}

/// Load notification settings
class LoadNotificationSettings extends NotificationEvent {
  const LoadNotificationSettings();
}

/// Update notification settings
class UpdateNotificationSettings extends NotificationEvent {
  final NotificationSettings settings;

  const UpdateNotificationSettings(this.settings);

  @override
  List<Object> get props => [settings];
}

/// Update specific notification type setting
class UpdateNotificationTypeSetting extends NotificationEvent {
  final NotificationType type;
  final bool enabled;

  const UpdateNotificationTypeSetting({
    required this.type,
    required this.enabled,
  });

  @override
  List<Object> get props => [type, enabled];
}

/// Update quiet hours
class UpdateQuietHours extends NotificationEvent {
  final bool enabled;
  final int startHour;
  final int startMinute;
  final int endHour;
  final int endMinute;

  const UpdateQuietHours({
    required this.enabled,
    required this.startHour,
    required this.startMinute,
    required this.endHour,
    required this.endMinute,
  });

  @override
  List<Object> get props => [enabled, startHour, startMinute, endHour, endMinute];
}

/// Update notification frequency
class UpdateNotificationFrequency extends NotificationEvent {
  final String frequency;

  const UpdateNotificationFrequency(this.frequency);

  @override
  List<Object> get props => [frequency];
}

/// Clear all notifications
class ClearAllNotifications extends NotificationEvent {
  const ClearAllNotifications();
}

/// Sync notifications with backend
class SyncNotifications extends NotificationEvent {
  const SyncNotifications();
}

/// Handle notification tap
class HandleNotificationTap extends NotificationEvent {
  final int notificationId;
  final String? navigationRoute;
  final Map<String, dynamic>? navigationData;

  const HandleNotificationTap({
    required this.notificationId,
    this.navigationRoute,
    this.navigationData,
  });

  @override
  List<Object?> get props => [notificationId, navigationRoute, navigationData];
}

/// Request notification permissions
class RequestNotificationPermissions extends NotificationEvent {
  const RequestNotificationPermissions();
}

/// Initialize notification service
class InitializeNotificationService extends NotificationEvent {
  const InitializeNotificationService();
}

/// Schedule contextual AI notification
class ScheduleContextualNotification extends NotificationEvent {
  final String context;
  final String suggestion;
  final DateTime scheduledTime;

  const ScheduleContextualNotification({
    required this.context,
    required this.suggestion,
    required this.scheduledTime,
  });

  @override
  List<Object> get props => [context, suggestion, scheduledTime];
}

/// Schedule therapy reminder
class ScheduleTherapyReminder extends NotificationEvent {
  final DateTime sessionTime;
  final String? sessionType;
  final String? customMessage;

  const ScheduleTherapyReminder({
    required this.sessionTime,
    this.sessionType,
    this.customMessage,
  });

  @override
  List<Object?> get props => [sessionTime, sessionType, customMessage];
}

/// Schedule task reminder
class ScheduleTaskReminder extends NotificationEvent {
  final String taskId;
  final String taskTitle;
  final DateTime dueTime;
  final NotificationPriority priority;

  const ScheduleTaskReminder({
    required this.taskId,
    required this.taskTitle,
    required this.dueTime,
    this.priority = NotificationPriority.normal,
  });

  @override
  List<Object> get props => [taskId, taskTitle, dueTime, priority];
}

/// Schedule daily check-in reminder
class ScheduleDailyCheckIn extends NotificationEvent {
  final DateTime scheduledTime;
  final String? customMessage;

  const ScheduleDailyCheckIn({
    required this.scheduledTime,
    this.customMessage,
  });

  @override
  List<Object?> get props => [scheduledTime, customMessage];
}

/// Schedule weekly report notification
class ScheduleWeeklyReport extends NotificationEvent {
  final DateTime scheduledTime;
  final Map<String, dynamic> reportData;

  const ScheduleWeeklyReport({
    required this.scheduledTime,
    required this.reportData,
  });

  @override
  List<Object> get props => [scheduledTime, reportData];
}
