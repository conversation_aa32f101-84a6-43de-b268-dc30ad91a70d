/// Platform-aware Firebase service that handles compatibility issues
/// Uses conditional imports to avoid Firebase web compilation issues

import 'package:flutter/foundation.dart';

// Conditional imports based on platform
import 'firebase_service_mobile.dart' if (dart.library.html) 'firebase_service_web.dart';

class FirebaseService {
  static Future<void> initializeApp() async {
    if (kIsWeb) {
      // Skip Firebase initialization on web due to compatibility issues
      print('Firebase initialization skipped on web platform');
      return;
    }
    
    // Initialize Firebase on mobile platforms
    await FirebaseServiceImpl.initializeApp();
  }
  
  static bool get isSupported => !kIsWeb;
}
