import 'package:flutter/material.dart';
import 'dart:ui';
import '../../../utils/design_tokens.dart';
import '../models/captured_content.dart';

class ContentDetailModal extends StatefulWidget {
  final CapturedContent content;
  final VoidCallback onAddToNotes;
  final VoidCallback onChatWithDarvis;
  final VoidCallback onDiscard;

  const ContentDetailModal({
    super.key,
    required this.content,
    required this.onAddToNotes,
    required this.onChatWithDarvis,
    required this.onDiscard,
  });

  @override
  State<ContentDetailModal> createState() => _ContentDetailModalState();
}

class _ContentDetailModalState extends State<ContentDetailModal>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _animationController.forward();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _closeModal,
      child: Scaffold(
        backgroundColor: Colors.black.withValues(alpha: 0.5),
        body: Stack(
          children: [
            Positioned.fill(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                child: Container(
                  color: Colors.black.withValues(alpha: 0.3),
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: GestureDetector(
                    onTap: () {}, // Prevent closing when tapping inside
                    child: _buildModalContent(),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModalContent() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.75,
      decoration: const BoxDecoration(
        color: DesignTokens.backgroundApp,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(DesignTokens.borderRadiusXl),
          topRight: Radius.circular(DesignTokens.borderRadiusXl),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(DesignTokens.spacingLg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildContentPreview(),
                  const SizedBox(height: DesignTokens.spacingXl),
                  _buildFullSummary(),
                  const SizedBox(height: DesignTokens.spacingXl),
                  _buildTagSection(),
                ],
              ),
            ),
          ),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingLg),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Color(0xFF333333),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              widget.content.title,
              style: DesignTokens.cardTitleStyle.copyWith(
                fontSize: 20,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            onPressed: _closeModal,
            icon: const Icon(
              Icons.close,
              color: DesignTokens.iconPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentPreview() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: DesignTokens.backgroundAccentCard.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
                    ),
                    child: Center(
                      child: widget.content.faviconUrl != null
                          ? Text(
                              widget.content.faviconUrl!,
                              style: const TextStyle(fontSize: 24),
                            )
                          : Icon(
                              widget.content.type == ContentType.image 
                                  ? Icons.image 
                                  : Icons.link,
                              color: DesignTokens.iconSecondary,
                              size: 24,
                            ),
                    ),
                  ),
                  const SizedBox(width: DesignTokens.spacingMd),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Original Link',
                          style: DesignTokens.bodyStyle.copyWith(
                            color: DesignTokens.textMuted,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: DesignTokens.spacingXs),
                        Text(
                          widget.content.url,
                          style: DesignTokens.bodyStyle.copyWith(
                            color: DesignTokens.primaryInteractiveBlue,
                            fontSize: 12,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFullSummary() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'AI Summary',
          style: DesignTokens.cardTitleStyle.copyWith(
            fontSize: 16,
          ),
        ),
        const SizedBox(height: DesignTokens.spacingSm),
        Container(
          width: double.infinity,
          height: 150, // Fixed height for scrolling
          padding: const EdgeInsets.all(DesignTokens.spacingMd),
          decoration: BoxDecoration(
            color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.08),
              width: 1,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: SingleChildScrollView(
                child: Text(
                  widget.content.summary.isNotEmpty
                      ? widget.content.summary
                      : 'No summary available for this content.',
                  style: DesignTokens.bodyStyle.copyWith(
                    color: DesignTokens.textSecondary,
                    height: 1.5,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTagSection() {
    return Row(
      children: [
        Text(
          'Tag: ',
          style: DesignTokens.cardTitleStyle.copyWith(
            fontSize: 16,
          ),
        ),
        if (widget.content.tag.isNotEmpty)
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingSm,
            ),
            decoration: BoxDecoration(
              color: DesignTokens.primaryAccentBlue.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              border: Border.all(
                color: DesignTokens.primaryAccentBlue.withValues(alpha: 0.5),
                width: 1,
              ),
            ),
            child: Text(
              '#${widget.content.tag.toLowerCase()}',
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.primaryAccentBlue,
                fontWeight: FontWeight.w600,
              ),
            ),
          )
        else
          Text(
            'No tag assigned',
            style: DesignTokens.bodyStyle.copyWith(
              color: DesignTokens.textMuted,
            ),
          ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingLg),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Color(0xFF333333),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  'Add to Notes',
                  Icons.note_add,
                  DesignTokens.primaryInteractiveBlue,
                  widget.onAddToNotes,
                ),
              ),
              const SizedBox(width: DesignTokens.spacingMd),
              Expanded(
                child: _buildActionButtonWithImage(
                  'Chat with Darvis',
                  'assets/images/darvisintelligence.png',
                  DesignTokens.primaryAccentBlue,
                  widget.onChatWithDarvis,
                ),
              ),
            ],
          ),
          const SizedBox(height: DesignTokens.spacingMd),
          SizedBox(
            width: double.infinity,
            child: _buildActionButton(
              'Discard',
              Icons.delete_outline,
              DesignTokens.priorityHigh,
              widget.onDiscard,
              isDestructive: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed, {
    bool isDestructive = false,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: isDestructive ? color : Colors.white),
      label: Text(
        label,
        style: DesignTokens.bodyStyle.copyWith(
          color: isDestructive ? color : Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: isDestructive
            ? color.withValues(alpha: 0.2)
            : color,
        padding: const EdgeInsets.symmetric(
          vertical: DesignTokens.spacingMd,
          horizontal: DesignTokens.spacingLg,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          side: isDestructive
              ? BorderSide(color: color, width: 1)
              : BorderSide.none,
        ),
      ),
    );
  }

  Widget _buildActionButtonWithImage(
    String label,
    String imagePath,
    Color color,
    VoidCallback onPressed, {
    bool isDestructive = false,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isDestructive
            ? color.withValues(alpha: 0.2)
            : color,
        padding: const EdgeInsets.symmetric(
          vertical: DesignTokens.spacingMd,
          horizontal: DesignTokens.spacingLg,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          side: isDestructive
              ? BorderSide(color: color, width: 1)
              : BorderSide.none,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            imagePath,
            width: 20,
            height: 20,
            color: isDestructive ? color : Colors.white,
          ),
          const SizedBox(width: DesignTokens.spacingSm),
          Flexible(
            child: Text(
              label,
              style: DesignTokens.bodyStyle.copyWith(
                color: isDestructive ? color : Colors.white,
                fontWeight: FontWeight.w600,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  void _closeModal() {
    _animationController.reverse().then((_) {
      Navigator.of(context).pop();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}
