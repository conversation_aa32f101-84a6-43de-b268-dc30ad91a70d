import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import '../../utils/design_tokens.dart';

class ContactUsScreen extends StatefulWidget {
  const ContactUsScreen({super.key});

  @override
  State<ContactUsScreen> createState() => _ContactUsScreenState();
}

class _ContactUsScreenState extends State<ContactUsScreen>
    with TickerProviderStateMixin {
  
  late AnimationController _pageAnimationController;
  late Animation<Offset> _pageSlideAnimation;
  
  // Form controllers
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _subjectController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();
  
  // Form state
  String _selectedCategory = 'General Inquiry';
  final List<String> _categories = [
    'General Inquiry',
    'Technical Support',
    'Bug Report',
    'Feature Request',
    'Account Issues',
    'Privacy Concerns',
    'Billing Questions',
    'Feedback',
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 350),
      vsync: this,
    );
    
    _pageSlideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _pageAnimationController,
      curve: Curves.easeOut,
    ));
    
    // Delay animation start until after page route transition completes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _pageAnimationController.forward();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: SafeArea(
        child: SlideTransition(
          position: _pageSlideAnimation,
          child: CustomScrollView(
            physics: const ClampingScrollPhysics(),
            slivers: [
              _buildHeader(),
              _buildContactForm(),
              _buildQuickContactSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return SliverToBoxAdapter(
      child: Container(
        height: 80,
        padding: const EdgeInsets.fromLTRB(
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingSm,
        ),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                Icons.arrow_back,
                color: DesignTokens.iconPrimary,
              ),
            ),
            Expanded(
              child: Center(
                child: ShaderMask(
                  shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
                  child: Text(
                    'Contact Us',
                    style: DesignTokens.therapySessionTitleStyle.copyWith(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 48), // Balance the back button
          ],
        ),
      ),
    );
  }

  Widget _buildContactForm() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Container(
          padding: const EdgeInsets.all(DesignTokens.spacingLg),
          decoration: BoxDecoration(
            color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.08),
              width: 1,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Send us a message',
                    style: DesignTokens.cardTitleStyle.copyWith(fontSize: 20),
                  ),
                  const SizedBox(height: DesignTokens.spacingSm),
                  Text(
                    'We\'d love to hear from you. Send us a message and we\'ll respond as soon as possible.',
                    style: DesignTokens.bodyStyle.copyWith(
                      color: DesignTokens.textSecondary,
                    ),
                  ),
                  const SizedBox(height: DesignTokens.spacingLg),
                  
                  // Name field
                  _buildInputField(
                    'Name',
                    _nameController,
                    'Your full name',
                  ),
                  
                  const SizedBox(height: DesignTokens.spacingMd),
                  
                  // Email field
                  _buildInputField(
                    'Email',
                    _emailController,
                    '<EMAIL>',
                    keyboardType: TextInputType.emailAddress,
                  ),
                  
                  const SizedBox(height: DesignTokens.spacingMd),
                  
                  // Category dropdown
                  _buildDropdownField(
                    'Category',
                    _selectedCategory,
                    _categories,
                    (value) {
                      setState(() {
                        _selectedCategory = value!;
                      });
                    },
                  ),
                  
                  const SizedBox(height: DesignTokens.spacingMd),
                  
                  // Subject field
                  _buildInputField(
                    'Subject',
                    _subjectController,
                    'Brief description of your inquiry',
                  ),
                  
                  const SizedBox(height: DesignTokens.spacingMd),
                  
                  // Message field
                  _buildInputField(
                    'Message',
                    _messageController,
                    'Please provide details about your inquiry...',
                    maxLines: 5,
                  ),
                  
                  const SizedBox(height: DesignTokens.spacingLg),
                  
                  // Send button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _sendMessage,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: DesignTokens.primaryInteractiveBlue,
                        padding: const EdgeInsets.symmetric(
                          vertical: DesignTokens.spacingMd,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.send,
                            color: Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: DesignTokens.spacingSm),
                          Text(
                            'Send Message',
                            style: DesignTokens.bodyStyle.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickContactSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Other ways to reach us',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            
            _buildContactItem(
              'Email Support',
              '<EMAIL>',
              Icons.email_outlined,
              () => _showComingSoon('Email Support'),
            ),
            
            _buildContactItem(
              'Live Chat',
              'Available 24/7 for immediate assistance',
              Icons.chat_outlined,
              () => _showComingSoon('Live Chat'),
            ),
            
            _buildContactItem(
              'Community Forum',
              'Join our community discussions',
              Icons.forum_outlined,
              () => _showComingSoon('Community Forum'),
            ),
            
            _buildContactItem(
              'Documentation',
              'Browse our help documentation',
              Icons.help_outline,
              () => _showComingSoon('Documentation'),
            ),
            
            const SizedBox(height: DesignTokens.spacingXl),
          ],
        ),
      ),
    );
  }

  Widget _buildInputField(String label, TextEditingController controller, String hint, {TextInputType? keyboardType, int maxLines = 1}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: DesignTokens.bodyStyle.copyWith(
            fontWeight: FontWeight.w600,
            color: DesignTokens.textPrimary,
          ),
        ),
        const SizedBox(height: DesignTokens.spacingSm),
        Container(
          decoration: BoxDecoration(
            color: DesignTokens.backgroundTextInput,
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusXl),
          ),
          child: TextField(
            controller: controller,
            keyboardType: keyboardType,
            maxLines: maxLines,
            style: DesignTokens.bodyStyle.copyWith(
              color: DesignTokens.textOnLightBackground,
            ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textOnLightBackground.withValues(alpha: 0.6),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusXl),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusXl),
                borderSide: const BorderSide(
                  color: DesignTokens.primaryInteractiveBlue,
                  width: 2,
                ),
              ),
              contentPadding: const EdgeInsets.all(DesignTokens.spacingMd),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField(String label, String value, List<String> options, Function(String?) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: DesignTokens.bodyStyle.copyWith(
            fontWeight: FontWeight.w600,
            color: DesignTokens.textPrimary,
          ),
        ),
        const SizedBox(height: DesignTokens.spacingSm),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
          decoration: BoxDecoration(
            color: DesignTokens.backgroundCard,
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
            border: Border.all(
              color: DesignTokens.borderInteractiveElement,
              width: 1,
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              isExpanded: true,
              icon: const Icon(Icons.keyboard_arrow_down, color: DesignTokens.iconPrimary),
              style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
              dropdownColor: DesignTokens.backgroundCard,
              onChanged: onChanged,
              selectedItemBuilder: (context) {
                return options.map((String option) {
                  return Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      option,
                      style: DesignTokens.bodyStyle.copyWith(
                        color: DesignTokens.primaryInteractiveBlue,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  );
                }).toList();
              },
              items: options.map((String option) {
                return DropdownMenuItem<String>(
                  value: option,
                  child: Text(option),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContactItem(String title, String subtitle, IconData icon, VoidCallback onTap) {
    return Container(
      margin: const EdgeInsets.only(bottom: DesignTokens.spacingSm),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          child: Container(
            padding: const EdgeInsets.all(DesignTokens.spacingMd),
            decoration: BoxDecoration(
              color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.08),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Padding(
                  padding: const EdgeInsets.all(DesignTokens.spacingSm),
                  child: Row(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                        ),
                        child: Icon(
                          icon,
                          color: DesignTokens.primaryInteractiveBlue,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: DesignTokens.spacingMd),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              title,
                              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 16),
                            ),
                            const SizedBox(height: DesignTokens.spacingXs),
                            Text(
                              subtitle,
                              style: DesignTokens.bodyStyle.copyWith(
                                color: DesignTokens.textSecondary,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Icon(
                        Icons.chevron_right,
                        color: DesignTokens.iconSecondary,
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _sendMessage() {
    if (_nameController.text.isEmpty ||
        _emailController.text.isEmpty ||
        _subjectController.text.isEmpty ||
        _messageController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Please fill in all required fields'),
          backgroundColor: DesignTokens.priorityHigh.withValues(alpha: 0.9),
          duration: const Duration(seconds: 3),
        ),
      );
      return;
    }

    // Simulate sending message
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Message sent successfully! We\'ll get back to you soon.'),
        backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
        duration: const Duration(seconds: 3),
      ),
    );

    // Clear form
    _nameController.clear();
    _emailController.clear();
    _subjectController.clear();
    _messageController.clear();
    setState(() {
      _selectedCategory = 'General Inquiry';
    });
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature coming soon!'),
        backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  void dispose() {
    _pageAnimationController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _subjectController.dispose();
    _messageController.dispose();
    super.dispose();
  }
}
