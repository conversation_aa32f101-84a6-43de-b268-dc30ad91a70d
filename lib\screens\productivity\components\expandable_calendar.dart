import 'package:flutter/material.dart';
import 'dart:ui';
import '../../../utils/design_tokens.dart';

class ExpandableCalendar extends StatefulWidget {
  final bool isExpanded;
  final DateTime selectedDate;
  final VoidCallback onToggleExpanded;
  final Function(DateTime) onDateSelected;

  const ExpandableCalendar({
    super.key,
    required this.isExpanded,
    required this.selectedDate,
    required this.onToggleExpanded,
    required this.onDateSelected,
  });

  @override
  State<ExpandableCalendar> createState() => _ExpandableCalendarState();
}

class _ExpandableCalendarState extends State<ExpandableCalendar>
    with TickerProviderStateMixin {
  late AnimationController _expansionController;
  late Animation<double> _expansionAnimation;
  late PageController _pageController;
  
  DateTime _currentMonth = DateTime.now();

  @override
  void initState() {
    super.initState();
    _expansionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _expansionAnimation = CurvedAnimation(
      parent: _expansionController,
      curve: Curves.easeInOut,
    );
    
    _pageController = PageController(
      initialPage: _getMonthIndex(_currentMonth),
    );
    
    if (widget.isExpanded) {
      _expansionController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(ExpandableCalendar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isExpanded != oldWidget.isExpanded) {
      if (widget.isExpanded) {
        _expansionController.forward();
      } else {
        _expansionController.reverse();
      }
    }
  }

  int _getMonthIndex(DateTime month) {
    final now = DateTime.now();
    return (month.year - now.year) * 12 + (month.month - now.month) + 12;
  }

  DateTime _getMonthFromIndex(int index) {
    final now = DateTime.now();
    final monthsFromNow = index - 12;
    return DateTime(now.year, now.month + monthsFromNow);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: DesignTokens.backgroundCard.withOpacity(0.3),
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
              border: Border.all(
                color: Colors.white.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                _buildHeader(),
                AnimatedBuilder(
                  animation: _expansionAnimation,
                  builder: (context, child) {
                    return SizeTransition(
                      sizeFactor: _expansionAnimation,
                      child: _buildCalendarGrid(),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return GestureDetector(
      onTap: widget.onToggleExpanded,
      child: Container(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Row(
          children: [
            IconButton(
              icon: const Icon(Icons.chevron_left, color: DesignTokens.iconPrimary),
              onPressed: _previousMonth,
            ),
            Expanded(
              child: Text(
                _getMonthYearLabel(_currentMonth),
                style: DesignTokens.cardTitleStyle,
                textAlign: TextAlign.center,
              ),
            ),
            IconButton(
              icon: const Icon(Icons.chevron_right, color: DesignTokens.iconPrimary),
              onPressed: _nextMonth,
            ),
            const SizedBox(width: DesignTokens.spacingSm),
            AnimatedRotation(
              turns: widget.isExpanded ? 0.5 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: const Icon(
                Icons.keyboard_arrow_down,
                color: DesignTokens.iconSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalendarGrid() {
    return Container(
      padding: const EdgeInsets.only(
        left: DesignTokens.spacingMd,
        right: DesignTokens.spacingMd,
        bottom: DesignTokens.spacingMd,
      ),
      child: Column(
        children: [
          _buildWeekdayHeaders(),
          const SizedBox(height: DesignTokens.spacingSm),
          _buildDaysGrid(),
        ],
      ),
    );
  }

  Widget _buildWeekdayHeaders() {
    const weekdays = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];
    
    return Row(
      children: weekdays.map((day) {
        return Expanded(
          child: Text(
            day,
            style: DesignTokens.bodyStyle.copyWith(
              color: DesignTokens.textMuted,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDaysGrid() {
    final firstDayOfMonth = DateTime(_currentMonth.year, _currentMonth.month, 1);
    final lastDayOfMonth = DateTime(_currentMonth.year, _currentMonth.month + 1, 0);
    final firstWeekday = firstDayOfMonth.weekday;
    final daysInMonth = lastDayOfMonth.day;
    
    final List<Widget> dayWidgets = [];
    
    // Add empty cells for days before the first day of the month
    for (int i = 1; i < firstWeekday; i++) {
      dayWidgets.add(const SizedBox());
    }
    
    // Add day cells
    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(_currentMonth.year, _currentMonth.month, day);
      dayWidgets.add(_buildDayCell(date));
    }
    
    // Create rows of 7 days each
    final List<Widget> rows = [];
    for (int i = 0; i < dayWidgets.length; i += 7) {
      final rowWidgets = dayWidgets.skip(i).take(7).toList();
      while (rowWidgets.length < 7) {
        rowWidgets.add(const SizedBox());
      }
      rows.add(
        Row(
          children: rowWidgets.map((widget) => Expanded(child: widget)).toList(),
        ),
      );
    }
    
    return Column(
      children: rows.map((row) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: row,
        );
      }).toList(),
    );
  }

  Widget _buildDayCell(DateTime date) {
    final isSelected = _isSameDay(date, widget.selectedDate);
    final isToday = _isSameDay(date, DateTime.now());
    final isCurrentMonth = date.month == _currentMonth.month;
    
    return GestureDetector(
      onTap: () => widget.onDateSelected(date),
      child: Container(
        height: 40,
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: isSelected 
              ? DesignTokens.primaryInteractiveBlue
              : isToday 
                  ? DesignTokens.primaryInteractiveBlue.withOpacity(0.3)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
          border: isToday && !isSelected
              ? Border.all(
                  color: DesignTokens.primaryInteractiveBlue,
                  width: 1,
                )
              : null,
        ),
        child: Center(
          child: Text(
            date.day.toString(),
            style: DesignTokens.bodyStyle.copyWith(
              color: isSelected
                  ? DesignTokens.textPrimary
                  : isCurrentMonth
                      ? DesignTokens.textPrimary
                      : DesignTokens.textMuted,
              fontWeight: isSelected || isToday 
                  ? FontWeight.w600 
                  : FontWeight.w400,
            ),
          ),
        ),
      ),
    );
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  String _getMonthYearLabel(DateTime date) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  void _previousMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
    });
  }

  void _nextMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
    });
  }

  @override
  void dispose() {
    _expansionController.dispose();
    _pageController.dispose();
    super.dispose();
  }
}
