import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:isar/isar.dart';
import 'package:timezone/timezone.dart' as tz;
import '../models/notification_models.dart';
import 'api_service.dart';

/// Comprehensive notification service handling local and remote notifications
class NotificationService {
  final FlutterLocalNotificationsPlugin _localNotifications;
  final Isar _isar;
  final ApiService _apiService;
  
  static const String _channelId = 'drix_notifications';
  static const String _channelName = 'Drix Notifications';
  static const String _channelDescription = 'Notifications from Drix AI Assistant';

  NotificationService({
    required FlutterLocalNotificationsPlugin localNotifications,
    required Isar isar,
    required ApiService apiService,
  }) : _localNotifications = localNotifications,
       _isar = isar,
       _apiService = apiService;

  /// Initialize notification service
  Future<void> initialize() async {
    try {
      // Android initialization
      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
      
      // iOS initialization
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );
      
      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );
      
      await _localNotifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );
      
      // Create notification channel for Android
      await _createNotificationChannel();
      
      // Request permissions
      await _requestPermissions();
      
      print('✅ Notification service initialized successfully');
    } catch (e) {
      print('❌ Failed to initialize notification service: $e');
    }
  }

  /// Create notification channel for Android
  Future<void> _createNotificationChannel() async {
    const androidChannel = AndroidNotificationChannel(
      _channelId,
      _channelName,
      description: _channelDescription,
      importance: Importance.high,
      enableVibration: true,
      playSound: true,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidChannel);
  }

  /// Request notification permissions
  Future<bool> _requestPermissions() async {
    try {
      // iOS permissions
      final iosPlugin = _localNotifications
          .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>();
      
      if (iosPlugin != null) {
        final granted = await iosPlugin.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
        return granted ?? false;
      }
      
      // Android permissions (handled automatically in newer versions)
      final androidPlugin = _localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
      
      if (androidPlugin != null) {
        final granted = await androidPlugin.requestNotificationsPermission();
        return granted ?? false;
      }
      
      return true;
    } catch (e) {
      print('❌ Failed to request notification permissions: $e');
      return false;
    }
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) async {
    try {
      final payload = response.payload;
      if (payload == null) return;
      
      final data = jsonDecode(payload) as Map<String, dynamic>;
      final notificationId = data['notification_id'] as String?;
      
      if (notificationId != null) {
        // Mark notification as interacted
        await _markNotificationAsInteracted(notificationId);
        
        // Handle navigation
        final route = data['navigation_route'] as String?;
        final navigationData = data['navigation_data'] as Map<String, dynamic>?;
        
        if (route != null) {
          await _handleNotificationNavigation(route, navigationData);
        }
      }
    } catch (e) {
      print('❌ Error handling notification tap: $e');
    }
  }

  /// Schedule a local notification
  Future<void> scheduleNotification({
    required String title,
    required String body,
    required NotificationType type,
    required DateTime scheduledTime,
    NotificationPriority priority = NotificationPriority.normal,
    String? navigationRoute,
    Map<String, dynamic>? navigationData,
  }) async {
    try {
      // Check if notifications are enabled for this type
      final settings = await getNotificationSettings();
      if (!_isNotificationTypeEnabled(type, settings)) {
        print('⚠️ Notifications disabled for type: ${type.name}');
        return;
      }
      
      // Check quiet hours
      if (_isInQuietHours(scheduledTime, settings)) {
        print('⚠️ Notification scheduled during quiet hours, skipping');
        return;
      }
      
      // Create local notification record
      final notification = LocalNotification()
        ..title = title
        ..body = body
        ..type = type
        ..priority = priority
        ..scheduledTime = scheduledTime
        ..createdAt = DateTime.now()
        ..navigationRoute = navigationRoute
        ..navigationData = navigationData != null ? jsonEncode(navigationData) : null
        ..needsSync = true
        ..syncStatus = 'pending';
      
      // Save to local database
      await _isar.writeTxn(() async {
        await _isar.localNotifications.put(notification);
      });
      
      // Schedule local notification
      await _scheduleLocalNotification(notification);
      
      // Sync with backend
      await _syncNotificationToBackend(notification);
      
      print('✅ Notification scheduled: $title');
    } catch (e) {
      print('❌ Failed to schedule notification: $e');
    }
  }

  /// Schedule local notification with Flutter Local Notifications
  Future<void> _scheduleLocalNotification(LocalNotification notification) async {
    try {
      final payload = jsonEncode({
        'notification_id': notification.id.toString(),
        'navigation_route': notification.navigationRoute,
        'navigation_data': notification.navigationData != null 
            ? jsonDecode(notification.navigationData!) 
            : null,
      });
      
      final androidDetails = AndroidNotificationDetails(
        _channelId,
        _channelName,
        channelDescription: _channelDescription,
        importance: _getAndroidImportance(notification.priority),
        priority: _getAndroidPriority(notification.priority),
        enableVibration: true,
        playSound: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );
      
      await _localNotifications.zonedSchedule(
        notification.id,
        notification.title,
        notification.body,
        tz.TZDateTime.from(notification.scheduledTime, tz.local),
        notificationDetails,
        payload: payload,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      );
    } catch (e) {
      print('❌ Failed to schedule local notification: $e');
    }
  }

  /// Get notification settings
  Future<NotificationSettings> getNotificationSettings() async {
    try {
      final settings = await _isar.notificationSettings.where().findFirst();
      return settings ?? NotificationSettings()
        ..updatedAt = DateTime.now()
        ..needsSync = false;
    } catch (e) {
      print('❌ Failed to get notification settings: $e');
      return NotificationSettings()
        ..updatedAt = DateTime.now()
        ..needsSync = false;
    }
  }

  /// Update notification settings
  Future<void> updateNotificationSettings(NotificationSettings settings) async {
    try {
      settings.updatedAt = DateTime.now();
      settings.needsSync = true;
      
      await _isar.writeTxn(() async {
        await _isar.notificationSettings.put(settings);
      });
      
      // Sync with backend
      await _syncSettingsToBackend(settings);
      
      print('✅ Notification settings updated');
    } catch (e) {
      print('❌ Failed to update notification settings: $e');
    }
  }

  /// Get all notifications
  Future<List<LocalNotification>> getAllNotifications() async {
    try {
      return await _isar.localNotifications
          .where()
          .sortByCreatedAtDesc()
          .findAll();
    } catch (e) {
      print('❌ Failed to get notifications: $e');
      return [];
    }
  }

  /// Mark notification as read
  Future<void> markAsRead(int notificationId) async {
    try {
      final notification = await _isar.localNotifications.get(notificationId);
      if (notification != null) {
        notification.isRead = true;
        notification.needsSync = true;
        
        await _isar.writeTxn(() async {
          await _isar.localNotifications.put(notification);
        });
        
        // Track analytics
        await _trackNotificationAnalytics(notification, 'read');
      }
    } catch (e) {
      print('❌ Failed to mark notification as read: $e');
    }
  }

  /// Cancel notification
  Future<void> cancelNotification(int notificationId) async {
    try {
      await _localNotifications.cancel(notificationId);
      
      await _isar.writeTxn(() async {
        await _isar.localNotifications.delete(notificationId);
      });
      
      print('✅ Notification cancelled: $notificationId');
    } catch (e) {
      print('❌ Failed to cancel notification: $e');
    }
  }

  /// Helper methods
  bool _isNotificationTypeEnabled(NotificationType type, NotificationSettings settings) {
    switch (type) {
      case NotificationType.therapyReminder:
        return settings.therapyReminders;
      case NotificationType.taskNotification:
        return settings.taskNotifications;
      case NotificationType.dailyCheckIn:
        return settings.dailyCheckIns;
      case NotificationType.weeklyReport:
        return settings.weeklyReports;
      case NotificationType.systemUpdate:
        return settings.systemUpdates;
      case NotificationType.contextualAI:
        return settings.contextualAI;
    }
  }

  bool _isInQuietHours(DateTime scheduledTime, NotificationSettings settings) {
    if (!settings.quietHoursEnabled) return false;
    
    final hour = scheduledTime.hour;
    final minute = scheduledTime.minute;
    final timeInMinutes = hour * 60 + minute;
    
    final startMinutes = settings.quietHoursStartHour * 60 + settings.quietHoursStartMinute;
    final endMinutes = settings.quietHoursEndHour * 60 + settings.quietHoursEndMinute;
    
    if (startMinutes <= endMinutes) {
      return timeInMinutes >= startMinutes && timeInMinutes <= endMinutes;
    } else {
      return timeInMinutes >= startMinutes || timeInMinutes <= endMinutes;
    }
  }

  Importance _getAndroidImportance(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Importance.low;
      case NotificationPriority.normal:
        return Importance.defaultImportance;
      case NotificationPriority.high:
        return Importance.high;
      case NotificationPriority.urgent:
        return Importance.max;
    }
  }

  Priority _getAndroidPriority(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Priority.low;
      case NotificationPriority.normal:
        return Priority.defaultPriority;
      case NotificationPriority.high:
        return Priority.high;
      case NotificationPriority.urgent:
        return Priority.max;
    }
  }

  // Backend sync methods
  Future<void> _syncNotificationToBackend(LocalNotification notification) async {
    try {
      final dto = NotificationDto(
        title: notification.title,
        body: notification.body,
        type: notification.type,
        priority: notification.priority,
        scheduledTime: notification.scheduledTime,
        navigationRoute: notification.navigationRoute,
        navigationData: notification.navigationData != null
            ? jsonDecode(notification.navigationData!)
            : null,
      );

      await _apiService.createNotification(dto);

      notification.syncStatus = 'synced';
      notification.needsSync = false;

      await _isar.writeTxn(() async {
        await _isar.localNotifications.put(notification);
      });
    } catch (e) {
      print('❌ Failed to sync notification to backend: $e');
      notification.syncStatus = 'error';
      await _isar.writeTxn(() async {
        await _isar.localNotifications.put(notification);
      });
    }
  }

  Future<void> _syncSettingsToBackend(NotificationSettings settings) async {
    try {
      final dto = NotificationSettingsDto(
        therapyReminders: settings.therapyReminders,
        taskNotifications: settings.taskNotifications,
        dailyCheckIns: settings.dailyCheckIns,
        weeklyReports: settings.weeklyReports,
        systemUpdates: settings.systemUpdates,
        contextualAI: settings.contextualAI,
        quietHoursEnabled: settings.quietHoursEnabled,
        quietHoursStartHour: settings.quietHoursStartHour,
        quietHoursStartMinute: settings.quietHoursStartMinute,
        quietHoursEndHour: settings.quietHoursEndHour,
        quietHoursEndMinute: settings.quietHoursEndMinute,
        notificationFrequency: settings.notificationFrequency,
        soundEnabled: settings.soundEnabled,
        vibrationEnabled: settings.vibrationEnabled,
        badgeCountEnabled: settings.badgeCountEnabled,
      );

      await _apiService.updateNotificationSettings(dto);

      settings.needsSync = false;
      await _isar.writeTxn(() async {
        await _isar.notificationSettings.put(settings);
      });
    } catch (e) {
      print('❌ Failed to sync notification settings to backend: $e');
    }
  }

  Future<void> _markNotificationAsInteracted(String notificationId) async {
    try {
      final id = int.tryParse(notificationId);
      if (id != null) {
        await markAsRead(id);
        await _apiService.markNotificationAsInteracted(notificationId);
      }
    } catch (e) {
      print('❌ Failed to mark notification as interacted: $e');
    }
  }

  Future<void> _handleNotificationNavigation(String route, Map<String, dynamic>? data) async {
    // This will be implemented when navigation service is integrated
    print('🔄 Navigation requested: $route with data: $data');
  }

  Future<void> _trackNotificationAnalytics(LocalNotification notification, String action) async {
    try {
      final analytics = NotificationAnalytics()
        ..notificationId = notification.id.toString()
        ..type = notification.type
        ..deliveredAt = notification.createdAt
        ..createdAt = DateTime.now()
        ..needsSync = true;

      switch (action) {
        case 'read':
          analytics.readAt = DateTime.now();
          break;
        case 'interact':
          analytics.interactedAt = DateTime.now();
          break;
        case 'dismiss':
          analytics.dismissedAt = DateTime.now();
          break;
      }

      await _isar.writeTxn(() async {
        await _isar.notificationAnalytics.put(analytics);
      });
    } catch (e) {
      print('❌ Failed to track notification analytics: $e');
    }
  }
}
