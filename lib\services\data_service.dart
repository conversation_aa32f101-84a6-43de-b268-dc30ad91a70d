import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'api_service.dart';

// Contact Model - Updated to match backend API structure
class Contact {
  final String id;
  final String name;
  final String phone;
  final String email;
  final String location;
  final String? metAt;
  final Map<String, String> socialMedia;
  final String memoryPrompt;
  final String? imagePath;
  final String? imagePublicId;
  final String deviceSyncStatus;
  final DateTime createdAt;
  final DateTime updatedAt;

  Contact({
    required this.id,
    required this.name,
    required this.phone,
    this.email = '',
    this.location = '',
    this.metAt,
    this.socialMedia = const {},
    this.memoryPrompt = '',
    this.imagePath,
    this.imagePublicId,
    this.deviceSyncStatus = 'disabled',
    required this.createdAt,
    DateTime? updatedAt,
  }) : updatedAt = updatedAt ?? createdAt;

  // Helper getters for backward compatibility
  DateTime get dateAdded => createdAt;
  String get socialPlatform => socialMedia.keys.isNotEmpty ? socialMedia.keys.first : '';
  String get socialUsername => socialMedia.values.isNotEmpty ? socialMedia.values.first : '';
  String get notes => memoryPrompt;

  Contact copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    String? location,
    String? metAt,
    Map<String, String>? socialMedia,
    String? memoryPrompt,
    String? imagePath,
    String? imagePublicId,
    String? deviceSyncStatus,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Contact(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      location: location ?? this.location,
      metAt: metAt ?? this.metAt,
      socialMedia: socialMedia ?? this.socialMedia,
      memoryPrompt: memoryPrompt ?? this.memoryPrompt,
      imagePath: imagePath ?? this.imagePath,
      imagePublicId: imagePublicId ?? this.imagePublicId,
      deviceSyncStatus: deviceSyncStatus ?? this.deviceSyncStatus,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Factory constructor to create Contact from API response
  factory Contact.fromApi(Map<String, dynamic> json) {
    return Contact(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      location: json['location'] ?? '',
      metAt: json['met_at'],
      socialMedia: Map<String, String>.from(json['social_media'] ?? {}),
      memoryPrompt: json['memory_prompt'] ?? '',
      imagePath: json['image_path'],
      imagePublicId: json['image_public_id'],
      deviceSyncStatus: json['device_sync_status'] ?? 'disabled',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  // Convert Contact to API request format
  Map<String, dynamic> toApi() {
    return {
      'name': name,
      'phone': phone,
      'email': email.isNotEmpty ? email : null,
      'location': location.isNotEmpty ? location : null,
      'met_at': metAt,
      'social_media': socialMedia.isNotEmpty ? socialMedia : null,
      'memory_prompt': memoryPrompt.isNotEmpty ? memoryPrompt : null,
      'image_path': imagePath,
      'image_public_id': imagePublicId,
      'device_sync_status': deviceSyncStatus,
    };
  }
}

// Event Model
class Event {
  final String id;
  final String title;
  final String time;
  final DateTime date;
  final DateTime createdAt;

  Event({
    required this.id,
    required this.title,
    required this.time,
    required this.date,
    required this.createdAt,
  });

  Event copyWith({
    String? id,
    String? title,
    String? time,
    DateTime? date,
    DateTime? createdAt,
  }) {
    return Event(
      id: id ?? this.id,
      title: title ?? this.title,
      time: time ?? this.time,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

// Data Service for managing contacts and events
class DataService extends ChangeNotifier {
  static final DataService _instance = DataService._internal();
  factory DataService() => _instance;
  DataService._internal();

  // Get API service instance
  ApiService get _apiService => GetIt.instance<ApiService>();

  // Contact data storage (cache)
  List<Contact> _contacts = [];
  bool _contactsLoaded = false;

  // Event data storage
  final Map<DateTime, List<Event>> _events = {
    DateTime.utc(DateTime.now().year, DateTime.now().month, DateTime.now().day): [
      Event(
        id: '1',
        title: 'Team Standup',
        time: '10:00 AM',
        date: DateTime.now(),
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      Event(
        id: '2',
        title: 'Design Review',
        time: '1:00 PM',
        date: DateTime.now(),
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
    ],
    DateTime.utc(DateTime.now().year, DateTime.now().month, DateTime.now().day + 2): [
      Event(
        id: '3',
        title: 'Project Kickoff',
        time: '11:00 AM',
        date: DateTime.now().add(const Duration(days: 2)),
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
      ),
    ],
  };

  // Contact methods - Backend integrated
  List<Contact> get contacts => List.unmodifiable(_contacts);

  /// Load contacts from backend API
  Future<void> loadContacts({String? search, bool forceRefresh = false}) async {
    if (_contactsLoaded && !forceRefresh && search == null) {
      return; // Use cached data
    }

    try {
      final response = await _apiService.getContacts(
        search: search,
        limit: 100, // Load more contacts for better UX
        sortBy: 'name',
        sortOrder: 'asc',
      );

      final contactsData = response['contacts'] as List<dynamic>? ?? [];
      _contacts = contactsData.map((json) => Contact.fromApi(json)).toList();
      _contactsLoaded = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading contacts: $e');
      // Keep existing contacts on error
    }
  }

  /// Add a new contact via backend API
  Future<Contact?> addContact(Contact contact) async {
    try {
      final response = await _apiService.createContact(
        name: contact.name,
        phone: contact.phone,
        email: contact.email.isNotEmpty ? contact.email : null,
        location: contact.location.isNotEmpty ? contact.location : null,
        metAt: contact.metAt,
        socialMedia: contact.socialMedia.isNotEmpty ? contact.socialMedia : null,
        memoryPrompt: contact.memoryPrompt.isNotEmpty ? contact.memoryPrompt : null,
        imagePath: contact.imagePath,
        imagePublicId: contact.imagePublicId,
        deviceSyncStatus: contact.deviceSyncStatus,
      );

      final newContact = Contact.fromApi(response);
      _contacts.insert(0, newContact); // Add to beginning for newest first
      notifyListeners();
      return newContact;
    } catch (e) {
      debugPrint('Error adding contact: $e');
      throw Exception('Failed to add contact: $e');
    }
  }

  /// Update an existing contact via backend API
  Future<Contact?> updateContact(Contact updatedContact) async {
    try {
      final response = await _apiService.updateContact(
        contactId: updatedContact.id,
        name: updatedContact.name,
        phone: updatedContact.phone,
        email: updatedContact.email.isNotEmpty ? updatedContact.email : null,
        location: updatedContact.location.isNotEmpty ? updatedContact.location : null,
        metAt: updatedContact.metAt,
        socialMedia: updatedContact.socialMedia.isNotEmpty ? updatedContact.socialMedia : null,
        memoryPrompt: updatedContact.memoryPrompt.isNotEmpty ? updatedContact.memoryPrompt : null,
        imagePath: updatedContact.imagePath,
        imagePublicId: updatedContact.imagePublicId,
        deviceSyncStatus: updatedContact.deviceSyncStatus,
      );

      final updated = Contact.fromApi(response);
      final index = _contacts.indexWhere((contact) => contact.id == updatedContact.id);
      if (index != -1) {
        _contacts[index] = updated;
        notifyListeners();
      }
      return updated;
    } catch (e) {
      debugPrint('Error updating contact: $e');
      throw Exception('Failed to update contact: $e');
    }
  }

  /// Remove a contact via backend API
  Future<void> removeContact(String id) async {
    try {
      await _apiService.deleteContact(id);
      _contacts.removeWhere((contact) => contact.id == id);
      notifyListeners();
    } catch (e) {
      debugPrint('Error removing contact: $e');
      throw Exception('Failed to remove contact: $e');
    }
  }

  /// Get a contact by ID (from cache or backend)
  Future<Contact?> getContactById(String id) async {
    // First try to find in cache
    try {
      final cachedContact = _contacts.firstWhere((contact) => contact.id == id);
      return cachedContact;
    } catch (e) {
      // Not in cache, try to fetch from backend
      try {
        final response = await _apiService.getContact(id);
        return Contact.fromApi(response);
      } catch (e) {
        debugPrint('Error getting contact by ID: $e');
        return null;
      }
    }
  }

  /// Search contacts via backend API
  Future<List<Contact>> searchContacts(String query) async {
    if (query.isEmpty) {
      await loadContacts();
      return _contacts;
    }

    try {
      final response = await _apiService.searchContacts(
        query: query,
        fields: 'name,phone,email,location',
        limit: 50,
      );

      final resultsData = response['results'] as List<dynamic>? ?? [];
      return resultsData.map((json) => Contact.fromApi(json)).toList();
    } catch (e) {
      debugPrint('Error searching contacts: $e');
      // Fallback to local search
      final queryLower = query.toLowerCase();
      return _contacts.where((contact) {
        return contact.name.toLowerCase().contains(queryLower) ||
               contact.phone.toLowerCase().contains(queryLower) ||
               contact.email.toLowerCase().contains(queryLower) ||
               contact.location.toLowerCase().contains(queryLower);
      }).toList();
    }
  }

  /// Get VCF content for a contact
  Future<String> getContactVCF(String contactId) async {
    try {
      return await _apiService.getContactVCF(contactId);
    } catch (e) {
      debugPrint('Error getting contact VCF: $e');
      throw Exception('Failed to generate VCF: $e');
    }
  }

  /// Update device sync status for a contact
  Future<void> updateContactDeviceSync({
    required String contactId,
    required String syncStatus,
    String? deviceContactId,
    String? syncError,
  }) async {
    try {
      await _apiService.updateDeviceSync(
        contactId: contactId,
        syncStatus: syncStatus,
        deviceContactId: deviceContactId,
        syncError: syncError,
        deviceInfo: {
          'platform': 'flutter',
          'version': '1.0.0',
        },
      );

      // Update local cache
      final index = _contacts.indexWhere((contact) => contact.id == contactId);
      if (index != -1) {
        _contacts[index] = _contacts[index].copyWith(
          deviceSyncStatus: syncStatus,
          updatedAt: DateTime.now(),
        );
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating device sync: $e');
      throw Exception('Failed to update sync status: $e');
    }
  }

  // Event methods
  Map<DateTime, List<Event>> get events => Map.unmodifiable(_events);

  List<Event> getEventsForDay(DateTime day) {
    final key = DateTime.utc(day.year, day.month, day.day);
    return _events[key] ?? [];
  }

  Future<void> addEvent(Event event) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    final key = DateTime.utc(event.date.year, event.date.month, event.date.day);
    
    if (_events.containsKey(key)) {
      _events[key]!.add(event);
    } else {
      _events[key] = [event];
    }
    
    notifyListeners();
  }

  void removeEvent(String id) {
    for (final eventList in _events.values) {
      eventList.removeWhere((event) => event.id == id);
    }
    notifyListeners();
  }

  Event? getEventById(String id) {
    for (final eventList in _events.values) {
      try {
        return eventList.firstWhere((event) => event.id == id);
      } catch (e) {
        continue;
      }
    }
    return null;
  }


}
