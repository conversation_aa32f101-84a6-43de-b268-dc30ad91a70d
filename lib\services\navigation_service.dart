import 'package:flutter/material.dart';
import '../screens/home/<USER>';
import '../screens/calendar/calendar_screen.dart';
import '../screens/smart_capture/smart_capture_screen.dart';
import '../screens/profile/profile_screen.dart';
import '../screens/chat/chat_screen.dart';
import '../screens/therapy/mind_garden_screen.dart';
import '../screens/therapy/therapy_voice_screen.dart';
import '../screens/therapy/therapy_progress_screen.dart';
import '../screens/voice/voice_screen.dart';
import '../screens/productivity/notes_screen.dart';
import '../screens/productivity/task_management_screen.dart';
import '../screens/calendar/add_event_screen.dart';
import '../screens/calendar/add_contact_screen.dart';
import '../screens/calendar/contact_list_screen.dart';
import '../widgets/enhanced_bottom_nav_bar.dart';

/// Navigation service to handle all app navigation logic
/// Implements hub-and-spoke pattern with hierarchical depth
class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Main tab indices for bottom navigation
  static const int homeTab = 0;
  static const int calendarTab = 1;
  static const int smartCaptureTab = 3;
  static const int profileTab = 4;

  /// Current active tab for maintaining state
  int _currentTab = homeTab;
  int get currentTab => _currentTab;

  /// Navigation context helper
  BuildContext? get context => navigatorKey.currentContext;

  /// Navigate to main tabs (bottom navigation)
  void navigateToTab(int tabIndex) {
    if (_currentTab == tabIndex) return; // Already on this tab
    
    _currentTab = tabIndex;
    
    switch (tabIndex) {
      case homeTab:
        _navigateAndReplace(const HomeScreen());
        break;
      case calendarTab:
        _navigateAndReplace(const CalendarScreen());
        break;
      case smartCaptureTab:
        _navigateAndReplace(const SmartCaptureScreen());
        break;
      case profileTab:
        _navigateAndReplace(const ProfileScreen());
        break;
    }
  }

  /// Navigate to specific screens with proper hierarchy
  void navigateToScreen(Widget screen, {bool replace = false}) {
    if (context == null) return;
    
    if (replace) {
      Navigator.of(context!).pushReplacement(
        _createRoute(screen, isTabNavigation: false),
      );
    } else {
      Navigator.of(context!).push(
        _createRoute(screen, isTabNavigation: false),
      );
    }
  }

  /// Navigate back in hierarchy
  void navigateBack() {
    if (context == null) return;
    
    if (Navigator.of(context!).canPop()) {
      Navigator.of(context!).pop();
    } else {
      // If can't pop, go to home
      navigateToTab(homeTab);
    }
  }

  /// Navigate to home and clear stack
  void navigateToHome() {
    _currentTab = homeTab;
    _navigateAndReplace(const HomeScreen());
  }

  /// Specific navigation methods for common flows
  
  // Home screen navigation
  void navigateToPersonalSpace() {
    navigateToScreen(const MindGardenScreen());
  }

  void navigateToChat() {
    navigateToScreen(const ChatScreen());
  }

  void navigateToProductivityOverlay() {
    // This will trigger the navbar overlay for productivity actions
    // Implementation handled by the navbar component
  }

  void navigateToUpcomingEvents() {
    navigateToTab(calendarTab);
  }

  // Mind Garden navigation
  void navigateToTherapyVoice() {
    navigateToScreen(const TherapyVoiceScreen());
  }

  void navigateToTherapyProgress() {
    navigateToScreen(const TherapyProgressScreen());
  }

  // Voice navigation
  void navigateToVoice() {
    navigateToScreen(const VoiceScreen());
  }

  // Calendar navigation
  void navigateToAddEvent() {
    navigateToScreen(const AddEventScreen());
  }

  void navigateToAddContact() {
    navigateToScreen(const AddContactScreen());
  }

  void navigateToContactList() {
    navigateToScreen(const ContactListScreen());
  }

  // Productivity navigation
  void navigateToNotes() {
    navigateToScreen(const NotesScreen());
  }

  void navigateToTasks() {
    navigateToScreen(const TaskManagementScreen());
  }

  /// Private helper methods
  
  /// TabScreen wrapper that keeps the bottom nav bar fixed
  Widget _wrapWithTabContainer(Widget screen, int tabIndex) {
    return _TabScreenWrapper(
      child: screen,
      currentIndex: tabIndex,
    );
  }
  
  void _navigateAndReplace(Widget screen) {
    if (context == null) return;
    
    Navigator.of(context!).pushAndRemoveUntil(
      _createRoute(_wrapWithTabContainer(screen, _currentTab), isTabNavigation: true),
      (route) => false, // Remove all previous routes
    );
  }

  PageRoute _createRoute(Widget screen, {bool isTabNavigation = false}) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => screen,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // For tab navigation, we apply a fade transition to the content
        // to avoid the entire scaffold sliding, which makes the navbar move.
        if (isTabNavigation) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        } 
        
        // For all other navigation (pushing a new screen on top),
        // use the standard slide transition.
        else {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        }
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  /// Handle Android back button
  Future<bool> onWillPop() async {
    if (Navigator.of(context!).canPop()) {
      Navigator.of(context!).pop();
      return false;
    } else if (_currentTab != homeTab) {
      navigateToTab(homeTab);
      return false;
    }
    return true; // Allow app to close
  }

  /// Reset navigation state (useful for logout)
  void reset() {
    _currentTab = homeTab;
  }
}

/// A wrapper widget for tab screens that keeps the bottom navigation bar fixed
/// during tab transitions
class _TabScreenWrapper extends StatelessWidget {
  final Widget child;
  final int currentIndex;
  
  const _TabScreenWrapper({
    required this.child,
    required this.currentIndex,
  });
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: child,
      bottomNavigationBar: EnhancedBottomNavBar(selectedIndex: currentIndex),
    );
  }
}
