/// Dashboard data models following the manual JSON serialization pattern

class DashboardData {
  final String userName;
  final String greeting;
  final TaskSummary taskSummary;
  final NoteSummary noteSummary;
  final List<UpcomingEvent> upcomingEvents;
  final TherapyProgress therapyProgress;
  final List<QuickAction> quickActions;

  const DashboardData({
    required this.userName,
    required this.greeting,
    required this.taskSummary,
    required this.noteSummary,
    required this.upcomingEvents,
    required this.therapyProgress,
    required this.quickActions,
  });

  factory DashboardData.fromJson(Map<String, dynamic> json) {
    return DashboardData(
      userName: json['user_name'] ?? '',
      greeting: json['greeting'] ?? '',
      taskSummary: TaskSummary.fromJson(json['task_summary'] ?? {}),
      noteSummary: NoteSummary.fromJson(json['note_summary'] ?? {}),
      upcomingEvents: (json['upcoming_events'] as List<dynamic>?)
          ?.map((e) => UpcomingEvent.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      therapyProgress: TherapyProgress.fromJson(json['therapy_progress'] ?? {}),
      quickActions: (json['quick_actions'] as List<dynamic>?)
          ?.map((e) => QuickAction.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_name': userName,
      'greeting': greeting,
      'task_summary': taskSummary.toJson(),
      'note_summary': noteSummary.toJson(),
      'upcoming_events': upcomingEvents.map((e) => e.toJson()).toList(),
      'therapy_progress': therapyProgress.toJson(),
      'quick_actions': quickActions.map((e) => e.toJson()).toList(),
    };
  }
}

class TaskSummary {
  final int totalTasks;
  final int completedTasks;
  final int pendingTasks;
  final List<TaskItem> todayTasks;

  const TaskSummary({
    required this.totalTasks,
    required this.completedTasks,
    required this.pendingTasks,
    required this.todayTasks,
  });

  factory TaskSummary.fromJson(Map<String, dynamic> json) {
    return TaskSummary(
      totalTasks: json['total_tasks'] ?? 0,
      completedTasks: json['completed_tasks'] ?? 0,
      pendingTasks: json['pending_tasks'] ?? 0,
      todayTasks: (json['today_tasks'] as List<dynamic>?)
          ?.map((e) => TaskItem.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_tasks': totalTasks,
      'completed_tasks': completedTasks,
      'pending_tasks': pendingTasks,
      'today_tasks': todayTasks.map((e) => e.toJson()).toList(),
    };
  }
}

class TaskItem {
  final String id;
  final String title;
  final String? description;
  final bool isCompleted;
  final String priority;
  final DateTime? dueDate;

  const TaskItem({
    required this.id,
    required this.title,
    this.description,
    required this.isCompleted,
    required this.priority,
    this.dueDate,
  });

  factory TaskItem.fromJson(Map<String, dynamic> json) {
    return TaskItem(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'],
      isCompleted: json['is_completed'] ?? false,
      priority: json['priority'] ?? 'medium',
      dueDate: json['due_date'] != null 
          ? DateTime.parse(json['due_date']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'is_completed': isCompleted,
      'priority': priority,
      'due_date': dueDate?.toIso8601String(),
    };
  }
}

class NoteSummary {
  final int totalNotes;
  final int recentNotes;
  final List<NoteItem> pinnedNotes;

  const NoteSummary({
    required this.totalNotes,
    required this.recentNotes,
    required this.pinnedNotes,
  });

  factory NoteSummary.fromJson(Map<String, dynamic> json) {
    return NoteSummary(
      totalNotes: json['total_notes'] ?? 0,
      recentNotes: json['recent_notes'] ?? 0,
      pinnedNotes: (json['pinned_notes'] as List<dynamic>?)
          ?.map((e) => NoteItem.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_notes': totalNotes,
      'recent_notes': recentNotes,
      'pinned_notes': pinnedNotes.map((e) => e.toJson()).toList(),
    };
  }
}

class NoteItem {
  final String id;
  final String title;
  final String content;
  final bool isPinned;
  final DateTime createdAt;
  final DateTime updatedAt;

  const NoteItem({
    required this.id,
    required this.title,
    required this.content,
    required this.isPinned,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NoteItem.fromJson(Map<String, dynamic> json) {
    return NoteItem(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      isPinned: json['is_pinned'] ?? false,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'is_pinned': isPinned,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class UpcomingEvent {
  final String id;
  final String title;
  final String? description;
  final DateTime startTime;
  final DateTime endTime;
  final String type;

  const UpcomingEvent({
    required this.id,
    required this.title,
    this.description,
    required this.startTime,
    required this.endTime,
    required this.type,
  });

  factory UpcomingEvent.fromJson(Map<String, dynamic> json) {
    return UpcomingEvent(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'],
      startTime: DateTime.parse(json['start_time'] ?? DateTime.now().toIso8601String()),
      endTime: DateTime.parse(json['end_time'] ?? DateTime.now().toIso8601String()),
      type: json['type'] ?? 'event',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'type': type,
    };
  }
}

class TherapyProgress {
  final int sessionStreak;
  final String currentMood;
  final int weeklyGoalProgress;
  final String lastSessionDate;

  const TherapyProgress({
    required this.sessionStreak,
    required this.currentMood,
    required this.weeklyGoalProgress,
    required this.lastSessionDate,
  });

  factory TherapyProgress.fromJson(Map<String, dynamic> json) {
    return TherapyProgress(
      sessionStreak: json['session_streak'] ?? 0,
      currentMood: json['current_mood'] ?? '',
      weeklyGoalProgress: json['weekly_goal_progress'] ?? 0,
      lastSessionDate: json['last_session_date'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'session_streak': sessionStreak,
      'current_mood': currentMood,
      'weekly_goal_progress': weeklyGoalProgress,
      'last_session_date': lastSessionDate,
    };
  }
}

class QuickAction {
  final String id;
  final String title;
  final String iconPath;
  final String action;

  const QuickAction({
    required this.id,
    required this.title,
    required this.iconPath,
    required this.action,
  });

  factory QuickAction.fromJson(Map<String, dynamic> json) {
    return QuickAction(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      iconPath: json['icon_path'] ?? '',
      action: json['action'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'icon_path': iconPath,
      'action': action,
    };
  }
}
