import 'package:flutter/material.dart';
import '../../../utils/design_tokens.dart';
import '../models/category.dart';

class EmptyState extends StatelessWidget {
  final String searchQuery;
  final TaskCategory? selectedCategory;
  final VoidCallback onAddTask;
  final VoidCallback onClearFilters;

  const EmptyState({
    super.key,
    required this.searchQuery,
    required this.selectedCategory,
    required this.onAddTask,
    required this.onClearFilters,
  });

  @override
  Widget build(BuildContext context) {
    if (searchQuery.isNotEmpty || selectedCategory != null) {
      return _buildFilteredEmptyState();
    }
    
    return _buildNoTasksEmptyState();
  }

  Widget _buildNoTasksEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingXl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Darvis list illustration
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                child: Image.asset(
                  'assets/images/darvis_list.png',
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    // Fallback if image doesn't exist
                    return Container(
                      decoration: BoxDecoration(
                        color: DesignTokens.backgroundCard.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.1),
                          width: 1,
                        ),
                      ),
                      child: const Icon(
                        Icons.task_alt,
                        size: 80,
                        color: DesignTokens.iconSecondary,
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: DesignTokens.spacingXl),
            const Text(
              'No task added yet',
              style: DesignTokens.cardTitleStyle,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            Text(
              'Start organizing your day by adding your first task',
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: DesignTokens.spacingXl),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onAddTask,
                style: ElevatedButton.styleFrom(
                  backgroundColor: DesignTokens.primaryInteractiveBlue,
                  padding: const EdgeInsets.symmetric(
                    vertical: DesignTokens.spacingMd,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.add,
                      color: DesignTokens.iconPrimary,
                    ),
                    const SizedBox(width: DesignTokens.spacingSm),
                    Text(
                      'Add Your First Task',
                      style: DesignTokens.bodyStyle.copyWith(
                        color: DesignTokens.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilteredEmptyState() {
    String title;
    String description;
    IconData icon;

    if (searchQuery.isNotEmpty) {
      title = 'No tasks found';
      description = 'No tasks match "$searchQuery". Try a different search term or add a new task.';
      icon = Icons.search_off;
    } else if (selectedCategory != null) {
      title = 'No ${selectedCategory!.name.toLowerCase()} tasks';
      description = 'You don\'t have any tasks in the ${selectedCategory!.name} category yet.';
      icon = Icons.category;
    } else {
      title = 'No tasks found';
      description = 'No tasks match your current filters.';
      icon = Icons.filter_list_off;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingXl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: DesignTokens.backgroundCard.withOpacity(0.3),
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withOpacity(0.1),
                  width: 1,
                ),
              ),
              child: Icon(
                icon,
                size: 48,
                color: DesignTokens.iconSecondary,
              ),
            ),
            const SizedBox(height: DesignTokens.spacingXl),
            Text(
              title,
              style: DesignTokens.cardTitleStyle,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            Text(
              description,
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: DesignTokens.spacingXl),
            Row(
              children: [
                if (searchQuery.isNotEmpty || selectedCategory != null) ...[
                  Expanded(
                    child: OutlinedButton(
                      onPressed: onClearFilters,
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(
                          color: DesignTokens.textSecondary.withOpacity(0.5),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: DesignTokens.spacingMd,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                        ),
                      ),
                      child: Text(
                        'Clear Filters',
                        style: DesignTokens.bodyStyle.copyWith(
                          color: DesignTokens.textSecondary,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: DesignTokens.spacingMd),
                ],
                Expanded(
                  child: ElevatedButton(
                    onPressed: onAddTask,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: DesignTokens.primaryInteractiveBlue,
                      padding: const EdgeInsets.symmetric(
                        vertical: DesignTokens.spacingMd,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.add,
                          color: DesignTokens.iconPrimary,
                        ),
                        const SizedBox(width: DesignTokens.spacingSm),
                        Text(
                          'Add Task',
                          style: DesignTokens.bodyStyle.copyWith(
                            color: DesignTokens.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
