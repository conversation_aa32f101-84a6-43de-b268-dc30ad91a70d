import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:ui';
import '../../../utils/design_tokens.dart';
import '../models/category.dart';

class CategoryCreationFlow extends StatefulWidget {
  final Function(TaskCategory) onCategoryCreated;

  const CategoryCreationFlow({
    super.key,
    required this.onCategoryCreated,
  });

  @override
  State<CategoryCreationFlow> createState() => _CategoryCreationFlowState();
}

class _CategoryCreationFlowState extends State<CategoryCreationFlow>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  late PageController _pageController;
  
  final TextEditingController _nameController = TextEditingController();
  final FocusNode _nameFocusNode = FocusNode();
  
  int _currentStep = 0;
  String _selectedIconPath = 'assets/icons/categories/category_work.svg';
  Color _selectedColor = DesignTokens.primaryInteractiveBlue;
  
  // Available icons from the categories folder
  final List<String> _availableIcons = [
    'assets/icons/categories/category_exercise.svg',
    'assets/icons/categories/category_family.svg',
    'assets/icons/categories/category_food.svg',
    'assets/icons/categories/category_health.svg',
    'assets/icons/categories/category_home.svg',
    'assets/icons/categories/category_movie.svg',
    'assets/icons/categories/category_music.svg',
    'assets/icons/categories/category_school.svg',
    'assets/icons/categories/category_social.svg',
    'assets/icons/categories/category_work.svg',
  ];
  
  // Darvis-themed color palette
  final List<Color> _availableColors = [
    DesignTokens.primaryInteractiveBlue,
    const Color(0xFF059669), // Therapy green
    const Color(0xFF10B981), // Therapy green light
    const Color(0xFF8B5CF6), // Muted purple
    const Color(0xFF06B6D4), // Elegant teal
    const Color(0xFF6B7280), // Elegant gray
    const Color(0xFF9CA3AF), // Elegant gray light
    const Color(0xFFF59E0B), // Warm amber
    const Color(0xFFEF4444), // Red
    const Color(0xFF3B82F6), // Blue
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    _pageController = PageController();
    _slideController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _closeModal(),
      child: Scaffold(
        backgroundColor: Colors.black.withAlpha(128),
        body: Stack(
          children: [
            Positioned.fill(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2), // Reduced from 5,5
                child: Container(
                  color: Colors.black.withAlpha(77),
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: SlideTransition(
                position: _slideAnimation,
                child: GestureDetector(
                  onTap: () {}, // Prevent closing when tapping inside
                  child: _buildModalContent(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModalContent() {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(DesignTokens.borderRadiusXl),
        topRight: Radius.circular(DesignTokens.borderRadiusXl),
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8), // Reduced from 15,15
        child: Container(
          height: MediaQuery.of(context).size.height * 0.60, // compact equal height for both steps
          decoration: const BoxDecoration(
            color: Color(0xFF1A1A1A),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(DesignTokens.borderRadiusXl),
              topRight: Radius.circular(DesignTokens.borderRadiusXl),
            ),
          ),
          child: Column(
            children: [
              _buildHeader(),
              Flexible(
                fit: FlexFit.loose,
                child: PageView(
                  controller: _pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    _buildIconSelectionStep(),
                    _buildCategoryDetailsStep(),
                  ],
                ),
              ),
              _buildBottomActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingLg),
      child: Row(
        children: [
          if (_currentStep > 0)
            IconButton(
              onPressed: _previousStep,
              icon: const Icon(Icons.arrow_back, color: DesignTokens.iconPrimary),
            )
          else
            const SizedBox(width: 48),
          Expanded(
            child: Text(
              _currentStep == 0 ? 'Choose Icon' : 'Create Category',
              style: DesignTokens.cardTitleStyle,
              textAlign: TextAlign.center,
            ),
          ),
          IconButton(
            onPressed: _closeModal,
            icon: const Icon(Icons.close, color: DesignTokens.iconPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildIconSelectionStep() {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingLg,
        vertical: DesignTokens.spacingXs,  // tighter top/bottom padding
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select an icon for your category',
            style: DesignTokens.bodyStyle.copyWith(
              color: DesignTokens.textSecondary,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingXs),
          Expanded(
              child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                crossAxisSpacing: DesignTokens.spacingSm, // reduced spacing
                mainAxisSpacing: DesignTokens.spacingSm,
                childAspectRatio: 1,
              ),
              itemCount: _availableIcons.length,
              itemBuilder: (context, index) {
                final iconPath = _availableIcons[index];
                final isSelected = _selectedIconPath == iconPath;
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedIconPath = iconPath;
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(3), // Add visual margins
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected 
                            ? DesignTokens.primaryInteractiveBlue.withAlpha(51)
                            : const Color(0xFF2A2A2A),
                        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                        border: Border.all(
                          color: isSelected 
                              ? DesignTokens.primaryInteractiveBlue
                              : const Color(0xFF333333),
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Center(
                        child: SvgPicture.asset(
                          iconPath,
                          width: 18, // Slightly smaller icon
                          height: 18,
                          colorFilter: ColorFilter.mode(
                            isSelected 
                                ? DesignTokens.primaryInteractiveBlue
                                : DesignTokens.iconSecondary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryDetailsStep() {
    return Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingLg,
          vertical: DesignTokens.spacingMd,
        ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Selected icon preview
          const SizedBox(height: DesignTokens.spacingXs), // further reduce space under title
          Container(
            width: 50, // further reduced size
            height: 50,
            decoration: BoxDecoration(
              color: _selectedColor.withAlpha(51),
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              border: Border.all(
                color: _selectedColor,
                width: 2,
              ),
            ),
              child: Center(
              child: SvgPicture.asset(
                _selectedIconPath,
                width: 32,
                height: 32,
                colorFilter: ColorFilter.mode(
                  _selectedColor,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          const SizedBox(height: DesignTokens.spacingMd),
          
          // Category name input
          Text(
            'Category Name',
            style: DesignTokens.bodyStyle.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingSm),
          TextField(
            controller: _nameController,
            focusNode: _nameFocusNode,
            style: DesignTokens.bodyStyle,
            decoration: InputDecoration(
              hintText: 'Enter category name',
              hintStyle: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textMuted,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                borderSide: const BorderSide(color: Color(0xFF333333)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                borderSide: const BorderSide(color: Color(0xFF333333)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                borderSide: BorderSide(color: _selectedColor),
              ),
              filled: true,
              fillColor: const Color(0xFF2A2A2A),
            ),
          ),
          const SizedBox(height: DesignTokens.spacingXs), // further reduce space before color picker
          
          // Color picker
          Text(
            'Choose Color',
            style: DesignTokens.bodyStyle.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingSm),
          Wrap(
            spacing: DesignTokens.spacingMd,
            runSpacing: DesignTokens.spacingMd,
            children: _availableColors.map((color) {
              final isSelected = _selectedColor == color;
              
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedColor = color;
                  });
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected ? Colors.white : Colors.transparent,
                      width: 3,
                    ),
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 20,
                        )
                      : null,
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingLg,
          vertical: DesignTokens.spacingXs,
        ),
        margin: const EdgeInsets.only(bottom: DesignTokens.spacingSm), // add space after buttons
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: TextButton(
                onPressed: _previousStep,
                child: Text(
                  'Back',
                  style: DesignTokens.bodyStyle.copyWith(
                    color: DesignTokens.textSecondary,
                  ),
                ),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: DesignTokens.spacingMd),
          Expanded(
            flex: _currentStep == 0 ? 1 : 2,
            child: ElevatedButton(
              onPressed: _currentStep == 0 ? _nextStep : _createCategory,
              style: ElevatedButton.styleFrom(
                backgroundColor: DesignTokens.primaryInteractiveBlue,
                padding: const EdgeInsets.symmetric(vertical: DesignTokens.spacingMd),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                ),
              ),
              child: Text(
                _currentStep == 0 ? 'Next' : 'Create Category',
                style: DesignTokens.bodyStyle.copyWith(
                  color: DesignTokens.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _nextStep() {
    setState(() {
      _currentStep = 1;
    });
    _pageController.nextPage(
      duration: const Duration(milliseconds: 250),
      curve: Curves.easeInOut,
    ).then((_) {
      // Use WidgetsBinding to ensure proper timing with the widget lifecycle
      if (mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _nameFocusNode.requestFocus();
        });
      }
    });
  }

  void _previousStep() {
    setState(() {
      _currentStep = 0;
    });
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _createCategory() {
    if (_nameController.text.trim().isEmpty) return;

    final newCategory = TaskCategory(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: _nameController.text.trim(),
      color: _selectedColor,
      iconPath: _selectedIconPath,
    );

    widget.onCategoryCreated(newCategory);
    _closeModal();
  }

  void _closeModal() {
    _slideController.reverse().then((_) {
      Navigator.of(context).pop();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _pageController.dispose();
    _nameController.dispose();
    _nameFocusNode.dispose();
    super.dispose();
  }
}
