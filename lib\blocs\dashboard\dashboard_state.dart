import 'package:equatable/equatable.dart';
import '../../models/dashboard_models.dart';

/// Dashboard states following the AuthBloc pattern
abstract class DashboardState extends Equatable {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class DashboardInitial extends DashboardState {
  const DashboardInitial();
}

/// Loading state
class DashboardLoading extends DashboardState {
  const DashboardLoading();
}

/// Loaded state with dashboard data
class DashboardLoaded extends DashboardState {
  final DashboardData data;

  const DashboardLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

/// Error state
class DashboardError extends DashboardState {
  final String message;

  const DashboardError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Refreshing state (when data exists but refreshing)
class DashboardRefreshing extends DashboardState {
  final DashboardData currentData;

  const DashboardRefreshing(this.currentData);

  @override
  List<Object?> get props => [currentData];
}
