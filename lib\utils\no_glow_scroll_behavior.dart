import 'package:flutter/material.dart';

/// Custom scroll behavior that removes the overscroll glow effect
/// This eliminates the unwanted light flash during overscroll in dark themes
class NoGlowScrollBehavior extends ScrollBehavior {
  @override
  Widget buildOverscrollIndicator(
      BuildContext context, Widget child, ScrollableDetails details) {
    // Return the child directly without wrapping it in a GlowingOverscrollIndicator
    return child;
  }
  
  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    // Use ClampingScrollPhysics to prevent the bounce/overscroll effect entirely
    return const ClampingScrollPhysics();
  }
}
