package com.drix.app

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.drix.app/share"
    private var methodChannel: MethodChannel? = null
    private var pendingSharedData: Map<String, Any>? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
        methodChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "getInitialSharedData" -> {
                    result.success(pendingSharedData)
                    pendingSharedData = null // Clear after sending
                }
                else -> result.notImplemented()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        handleIntent(intent)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent?) {
        if (intent == null) return

        when (intent.action) {
            Intent.ACTION_SEND -> {
                if (intent.type?.startsWith("text/") == true) {
                    handleTextShare(intent)
                } else if (intent.type?.startsWith("image/") == true) {
                    handleImageShare(intent)
                }
            }
            Intent.ACTION_SEND_MULTIPLE -> {
                if (intent.type?.startsWith("image/") == true) {
                    handleMultipleImagesShare(intent)
                }
            }
        }
    }

    private fun handleTextShare(intent: Intent) {
        val sharedText = intent.getStringExtra(Intent.EXTRA_TEXT)
        val sharedSubject = intent.getStringExtra(Intent.EXTRA_SUBJECT)

        if (!sharedText.isNullOrEmpty()) {
            val sharedData = mapOf(
                "type" to "text",
                "content" to sharedText,
                "subject" to (sharedSubject ?: ""),
                "timestamp" to System.currentTimeMillis()
            )

            if (methodChannel != null) {
                methodChannel?.invokeMethod("handleSharedContent", sharedData)
            } else {
                pendingSharedData = sharedData
            }
        }
    }

    private fun handleImageShare(intent: Intent) {
        val imageUri = intent.getParcelableExtra<Uri>(Intent.EXTRA_STREAM)

        if (imageUri != null) {
            val sharedData = mapOf(
                "type" to "image",
                "uri" to imageUri.toString(),
                "timestamp" to System.currentTimeMillis()
            )

            if (methodChannel != null) {
                methodChannel?.invokeMethod("handleSharedContent", sharedData)
            } else {
                pendingSharedData = sharedData
            }
        }
    }

    private fun handleMultipleImagesShare(intent: Intent) {
        val imageUris = intent.getParcelableArrayListExtra<Uri>(Intent.EXTRA_STREAM)

        if (!imageUris.isNullOrEmpty()) {
            val uriStrings = imageUris.map { it.toString() }
            val sharedData = mapOf(
                "type" to "images",
                "uris" to uriStrings,
                "timestamp" to System.currentTimeMillis()
            )

            if (methodChannel != null) {
                methodChannel?.invokeMethod("handleSharedContent", sharedData)
            } else {
                pendingSharedData = sharedData
            }
        }
    }
}
