import 'dart:async';
import 'package:flutter/services.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';

/// Service for handling Android share intents and shared content
class ShareIntentService {
  static const MethodChannel _channel = MethodChannel('com.drix.app/share');
  
  // Stream controllers for different types of shared content
  final StreamController<String> _textStreamController = StreamController<String>.broadcast();
  final StreamController<List<String>> _imageStreamController = StreamController<List<String>>.broadcast();
  
  // Subscriptions to receive_sharing_intent streams
  late StreamSubscription _textSubscription;
  late StreamSubscription _imageSubscription;
  
  // Flag to track initialization
  bool _isInitialized = false;

  /// Initialize the share intent service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Set up method channel handler for native share intents
      _channel.setMethodCallHandler(_handleMethodCall);
      
      // Set up receive_sharing_intent listeners
      _setupReceiveSharingIntent();
      
      // Check for initial shared data when app starts
      await _checkInitialSharedData();
      
      _isInitialized = true;
      print('✅ ShareIntentService initialized successfully');
    } catch (e) {
      print('❌ Error initializing ShareIntentService: $e');
    }
  }

  /// Set up receive_sharing_intent listeners
  void _setupReceiveSharingIntent() {
    // Listen for all media sharing (including text) while app is running
    _textSubscription = ReceiveSharingIntent.instance.getMediaStream().listen(
      (List<SharedMediaFile> value) {
        // Filter and process text content
        for (final file in value) {
          if (file.type == SharedMediaType.text || file.type == SharedMediaType.url) {
            print('📱 Received shared text: ${file.path}');
            _textStreamController.add(file.path);
          }
        }
      },
      onError: (err) {
        print('❌ Error receiving shared media: $err');
      },
    );

    // Listen for image sharing while app is running
    _imageSubscription = ReceiveSharingIntent.instance.getMediaStream().listen(
      (List<SharedMediaFile> value) {
        // Filter and process image content
        final imagePaths = value
            .where((file) => file.type == SharedMediaType.image || file.type == SharedMediaType.video)
            .map((file) => file.path)
            .toList();
        
        if (imagePaths.isNotEmpty) {
          print('📱 Received shared images: $imagePaths');
          _imageStreamController.add(imagePaths);
        }
      },
      onError: (err) {
        print('❌ Error receiving shared images: $err');
      },
    );
  }

  /// Check for initial shared data when app starts
  Future<void> _checkInitialSharedData() async {
    try {
      // Check for initial shared media (including text)
      final initialMedia = await ReceiveSharingIntent.instance.getInitialMedia();
      
      if (initialMedia.isNotEmpty) {
        // Process text content
        for (final file in initialMedia) {
          if (file.type == SharedMediaType.text || file.type == SharedMediaType.url) {
            print('📱 Initial shared text: ${file.path}');
            _textStreamController.add(file.path);
          }
        }
        
        // Process image content
        final imagePaths = initialMedia
            .where((file) => file.type == SharedMediaType.image || file.type == SharedMediaType.video)
            .map((file) => file.path)
            .toList();
        
        if (imagePaths.isNotEmpty) {
          print('📱 Initial shared images: $imagePaths');
          _imageStreamController.add(imagePaths);
        }
      }

      // Also check native method channel for initial data
      final nativeInitialData = await _channel.invokeMethod('getInitialSharedData');
      if (nativeInitialData != null) {
        await _handleSharedData(nativeInitialData);
      }
    } catch (e) {
      print('❌ Error checking initial shared data: $e');
    }
  }

  /// Handle method calls from native Android code
  Future<void> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'handleSharedContent':
        await _handleSharedData(call.arguments);
        break;
      default:
        print('⚠️ Unknown method call: ${call.method}');
    }
  }

  /// Handle shared data from native code
  Future<void> _handleSharedData(dynamic data) async {
    if (data == null) return;
    
    try {
      final Map<String, dynamic> sharedData = Map<String, dynamic>.from(data);
      final String type = sharedData['type'] ?? '';
      
      switch (type) {
        case 'text':
          final String content = sharedData['content'] ?? '';
          if (content.isNotEmpty) {
            print('📱 Native shared text: $content');
            _textStreamController.add(content);
          }
          break;
          
        case 'image':
          final String uri = sharedData['uri'] ?? '';
          if (uri.isNotEmpty) {
            print('📱 Native shared image: $uri');
            _imageStreamController.add([uri]);
          }
          break;
          
        case 'images':
          final List<dynamic> uris = sharedData['uris'] ?? [];
          final List<String> imagePaths = uris.map((uri) => uri.toString()).toList();
          if (imagePaths.isNotEmpty) {
            print('📱 Native shared images: $imagePaths');
            _imageStreamController.add(imagePaths);
          }
          break;
          
        default:
          print('⚠️ Unknown shared content type: $type');
      }
    } catch (e) {
      print('❌ Error handling shared data: $e');
    }
  }

  /// Stream of shared text content
  Stream<String> get sharedTextStream => _textStreamController.stream;

  /// Stream of shared image paths
  Stream<List<String>> get sharedImageStream => _imageStreamController.stream;

  /// Check if a string is likely a URL
  bool isUrl(String text) {
    final urlPattern = RegExp(
      r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
      caseSensitive: false,
    );
    return urlPattern.hasMatch(text.trim());
  }

  /// Extract URLs from text
  List<String> extractUrls(String text) {
    final urlPattern = RegExp(
      r'https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)',
      caseSensitive: false,
    );
    
    final matches = urlPattern.allMatches(text);
    return matches.map((match) => match.group(0)!).toList();
  }

  /// Clean up shared text (remove extra whitespace, etc.)
  String cleanSharedText(String text) {
    return text.trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  /// Validate shared content
  bool isValidSharedContent(String content) {
    if (content.trim().isEmpty) return false;
    
    // Check if it's a valid URL or contains URLs
    if (isUrl(content) || extractUrls(content).isNotEmpty) {
      return true;
    }
    
    // Check if it's meaningful text (more than just whitespace)
    final cleanContent = cleanSharedText(content);
    return cleanContent.length > 3; // Minimum meaningful content length
  }

  /// Get content type from shared text
  String getContentType(String content) {
    if (isUrl(content)) {
      return 'url';
    } else if (extractUrls(content).isNotEmpty) {
      return 'text_with_urls';
    } else {
      return 'text';
    }
  }

  /// Reset shared content streams (clear any pending data)
  void resetStreams() {
    try {
      ReceiveSharingIntent.instance.reset();
    } catch (e) {
      print('❌ Error resetting receive sharing intent: $e');
    }
  }

  /// Dispose of the service
  void dispose() {
    _textSubscription.cancel();
    _imageSubscription.cancel();
    _textStreamController.close();
    _imageStreamController.close();
    _isInitialized = false;
    print('🗑️ ShareIntentService disposed');
  }
}
