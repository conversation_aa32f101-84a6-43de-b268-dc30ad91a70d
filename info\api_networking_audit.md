# API and Networking Infrastructure Audit

This report details the current state of the API and networking layer in the Drix Flutter application. The goal is to understand the existing architecture to ensure that new features, like the dashboard API, are integrated consistently.

**Overall Status:** The project has a foundational networking layer in place using `dio` and a service-oriented architecture with `get_it` for dependency injection. Authentication is handled by `flutter_bloc` and is currently using a mock service. The structure is scalable but requires further implementation for production use (e.g., authentication interceptors, robust error handling, and environment configuration).

---

### 1. Existing API Service Layer

-   **HTTP Client:** The application uses the **`dio`** package (`^5.3.2`) as its primary HTTP client.
    -   **File Location:** The Dio instance is created and configured in `lib/services/service_locator.dart`.
-   **API Service Class:** A centralized `ApiService` class is responsible for encapsulating all API calls.
    -   **File Location:** `lib/services/api_service.dart`.
    -   **Pattern:** It takes the `Dio` instance as a dependency, promoting testability. It's registered as a lazy singleton in `get_it`.
-   **Base URL Configuration:** The base URL is currently hardcoded.
    -   **Location:** `lib/services/service_locator.dart` within the `_createDio()` function.
    -   **Current Value:** `https://api.darvis.app`
-   **Authentication Header Handling:** This is **not yet implemented**. There is a `TODO` comment in `service_locator.dart` to add an authentication interceptor.

---

### 2. Authentication Integration

-   **Authentication Provider:** Firebase Auth (`firebase_auth`) is intended to be the provider, but it's currently disabled in `pubspec.yaml`. A **`MockAuthService`** is being used in its place.
    -   **File Location:** `lib/services/mock_auth_service.dart`.
-   **Token Management:**
    -   **Storage:** `flutter_secure_storage` is used for securely persisting authentication tokens.
    -   **Logic:** The `AuthServiceInterface` defines methods like `getAccessToken()` and `refreshAccessToken()`. The `AuthBloc` calls these methods.
-   **Authentication State Management:** **`flutter_bloc`** is used to manage the authentication state.
    -   **File Location:** `lib/blocs/auth/auth_bloc.dart`.
    -   **Pattern:** The `AuthBloc` listens to an `authStateChanges` stream from the `AuthServiceInterface` and manages states like `AuthAuthenticated`, `AuthUnauthenticated`, and `AuthLoading`.
-   **Token Injection:** Automatic token injection into API calls is **not yet implemented**. This will require a `Dio` interceptor that fetches the token from `AuthService` and adds it to the request headers.

---

### 3. State Management Patterns

-   **Primary Solution:** **`flutter_bloc`** is the state management library used across the app.
-   **Data Flow:**
    1.  UI Layer dispatches an event to a BLoC.
    2.  The BLoC calls a method on a service (e.g., `ApiService` or `AuthService`).
    3.  The service performs the network request or business logic.
    4.  The BLoC receives the result and emits a new state (`Loading`, `Success`, `Error`).
    5.  The UI rebuilds based on the new state.
-   **Loading/Error States:** The `AuthBloc` demonstrates a clear pattern for handling loading and error states, which should be replicated for other features.

---

### 4. Data Models Structure

-   **Model Classes:** The primary user model is `UserModel`.
    -   **File Location:** `lib/models/user_model.dart`.
-   **JSON Serialization/Deserialization:** Serialization is handled **manually** via `fromJson` and `toJson` methods within the model classes.
    -   **Pattern:** `UserModel.fromJson(Map<String, dynamic> json)` and `Map<String, dynamic> toJson()`.
-   **Model Generation Tools:** No code generation tools like `json_serializable` or `freezed` are currently in use.

---

### 5. Network Configuration

-   **Base URL:** As noted, the base URL is hardcoded in `service_locator.dart`. For a production app, this should be moved to an environment-specific configuration.
-   **Interceptors:**
    -   **Logging:** A `LogInterceptor` is configured to log request and response bodies in debug mode.
    -   **Retry:** The `dio_smart_retry` package is included in `pubspec.yaml`, but the `RetryInterceptor` has not been added to the Dio client yet.
    -   **TODOs:** There are placeholders to add authentication and certificate pinning interceptors.
-   **Connectivity Handling:** There is no explicit network connectivity handling (e.g., using the `connectivity_plus` package) integrated into the API service layer yet.

---

### 6. Existing Mock/Service Integration

-   **Service Locator:** **`get_it`** is used for dependency injection.
    -   **File Location:** `lib/services/service_locator.dart`.
-   **Mocked Services:** `AuthServiceInterface` is currently bound to `MockAuthService`. This makes it easy to swap in the real `FirebaseAuthService` later.
-   **Real Services:** `ApiService`, `LiveKitService`, and `SyncEngine` are registered as real services.
-   **Registration:** Services are registered as lazy singletons, which is efficient as they are only instantiated when first used.

---

### 7. Error Handling Patterns

-   **BLoC Level:** Errors are caught in `try-catch` blocks within the BLoCs (as seen in `AuthBloc`). The BLoC then emits an error state containing the error message.
-   **User-Facing Errors:** The UI layer would listen for these error states and display appropriate widgets (e.g., snackbars, dialogs). This part of the implementation is not yet visible in the audited files.
-   **API Service Level:** The `ApiService` should be structured to throw custom exceptions (e.g., `ApiException`) that the BLoCs can catch and interpret. This is not yet implemented.

---

### 8. Latency Handling

-   **Loading Indicators:** The primary mechanism for handling latency is displaying loading indicators based on BLoC states (e.g., `AuthLoading`).
-   **Timeouts:** The Dio client is configured with a 30-second timeout for connect, receive, and send operations.
-   **Caching:** `cached_network_image` is used for image caching. There is no generic HTTP response caching mechanism in place.
-   **Optimistic UI:** No optimistic UI patterns have been implemented yet.

---

### Recommendations for Dashboard API Integration

1.  **Follow the Existing Pattern:** Create a new BLoC for the dashboard (`DashboardBloc`).
2.  **Extend ApiService:** Add a new method to `ApiService` to fetch the dashboard data (e.g., `Future<DashboardData> getDashboardData()`).
3.  **Create Data Models:** Create the necessary `DashboardData` models with manual `fromJson`/`toJson` methods, following the `UserModel` pattern.
4.  **Implement in BLoC:** The `DashboardBloc` will call the `ApiService` method, handle loading and error states, and emit the final data to the UI.
5.  **Add Authentication:** Once the real `AuthService` is in place, implement the **authentication interceptor** in `service_locator.dart` to automatically inject the JWT into the headers of dashboard API requests.
6.  **Environment Configuration:** For the base URL, consider using a tool like `flutter_dotenv` to manage different environments (dev, staging, prod) without hardcoding URLs.
