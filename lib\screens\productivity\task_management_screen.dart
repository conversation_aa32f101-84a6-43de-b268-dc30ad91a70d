import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import '../../utils/design_tokens.dart';
import '../../services/navigation_service.dart';
import '../../blocs/tasks/tasks_bloc.dart';
import '../../blocs/tasks/tasks_event.dart';
import '../../blocs/tasks/tasks_state.dart';
import '../../models/isar_models.dart';
import 'models/task.dart';
import 'models/category.dart';
import 'components/expandable_calendar.dart';
import 'components/task_card.dart';
import 'components/categories_sidebar.dart';
import 'components/add_task_modal.dart';
import 'components/empty_state.dart';

enum TaskFilter { today, upcoming }

class TaskManagementScreen extends StatefulWidget {
  const TaskManagementScreen({super.key});

  @override
  State<TaskManagementScreen> createState() => _TaskManagementScreenState();
}

class _TaskManagementScreenState extends State<TaskManagementScreen>
    with TickerProviderStateMixin {

  // Controllers and focus nodes
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  // Animation controllers
  late AnimationController _sidebarController;
  late Animation<Offset> _sidebarSlideAnimation;

  // BLoC
  late TasksBloc _tasksBloc;

  // State variables
  List<TaskCategory> _customCategories = [];
  TaskFilter _currentFilter = TaskFilter.today;
  TaskCategory? _selectedCategory;
  String _searchQuery = '';
  bool _isSidebarVisible = false;
  bool _isCalendarExpanded = false;
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tasksBloc = GetIt.instance<TasksBloc>();
    _initializeAnimations();
    _searchController.addListener(_onSearchChanged);

    // Load tasks from BLoC
    _tasksBloc.add(const LoadTasks());
  }

  void _initializeAnimations() {
    _sidebarController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _sidebarSlideAnimation = Tween<Offset>(
      begin: const Offset(-1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _sidebarController,
      curve: Curves.easeInOut,
    ));
  }



  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  /// Convert LocalTask to Task and apply filters
  List<Task> _getFilteredTasks(List<LocalTask> localTasks) {
    // Convert LocalTask to Task
    var tasks = localTasks.map((localTask) => Task(
      id: localTask.id.toString(),
      title: localTask.title,
      description: localTask.description ?? '',
      dueDate: localTask.dueDate ?? DateTime.now(),
      category: TaskCategories.work, // Default category for now
      priority: TaskPriority.medium, // Default priority for now
      isCompleted: localTask.isCompleted,
      createdAt: localTask.createdAt,
      completedAt: localTask.isCompleted ? localTask.updatedAt : null,
    )).toList();

    // Apply category filter
    if (_selectedCategory != null) {
      tasks = tasks.where((task) => task.category.id == _selectedCategory!.id).toList();
    }

    // Apply time filter
    switch (_currentFilter) {
      case TaskFilter.today:
        tasks = tasks.where((task) => task.isDueToday || task.isOverdue).toList();
        break;
      case TaskFilter.upcoming:
        tasks = tasks.where((task) => task.isUpcoming).toList();
        break;
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      tasks = tasks.where((task) {
        return task.title.toLowerCase().contains(query) ||
               task.description.toLowerCase().contains(query) ||
               task.category.name.toLowerCase().contains(query);
      }).toList();
    }

    return tasks;
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _tasksBloc,
      child: Scaffold(
        backgroundColor: DesignTokens.backgroundApp,
        body: Stack(
          children: [
            _buildMainContent(),
            if (_isSidebarVisible) _buildSidebarOverlay(),
            if (_isSidebarVisible) _buildSidebar(),
          ],
        ),
        floatingActionButton: _buildFAB(),
      ),
    );
  }

  Widget _buildMainContent() {
    return SafeArea(
      child: GestureDetector(
        onHorizontalDragEnd: (details) {
          if (details.primaryVelocity! > 0) {
            _openSidebar();
          }
        },
        child: BlocBuilder<TasksBloc, TasksState>(
          builder: (context, state) {
            List<Task> filteredTasks = [];
            bool hasData = false;

            if (state is TasksLoaded) {
              filteredTasks = _getFilteredTasks(state.tasks);
              hasData = true;
            }

            return Column(
              children: [
                _buildHeader(),
                if (hasData && (filteredTasks.isNotEmpty || _searchQuery.isNotEmpty))
                  _buildExpandableCalendar(),
                if (hasData && (filteredTasks.isNotEmpty || _searchQuery.isNotEmpty))
                  const SizedBox(height: DesignTokens.spacingMd),
                _buildSearchBar(),
                _buildFilterTabs(),
                Expanded(
                  child: _buildTasksList(filteredTasks, state),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingLg,
        vertical: DesignTokens.spacingLg,
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: DesignTokens.iconPrimary),
            onPressed: () => GetIt.instance<NavigationService>().navigateBack(),
          ),
          Expanded(
            child: ShaderMask(
              shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
              child: Text(
                'Tasks',
                style: DesignTokens.therapySessionTitleStyle.copyWith(
                  color: Colors.white,
                  fontSize: 40,
                  fontWeight: FontWeight.w700,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.more_vert, color: DesignTokens.iconPrimary),
            onPressed: () {}, // TODO: Implement menu
          ),
        ],
      ),
    );
  }

  Widget _buildExpandableCalendar() {
    return ExpandableCalendar(
      isExpanded: _isCalendarExpanded,
      selectedDate: _selectedDate,
      onToggleExpanded: () {
        setState(() {
          _isCalendarExpanded = !_isCalendarExpanded;
        });
      },
      onDateSelected: (date) {
        setState(() {
          _selectedDate = date;
        });
      },
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: DesignTokens.backgroundCard.withOpacity(0.3),
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
              border: Border.all(
                color: Colors.white.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              style: DesignTokens.bodyStyle,
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: DesignTokens.bodyStyle.copyWith(
                  color: DesignTokens.textMuted,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: DesignTokens.iconSecondary,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: DesignTokens.spacingMd,
                  vertical: DesignTokens.spacingMd,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterTabs() {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      child: Row(
        children: [
          _buildFilterTab('Today', TaskFilter.today),
          const SizedBox(width: DesignTokens.spacingMd),
          _buildFilterTab('Upcoming', TaskFilter.upcoming),
        ],
      ),
    );
  }

  Widget _buildFilterTab(String label, TaskFilter filter) {
    final isActive = _currentFilter == filter;
    
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _currentFilter = filter;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(
            vertical: DesignTokens.spacingMd,
          ),
          decoration: BoxDecoration(
            color: isActive 
                ? DesignTokens.primaryInteractiveBlue.withOpacity(0.2)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
            border: Border.all(
              color: isActive 
                  ? DesignTokens.primaryInteractiveBlue
                  : Colors.transparent,
              width: 2,
            ),
          ),
          child: Text(
            label,
            style: DesignTokens.bodyStyle.copyWith(
              color: isActive 
                  ? DesignTokens.textPrimary 
                  : DesignTokens.textSecondary,
              fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildTasksList(List<Task> tasks, TasksState state) {
    if (state is TasksLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is TasksOperationError) {
      return Center(
        child: Text(
          'Error: ${state.error}',
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    if (tasks.isEmpty) {
      return EmptyState(
        searchQuery: _searchQuery,
        selectedCategory: _selectedCategory,
        onAddTask: _showAddTaskModal,
        onClearFilters: _clearFilters,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
      itemCount: tasks.length,
      itemBuilder: (context, index) {
        final task = tasks[index];
        return TaskCard(
          task: task,
          onToggleComplete: () => _toggleTaskComplete(task),
          onTap: () => _editTask(task),
        );
      },
    );
  }

  // Placeholder methods - will be implemented in components
  void _toggleTaskComplete(Task task) {
    _tasksBloc.add(ToggleTaskCompletion(task.id));
  }

  void _editTask(Task task) {
    // TODO: Navigate to task detail/edit
  }

  void _showAddTaskModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddTaskModal(
        customCategories: _customCategories,
        onTaskCreated: (task) {
          _tasksBloc.add(AddTask(
            title: task.title,
            description: task.description,
            dueDate: task.dueDate,
            priority: task.priority.name,
            category: task.category.name,
            tags: const [],
          ));
        },
      ),
    );
  }

  void _clearFilters() {
    setState(() {
      _selectedCategory = null;
      _searchController.clear();
      _currentFilter = TaskFilter.today;
    });
  }

  void _openSidebar() {
    setState(() {
      _isSidebarVisible = true;
    });
    _sidebarController.forward();
  }

  void _closeSidebar() {
    _sidebarController.reverse().then((_) {
      setState(() {
        _isSidebarVisible = false;
      });
    });
  }

  Widget _buildSidebarOverlay() {
    return GestureDetector(
      onTap: _closeSidebar,
      child: Container(
        color: Colors.black.withOpacity(0.5),
      ),
    );
  }

  Widget _buildSidebar() {
    return SlideTransition(
      position: _sidebarSlideAnimation,
      child: CategoriesSidebar(
        selectedCategory: _selectedCategory,
        customCategories: _customCategories,
        onCategorySelected: (category) {
          setState(() {
            _selectedCategory = category;
          });
          _closeSidebar();
        },
        onClose: _closeSidebar,
        onCategoryCreated: (category) {
          setState(() {
            _customCategories.add(category);
          });
        },
      ),
    );
  }

  Widget _buildFAB() {
    return FloatingActionButton(
      onPressed: _showAddTaskModal,
      backgroundColor: DesignTokens.primaryInteractiveBlue,
      child: const Icon(Icons.add, color: DesignTokens.iconPrimary),
    );
  }

  @override
  void dispose() {
    _sidebarController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }
}
