import 'package:flutter/material.dart';
import 'dart:ui';
import '../../../utils/design_tokens.dart';

class GlassmorphicInputField extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final String placeholder;
  final bool isSearchField;
  final bool isExpanded;
  final Function(String)? onSubmitted;
  final Function(bool)? onFocusChanged;
  final VoidCallback? onIconTap;

  const GlassmorphicInputField({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.placeholder,
    this.isSearchField = false,
    this.isExpanded = false,
    this.onSubmitted,
    this.onFocusChanged,
    this.onIconTap,
  });

  @override
  State<GlassmorphicInputField> createState() => _GlassmorphicInputFieldState();
}

class _GlassmorphicInputFieldState extends State<GlassmorphicInputField>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  
  bool _isFocused = false;
  bool _hasContent = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupListeners();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
  }

  void _setupListeners() {
    widget.focusNode.addListener(_onFocusChanged);
    widget.controller.addListener(_onTextChanged);
  }

  void _onFocusChanged() {
    setState(() {
      _isFocused = widget.focusNode.hasFocus;
    });
    
    if (widget.onFocusChanged != null) {
      widget.onFocusChanged!(_isFocused);
    }
    
    if (_isFocused) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  void _onTextChanged() {
    setState(() {
      _hasContent = widget.controller.text.isNotEmpty;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.isSearchField && _isFocused ? 1.02 : _scaleAnimation.value,
          child: Container(
            height: 48,
            decoration: BoxDecoration(
              color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              border: Border.all(
                color: _isFocused
                    ? DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.6)
                    : Colors.white.withValues(alpha: 0.1),
                width: _isFocused ? 2 : 1,
              ),
              boxShadow: _isFocused ? [
                BoxShadow(
                  color: DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.3 * _glowAnimation.value),
                  blurRadius: 15 * _glowAnimation.value,
                  offset: const Offset(0, 5),
                ),
              ] : null,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Row(
                  children: [
                    if (widget.isSearchField) ...[
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
                        child: Icon(
                          Icons.search,
                          color: _isFocused
                              ? DesignTokens.primaryInteractiveBlue
                              : DesignTokens.iconSecondary,
                          size: 20,
                        ),
                      ),
                    ] else ...[
                      const SizedBox(width: DesignTokens.spacingMd),
                    ],
                    Expanded(
                      child: TextField(
                        controller: widget.controller,
                        focusNode: widget.focusNode,
                        style: DesignTokens.bodyStyle.copyWith(
                          color: DesignTokens.textPrimary,
                        ),
                        decoration: InputDecoration(
                          hintText: widget.placeholder,
                          hintStyle: DesignTokens.bodyStyle.copyWith(
                            color: DesignTokens.textSecondary,
                          ),
                          filled: false, // Critical: prevents white background
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          contentPadding: EdgeInsets.zero,
                        ),
                        onSubmitted: widget.onSubmitted,
                        textInputAction: widget.isSearchField
                            ? TextInputAction.search
                            : TextInputAction.done,
                      ),
                    ),
                    if (_hasContent && widget.isSearchField) ...[
                      GestureDetector(
                        onTap: () {
                          widget.controller.clear();
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
                          child: Icon(
                            Icons.clear,
                            color: DesignTokens.iconSecondary,
                            size: 20,
                          ),
                        ),
                      ),
                    ] else if (!widget.isSearchField) ...[
                      GestureDetector(
                        onTap: widget.onIconTap,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
                          child: Icon(
                            Icons.add_link,
                            color: _isFocused
                                ? DesignTokens.primaryInteractiveBlue
                                : DesignTokens.iconSecondary,
                            size: 20,
                          ),
                        ),
                      ),
                    ] else ...[
                      const SizedBox(width: DesignTokens.spacingMd),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    widget.focusNode.removeListener(_onFocusChanged);
    widget.controller.removeListener(_onTextChanged);
    _animationController.dispose();
    super.dispose();
  }
}
