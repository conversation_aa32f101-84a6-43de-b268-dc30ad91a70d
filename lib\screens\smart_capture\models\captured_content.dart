import 'package:flutter/material.dart';

enum ContentType {
  link,
  image,
}

enum ProcessingStatus {
  processing,
  completed,
  error,
}

enum ContentFilter {
  all,
  links,
  images,
}

enum SortOption {
  chronological,
  alphabetical,
}

extension ContentFilterExtension on ContentFilter {
  String get displayName {
    switch (this) {
      case ContentFilter.all:
        return 'All';
      case ContentFilter.links:
        return 'Links';
      case ContentFilter.images:
        return 'Images';
    }
  }
}

extension SortOptionExtension on SortOption {
  String get displayName {
    switch (this) {
      case SortOption.chronological:
        return 'Recent';
      case SortOption.alphabetical:
        return 'A-Z';
    }
  }
}

class CapturedContent {
  final String id;
  final String url;
  final String title;
  final String summary;
  final String tag;
  final ContentType type;
  final ProcessingStatus status;
  final DateTime timestamp;
  final String? thumbnailUrl;
  final String? faviconUrl;

  const CapturedContent({
    required this.id,
    required this.url,
    required this.title,
    required this.summary,
    required this.tag,
    required this.type,
    required this.status,
    required this.timestamp,
    this.thumbnailUrl,
    this.faviconUrl,
  });

  CapturedContent copyWith({
    String? id,
    String? url,
    String? title,
    String? summary,
    String? tag,
    ContentType? type,
    ProcessingStatus? status,
    DateTime? timestamp,
    String? thumbnailUrl,
    String? faviconUrl,
  }) {
    return CapturedContent(
      id: id ?? this.id,
      url: url ?? this.url,
      title: title ?? this.title,
      summary: summary ?? this.summary,
      tag: tag ?? this.tag,
      type: type ?? this.type,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      faviconUrl: faviconUrl ?? this.faviconUrl,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'url': url,
      'title': title,
      'summary': summary,
      'tag': tag,
      'type': type.name,
      'status': status.name,
      'timestamp': timestamp.toIso8601String(),
      'thumbnailUrl': thumbnailUrl,
      'faviconUrl': faviconUrl,
    };
  }

  factory CapturedContent.fromJson(Map<String, dynamic> json) {
    return CapturedContent(
      id: json['id'] as String,
      url: json['url'] as String,
      title: json['title'] as String,
      summary: json['summary'] as String,
      tag: json['tag'] as String,
      type: ContentType.values.firstWhere((e) => e.name == json['type']),
      status: ProcessingStatus.values.firstWhere((e) => e.name == json['status']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      thumbnailUrl: json['thumbnailUrl'] as String?,
      faviconUrl: json['faviconUrl'] as String?,
    );
  }

  /// Create from API response
  factory CapturedContent.fromApi(Map<String, dynamic> json) {
    return CapturedContent(
      id: json['id']?.toString() ?? '',
      url: json['url']?.toString() ?? '',
      title: json['title']?.toString() ?? 'Untitled',
      summary: json['summary']?.toString() ?? 'No summary available',
      tag: json['tag']?.toString() ?? (json['tags'] as List?)?.first?.toString() ?? 'General',
      type: _parseContentType(json['content_type']?.toString() ?? json['type']?.toString()),
      status: _parseProcessingStatus(json['processing_status']?.toString() ?? json['status']?.toString()),
      timestamp: _parseTimestamp(json['created_at'] ?? json['timestamp']),
      thumbnailUrl: json['thumbnail_url']?.toString() ?? json['thumbnailUrl']?.toString(),
      faviconUrl: json['favicon_url']?.toString() ?? json['faviconUrl']?.toString(),
    );
  }

  static ContentType _parseContentType(String? type) {
    if (type == null) return ContentType.link;

    switch (type.toLowerCase()) {
      case 'image':
      case 'img':
        return ContentType.image;
      case 'link':
      case 'url':
      case 'text':
      default:
        return ContentType.link;
    }
  }

  static ProcessingStatus _parseProcessingStatus(String? status) {
    if (status == null) return ProcessingStatus.processing;

    switch (status.toLowerCase()) {
      case 'completed':
      case 'complete':
      case 'success':
        return ProcessingStatus.completed;
      case 'processing':
      case 'pending':
      case 'in_progress':
        return ProcessingStatus.processing;
      case 'error':
      case 'failed':
      case 'failure':
        return ProcessingStatus.error;
      default:
        return ProcessingStatus.processing;
    }
  }

  static DateTime _parseTimestamp(dynamic timestamp) {
    if (timestamp == null) return DateTime.now();

    if (timestamp is String) {
      try {
        return DateTime.parse(timestamp);
      } catch (e) {
        return DateTime.now();
      }
    }

    if (timestamp is int) {
      try {
        // Handle both milliseconds and seconds timestamps
        final dt = timestamp > 1000000000000
            ? DateTime.fromMillisecondsSinceEpoch(timestamp)
            : DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
        return dt;
      } catch (e) {
        return DateTime.now();
      }
    }

    return DateTime.now();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CapturedContent && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CapturedContent(id: $id, title: $title, type: $type, status: $status)';
  }
}
