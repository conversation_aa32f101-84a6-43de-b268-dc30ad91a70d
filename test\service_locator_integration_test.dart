import 'package:flutter_test/flutter_test.dart';
import 'package:darvis_app/services/service_locator.dart';
import 'package:darvis_app/services/auth_service_interface.dart';
import 'package:darvis_app/services/api_service.dart';
import 'package:darvis_app/services/sync_engine.dart';
import 'package:darvis_app/blocs/dashboard/dashboard_bloc.dart';
import 'package:darvis_app/blocs/dashboard/dashboard_state.dart';
import 'package:dio/dio.dart';

void main() {
  group('Service Locator Integration Tests', () {
    setUp(() async {
      // Reset service locator before each test
      await getIt.reset();
    });

    test('should setup all services without circular dependency errors', () async {
      // Act
      await setupServiceLocator();

      // Assert - All services should be registered successfully
      expect(getIt.isRegistered<Dio>(), true);
      expect(getIt.isRegistered<ApiService>(), true);
      expect(getIt.isRegistered<AuthServiceInterface>(), true);
      expect(getIt.isRegistered<SyncEngine>(), true);
      expect(getIt.isRegistered<DashboardBloc>(), true);
    });

    test('should create AuthService without circular dependency', () async {
      // Arrange
      await setupServiceLocator();

      // Act
      final authService = getIt<AuthServiceInterface>();

      // Assert
      expect(authService, isNotNull);
      expect(authService.isAuthenticated, false); // Should be false initially
    });

    test('should create ApiService with Dio instance', () async {
      // Arrange
      await setupServiceLocator();

      // Act
      final apiService = getIt<ApiService>();
      final dio = getIt<Dio>();

      // Assert
      expect(apiService, isNotNull);
      expect(dio, isNotNull);
      expect(dio.interceptors.length, greaterThan(0)); // Should have interceptors including AuthInterceptor
    });

    test('should create SyncEngine successfully', () async {
      // Arrange
      await setupServiceLocator();

      // Act
      final syncEngine = getIt<SyncEngine>();

      // Assert
      expect(syncEngine, isNotNull);
    });

    test('should create DashboardBloc successfully', () async {
      // Arrange
      await setupServiceLocator();

      // Act
      final dashboardBloc = getIt<DashboardBloc>();

      // Assert
      expect(dashboardBloc, isNotNull);
      expect(dashboardBloc.state, isA<DashboardState>());
    });

    test('should verify AuthInterceptor is added to Dio', () async {
      // Arrange
      await setupServiceLocator();

      // Act
      final dio = getIt<Dio>();

      // Assert
      expect(dio.interceptors.length, greaterThan(0));
      
      // Check if AuthInterceptor is present (it should be the last one added)
      final hasAuthInterceptor = dio.interceptors.any((interceptor) => 
          interceptor.toString().contains('AuthInterceptor'));
      expect(hasAuthInterceptor, true);
    });

    test('should handle service dependencies correctly', () async {
      // Arrange
      await setupServiceLocator();

      // Act
      final authService = getIt<AuthServiceInterface>();
      final apiService = getIt<ApiService>();
      final dashboardBloc = getIt<DashboardBloc>();

      // Assert - Services should be properly initialized and connected
      expect(authService, isNotNull);
      expect(apiService, isNotNull);
      expect(dashboardBloc, isNotNull);
      
      // Verify that services can be used without throwing exceptions
      expect(() => authService.isAuthenticated, returnsNormally);
      expect(() => apiService.toString(), returnsNormally);
      expect(() => dashboardBloc.state, returnsNormally);
    });

    test('should create multiple instances of factory services', () async {
      // Arrange
      await setupServiceLocator();

      // Act
      final dashboardBloc1 = getIt<DashboardBloc>();
      final dashboardBloc2 = getIt<DashboardBloc>();

      // Assert - Factory services should create new instances
      expect(dashboardBloc1, isNotNull);
      expect(dashboardBloc2, isNotNull);
      expect(identical(dashboardBloc1, dashboardBloc2), false);
    });

    test('should reuse singleton instances', () async {
      // Arrange
      await setupServiceLocator();

      // Act
      final authService1 = getIt<AuthServiceInterface>();
      final authService2 = getIt<AuthServiceInterface>();
      final apiService1 = getIt<ApiService>();
      final apiService2 = getIt<ApiService>();

      // Assert - Singleton services should return same instances
      expect(identical(authService1, authService2), true);
      expect(identical(apiService1, apiService2), true);
    });
  });
}
