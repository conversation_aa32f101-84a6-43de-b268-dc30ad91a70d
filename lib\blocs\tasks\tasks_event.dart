import 'package:equatable/equatable.dart';

/// Base class for all tasks events
abstract class TasksEvent extends Equatable {
  const TasksEvent();

  @override
  List<Object?> get props => [];
}

/// Load all tasks
class LoadTasks extends TasksEvent {
  const LoadTasks();
}

/// Refresh tasks from server
class RefreshTasks extends TasksEvent {
  const RefreshTasks();
}

/// Add a new task
class AddTask extends TasksEvent {
  final String title;
  final String description;
  final DateTime? dueDate;
  final String priority;
  final String category;
  final List<String> tags;

  const AddTask({
    required this.title,
    required this.description,
    this.dueDate,
    this.priority = 'medium',
    this.category = 'general',
    this.tags = const [],
  });

  @override
  List<Object?> get props => [title, description, dueDate, priority, category, tags];
}

/// Update an existing task
class UpdateTask extends TasksEvent {
  final String taskId;
  final String? title;
  final String? description;
  final DateTime? dueDate;
  final String? priority;
  final String? category;
  final List<String>? tags;
  final bool? isCompleted;

  const UpdateTask({
    required this.taskId,
    this.title,
    this.description,
    this.dueDate,
    this.priority,
    this.category,
    this.tags,
    this.isCompleted,
  });

  @override
  List<Object?> get props => [taskId, title, description, dueDate, priority, category, tags, isCompleted];
}

/// Delete a task
class DeleteTask extends TasksEvent {
  final String taskId;

  const DeleteTask(this.taskId);

  @override
  List<Object?> get props => [taskId];
}

/// Toggle task completion status
class ToggleTaskCompletion extends TasksEvent {
  final String taskId;

  const ToggleTaskCompletion(this.taskId);

  @override
  List<Object?> get props => [taskId];
}

/// Search tasks by query
class SearchTasks extends TasksEvent {
  final String query;

  const SearchTasks(this.query);

  @override
  List<Object?> get props => [query];
}

/// Filter tasks by status
class FilterTasksByStatus extends TasksEvent {
  final TaskStatusFilter? status;

  const FilterTasksByStatus(this.status);

  @override
  List<Object?> get props => [status];
}

/// Filter tasks by priority
class FilterTasksByPriority extends TasksEvent {
  final String? priority;

  const FilterTasksByPriority(this.priority);

  @override
  List<Object?> get props => [priority];
}

/// Filter tasks by category
class FilterTasksByCategory extends TasksEvent {
  final String? category;

  const FilterTasksByCategory(this.category);

  @override
  List<Object?> get props => [category];
}

/// Filter tasks by due date
class FilterTasksByDueDate extends TasksEvent {
  final TaskDueDateFilter? dueDateFilter;

  const FilterTasksByDueDate(this.dueDateFilter);

  @override
  List<Object?> get props => [dueDateFilter];
}

/// Sort tasks
class SortTasks extends TasksEvent {
  final TasksSortType sortType;

  const SortTasks(this.sortType);

  @override
  List<Object?> get props => [sortType];
}

/// Clear search and filters
class ClearTasksFilters extends TasksEvent {
  const ClearTasksFilters();
}

/// Select/deselect task for bulk operations
class ToggleTaskSelection extends TasksEvent {
  final String taskId;

  const ToggleTaskSelection(this.taskId);

  @override
  List<Object?> get props => [taskId];
}

/// Clear all selections
class ClearTaskSelections extends TasksEvent {
  const ClearTaskSelections();
}

/// Delete selected tasks
class DeleteSelectedTasks extends TasksEvent {
  const DeleteSelectedTasks();
}

/// Complete selected tasks
class CompleteSelectedTasks extends TasksEvent {
  const CompleteSelectedTasks();
}

/// Set priority for selected tasks
class SetPriorityForSelectedTasks extends TasksEvent {
  final String priority;

  const SetPriorityForSelectedTasks(this.priority);

  @override
  List<Object?> get props => [priority];
}

/// Set category for selected tasks
class SetCategoryForSelectedTasks extends TasksEvent {
  final String category;

  const SetCategoryForSelectedTasks(this.category);

  @override
  List<Object?> get props => [category];
}

/// Add tag to selected tasks
class AddTagToSelectedTasks extends TasksEvent {
  final String tag;

  const AddTagToSelectedTasks(this.tag);

  @override
  List<Object?> get props => [tag];
}

/// Export tasks
class ExportTasks extends TasksEvent {
  final List<String>? taskIds; // null means export all
  final String format; // 'json', 'csv', 'txt'

  const ExportTasks({
    this.taskIds,
    this.format = 'json',
  });

  @override
  List<Object?> get props => [taskIds, format];
}

/// Sync tasks with server
class SyncTasks extends TasksEvent {
  const SyncTasks();
}

/// Task status filters
enum TaskStatusFilter {
  all,
  pending,
  completed,
  overdue,
}

/// Task due date filters
enum TaskDueDateFilter {
  all,
  today,
  tomorrow,
  thisWeek,
  thisMonth,
  overdue,
  noDate,
}

/// Task sort types
enum TasksSortType {
  createdAtDesc,
  createdAtAsc,
  updatedAtDesc,
  updatedAtAsc,
  titleAsc,
  titleDesc,
  dueDateAsc,
  dueDateDesc,
  priorityHigh,
  priorityLow,
  status,
}
