import 'package:flutter_test/flutter_test.dart';
import 'package:darvis_app/services/data_service.dart';
import 'package:darvis_app/services/api_service.dart';
import 'package:darvis_app/services/vcf_service.dart';
import 'package:get_it/get_it.dart';
import '../mock_impl/mock_api_service.dart';

void main() {
  group('Contact Management Backend Integration Tests', () {
    late DataService dataService;
    late MockApiService mockApiService;
    late VcfService vcfService;

    setUpAll(() {
      // Setup service locator with mock API service
      GetIt.instance.reset();
      mockApiService = MockApiService();
      GetIt.instance.registerSingleton<ApiService>(mockApiService);
      
      dataService = DataService();
      vcfService = VcfService();
    });

    tearDown(() {
      // Reset state between tests
      dataService = DataService();
    });

    group('Contact CRUD Operations', () {
      test('should create contact via backend API', () async {
        // Arrange
        final contact = Contact(
          id: '',
          name: 'Test User',
          phone: '+1234567890',
          email: '<EMAIL>',
          location: 'Test Location',
          metAt: 'Test Meeting',
          socialMedia: {'twitter': 'testuser'},
          memoryPrompt: 'Test memory prompt',
          createdAt: DateTime.now(),
        );

        // Act
        final result = await dataService.addContact(contact);

        // Assert
        expect(result, isNotNull);
        expect(result!.name, equals('Test User'));
        expect(result.phone, equals('+1234567890'));
        expect(result.socialMedia['twitter'], equals('testuser'));
        expect(result.id, isNotEmpty);
      });

      test('should load contacts from backend API', () async {
        // Act
        await dataService.loadContacts();

        // Assert
        final contacts = dataService.contacts;
        expect(contacts, isNotEmpty);
        expect(contacts.length, greaterThan(0));
        
        // Verify contact structure
        final firstContact = contacts.first;
        expect(firstContact.id, isNotEmpty);
        expect(firstContact.name, isNotEmpty);
        expect(firstContact.phone, isNotEmpty);
      });

      test('should update contact via backend API', () async {
        // Arrange
        await dataService.loadContacts();
        final originalContact = dataService.contacts.first;
        final updatedContact = originalContact.copyWith(
          name: 'Updated Name',
          memoryPrompt: 'Updated memory prompt',
        );

        // Act
        final result = await dataService.updateContact(updatedContact);

        // Assert
        expect(result, isNotNull);
        expect(result!.name, equals('Updated Name'));
        expect(result.memoryPrompt, equals('Updated memory prompt'));
        expect(result.id, equals(originalContact.id));
      });

      test('should delete contact via backend API', () async {
        // Arrange
        await dataService.loadContacts();
        final initialCount = dataService.contacts.length;
        final contactToDelete = dataService.contacts.first;

        // Act
        await dataService.removeContact(contactToDelete.id);

        // Assert
        final finalCount = dataService.contacts.length;
        expect(finalCount, equals(initialCount - 1));
        
        // Verify contact is no longer in list
        final remainingContacts = dataService.contacts;
        expect(remainingContacts.any((c) => c.id == contactToDelete.id), isFalse);
      });
    });

    group('Contact Search Integration', () {
      test('should search contacts via backend API', () async {
        // Arrange
        const searchQuery = 'Sarah';

        // Act
        final results = await dataService.searchContacts(searchQuery);

        // Assert
        expect(results, isNotEmpty);
        expect(results.every((contact) => 
          contact.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          contact.location.toLowerCase().contains(searchQuery.toLowerCase()) ||
          contact.memoryPrompt.toLowerCase().contains(searchQuery.toLowerCase())
        ), isTrue);
      });

      test('should return all contacts for empty search query', () async {
        // Act
        final results = await dataService.searchContacts('');

        // Assert
        expect(results, isNotEmpty);
        expect(results.length, greaterThan(0));
      });

      test('should handle search errors gracefully', () async {
        // This test would require mocking a search error
        // For now, we test that search doesn't throw
        expect(() async => await dataService.searchContacts('test'), returnsNormally);
      });
    });

    group('VCF Generation Integration', () {
      test('should generate VCF content via backend API', () async {
        // Arrange
        await dataService.loadContacts();
        final contact = dataService.contacts.first;

        // Act
        final vcfContent = await vcfService.generateVcfContent(contact);

        // Assert
        expect(vcfContent, isNotEmpty);
        expect(vcfContent, contains('BEGIN:VCARD'));
        expect(vcfContent, contains('END:VCARD'));
        expect(vcfContent, contains('VERSION:3.0'));
        expect(vcfContent, contains('FN:${contact.name}'));
        expect(vcfContent, contains('TEL;TYPE=CELL:${contact.phone}'));
      });

      test('should save VCF file to device', () async {
        // Arrange
        await dataService.loadContacts();
        final contact = dataService.contacts.first;

        // Act
        final filePath = await vcfService.saveVcfFile(contact);

        // Assert
        expect(filePath, isNotEmpty);
        expect(filePath, endsWith('.vcf'));
        expect(filePath, contains(contact.name.replaceAll(' ', '_')));
      });

      test('should generate VCF with social media information', () async {
        // Arrange
        final contact = Contact(
          id: 'test_id',
          name: 'Social Test User',
          phone: '+1234567890',
          socialMedia: {'twitter': 'socialuser', 'instagram': 'socialuser_ig'},
          memoryPrompt: 'Met at social media conference',
          createdAt: DateTime.now(),
        );

        // Act
        final vcfContent = await vcfService.generateVcfContent(contact);

        // Assert
        expect(vcfContent, contains('URL:https://twitter.com/socialuser'));
        expect(vcfContent, contains('NOTE:Met at social media conference'));
      });
    });

    group('Device Sync Integration', () {
      test('should update device sync status', () async {
        // Arrange
        await dataService.loadContacts();
        final contact = dataService.contacts.first;

        // Act
        await dataService.updateContactDeviceSync(
          contactId: contact.id,
          syncStatus: 'synced',
          deviceContactId: 'device_123',
        );

        // Assert
        // Verify the sync status was updated
        final updatedContact = await dataService.getContactById(contact.id);
        expect(updatedContact, isNotNull);
        expect(updatedContact!.deviceSyncStatus, equals('synced'));
      });

      test('should handle sync errors properly', () async {
        // Arrange
        await dataService.loadContacts();
        final contact = dataService.contacts.first;

        // Act
        await dataService.updateContactDeviceSync(
          contactId: contact.id,
          syncStatus: 'failed',
          syncError: 'Permission denied',
        );

        // Assert - Should not throw and should update status
        expect(() async => await dataService.updateContactDeviceSync(
          contactId: contact.id,
          syncStatus: 'failed',
          syncError: 'Test error',
        ), returnsNormally);
      });
    });

    group('Error Handling', () {
      test('should handle API errors gracefully', () async {
        // Test that operations don't crash on errors
        expect(() async => await dataService.addContact(Contact(
          id: '',
          name: '',
          phone: '',
          createdAt: DateTime.now(),
        )), returnsNormally);
      });

      test('should provide meaningful error messages', () async {
        try {
          await dataService.getContactById('nonexistent_id');
        } catch (e) {
          expect(e.toString(), isNotEmpty);
        }
      });
    });

    group('Data Model Compatibility', () {
      test('should handle Contact.fromApi conversion correctly', () async {
        // Arrange
        final apiResponse = {
          'id': 'test_123',
          'name': 'API Test User',
          'phone': '+1234567890',
          'email': '<EMAIL>',
          'location': 'API Location',
          'met_at': 'API Meeting',
          'social_media': {'twitter': 'apiuser'},
          'memory_prompt': 'API memory prompt',
          'image_path': 'https://example.com/image.jpg',
          'device_sync_status': 'synced',
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        // Act
        final contact = Contact.fromApi(apiResponse);

        // Assert
        expect(contact.id, equals('test_123'));
        expect(contact.name, equals('API Test User'));
        expect(contact.phone, equals('+1234567890'));
        expect(contact.email, equals('<EMAIL>'));
        expect(contact.socialMedia['twitter'], equals('apiuser'));
        expect(contact.deviceSyncStatus, equals('synced'));
      });

      test('should handle Contact.toApi conversion correctly', () async {
        // Arrange
        final contact = Contact(
          id: 'test_123',
          name: 'Test User',
          phone: '+1234567890',
          email: '<EMAIL>',
          location: 'Test Location',
          metAt: 'Test Meeting',
          socialMedia: {'instagram': 'testuser'},
          memoryPrompt: 'Test memory',
          deviceSyncStatus: 'pending',
          createdAt: DateTime.now(),
        );

        // Act
        final apiData = contact.toApi();

        // Assert
        expect(apiData['name'], equals('Test User'));
        expect(apiData['phone'], equals('+1234567890'));
        expect(apiData['email'], equals('<EMAIL>'));
        expect(apiData['social_media']['instagram'], equals('testuser'));
        expect(apiData['device_sync_status'], equals('pending'));
      });
    });
  });
}
