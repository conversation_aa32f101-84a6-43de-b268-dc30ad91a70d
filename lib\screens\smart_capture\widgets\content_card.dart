import 'package:flutter/material.dart';
import 'dart:ui';
import '../../../utils/design_tokens.dart';
import '../models/captured_content.dart';

class ContentCard extends StatefulWidget {
  final CapturedContent content;
  final bool isSelected;
  final bool isBulkSelectionMode;
  final VoidCallback onTap;
  final VoidCallback onLongPress;
  final Function(bool) onSelectionChanged;
  final VoidCallback onRetry;
  final Function(String) onTagEdit;

  const ContentCard({
    super.key,
    required this.content,
    required this.isSelected,
    required this.isBulkSelectionMode,
    required this.onTap,
    required this.onLongPress,
    required this.onSelectionChanged,
    required this.onRetry,
    required this.onTagEdit,
  });

  @override
  State<ContentCard> createState() => _ContentCardState();
}

class _ContentCardState extends State<ContentCard>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  
  bool _isHovered = false;
  bool _isEditingTag = false;
  final TextEditingController _tagController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _tagController.text = widget.content.tag;
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.linear,
    ));
    
    if (widget.content.status == ProcessingStatus.processing) {
      _animationController.repeat();
    }
  }

  @override
  void didUpdateWidget(ContentCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.content.status != widget.content.status) {
      if (widget.content.status == ProcessingStatus.processing) {
        _animationController.repeat();
      } else {
        _animationController.stop();
        _animationController.reset();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _isHovered ? _scaleAnimation.value : 1.0,
          child: GestureDetector(
            onTap: widget.onTap,
            onLongPress: widget.onLongPress,
            onTapDown: (_) => setState(() => _isHovered = true),
            onTapUp: (_) => setState(() => _isHovered = false),
            onTapCancel: () => setState(() => _isHovered = false),
            child: Container(
              width: double.infinity,
              constraints: const BoxConstraints(minHeight: 120),
              margin: const EdgeInsets.symmetric(vertical: DesignTokens.spacingSm),
              decoration: BoxDecoration(
                color: _getCardColor().withValues(alpha: 0.3),
                borderRadius: _getAsymmetricBorderRadius(),
                border: Border.all(
                  color: widget.isSelected
                      ? DesignTokens.primaryInteractiveBlue
                      : Colors.white.withValues(alpha: 0.1),
                  width: widget.isSelected ? 2 : 1,
                ),
                boxShadow: [
                  if (_isHovered || widget.isSelected)
                    BoxShadow(
                      color: DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.2),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                ],
              ),
              child: ClipRRect(
                borderRadius: _getAsymmetricBorderRadius(),
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: widget.content.status == ProcessingStatus.processing ? 5 : 20,
                    sigmaY: widget.content.status == ProcessingStatus.processing ? 5 : 20,
                  ),
                  child: Stack(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(DesignTokens.spacingMd),
                        child: _buildCardContent(),
                      ),
                      if (widget.isBulkSelectionMode)
                        _buildSelectionOverlay(),
                      if (widget.content.status == ProcessingStatus.processing)
                        _buildProcessingOverlay(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  BorderRadius _getAsymmetricBorderRadius() {
    // Alternate sharp corners like notes/tasks
    final index = widget.content.id.hashCode % 2;
    if (index == 0) {
      return const BorderRadius.only(
        topLeft: Radius.circular(4),
        topRight: Radius.circular(DesignTokens.borderRadiusLg),
        bottomLeft: Radius.circular(DesignTokens.borderRadiusLg),
        bottomRight: Radius.circular(DesignTokens.borderRadiusLg),
      );
    } else {
      return const BorderRadius.only(
        topLeft: Radius.circular(DesignTokens.borderRadiusLg),
        topRight: Radius.circular(4),
        bottomLeft: Radius.circular(DesignTokens.borderRadiusLg),
        bottomRight: Radius.circular(DesignTokens.borderRadiusLg),
      );
    }
  }

  Color _getCardColor() {
    // Use same colors as action buttons
    // First color: "Add to Notes" button color (primaryInteractiveBlue)
    // Second color: "Chat with Darvis" button color (primaryAccentBlue)

    // Alternate colors based on content ID
    final index = widget.content.id.hashCode % 2;
    return index == 0
        ? DesignTokens.primaryInteractiveBlue
        : DesignTokens.primaryAccentBlue;
  }

  Widget _buildCardContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Row 1: Favicon/Thumbnail + Title
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildThumbnail(),
            const SizedBox(width: DesignTokens.spacingMd),
            Expanded(
              child: _buildTitle(),
            ),
          ],
        ),
        
        const SizedBox(height: DesignTokens.spacingSm),
        
        // Row 2: AI Summary
        if (widget.content.summary.isNotEmpty)
          _buildSummary(),
        
        const SizedBox(height: DesignTokens.spacingSm),
        
        // Row 3: Tag + Processing indicator/timestamp
        Row(
          children: [
            _buildTag(),
            const Spacer(),
            _buildStatusIndicator(),
          ],
        ),
      ],
    );
  }

  Widget _buildThumbnail() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Center(
        child: Icon(
          widget.content.type == ContentType.image
              ? Icons.image_outlined
              : Icons.link,
          color: DesignTokens.textPrimary,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      widget.content.title,
      style: DesignTokens.cardTitleStyle.copyWith(
        fontSize: 16,
        height: 1.3,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSummary() {
    return Text(
      widget.content.summary,
      style: DesignTokens.bodyStyle.copyWith(
        color: DesignTokens.textSecondary,
        height: 1.4,
      ),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildTag() {
    if (_isEditingTag) {
      return _buildTagEditor();
    }
    
    if (widget.content.tag.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _isEditingTag = true;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingSm,
          vertical: DesignTokens.spacingXs,
        ),
        decoration: BoxDecoration(
          color: DesignTokens.primaryAccentBlue.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
          border: Border.all(
            color: DesignTokens.primaryAccentBlue.withValues(alpha: 0.5),
            width: 1,
          ),
        ),
        child: Text(
          '#${widget.content.tag.toLowerCase()}',
          style: DesignTokens.bodyStyle.copyWith(
            color: DesignTokens.primaryAccentBlue,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildTagEditor() {
    return Container(
      width: 120,
      height: 32,
      padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingSm),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
        border: Border.all(
          color: DesignTokens.primaryInteractiveBlue,
          width: 1,
        ),
      ),
      child: TextField(
        controller: _tagController,
        style: DesignTokens.bodyStyle.copyWith(
          color: DesignTokens.textPrimary,
          fontSize: 12,
        ),
        decoration: const InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.zero,
        ),
        autofocus: true,
        onSubmitted: (value) {
          widget.onTagEdit(value.trim());
          setState(() {
            _isEditingTag = false;
          });
        },
        onTapOutside: (_) {
          setState(() {
            _isEditingTag = false;
          });
        },
      ),
    );
  }

  Widget _buildStatusIndicator() {
    switch (widget.content.status) {
      case ProcessingStatus.processing:
        return AnimatedBuilder(
          animation: _rotationAnimation,
          child: SizedBox(
            width: 20,
            height: 20,
            child: Image.asset(
              'assets/images/darvisintelligence.png',
              width: 20,
              height: 20,
            ),
          ),
          builder: (context, child) {
            return Transform.rotate(
              angle: _rotationAnimation.value * 2 * 3.14159,
              child: child,
            );
          },
        );
      case ProcessingStatus.error:
        return GestureDetector(
          onTap: widget.onRetry,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingSm,
              vertical: DesignTokens.spacingXs,
            ),
            decoration: BoxDecoration(
              color: DesignTokens.priorityHigh.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.refresh,
                  color: DesignTokens.priorityHigh,
                  size: 16,
                ),
                const SizedBox(width: DesignTokens.spacingXs),
                Text(
                  'Retry',
                  style: DesignTokens.bodyStyle.copyWith(
                    color: DesignTokens.priorityHigh,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        );
      case ProcessingStatus.completed:
        return Text(
          _formatTimestamp(widget.content.timestamp),
          style: DesignTokens.bodyStyle.copyWith(
            color: DesignTokens.textMuted,
            fontSize: 12,
          ),
        );
    }
  }

  Widget _buildSelectionOverlay() {
    return Positioned(
      top: DesignTokens.spacingSm,
      right: DesignTokens.spacingSm,
      child: AnimatedScale(
        scale: widget.isSelected ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 200),
        curve: Curves.elasticOut,
        child: Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: DesignTokens.primaryInteractiveBlue,
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white,
              width: 2,
            ),
          ),
          child: const Icon(
            Icons.check,
            color: Colors.white,
            size: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildProcessingOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.1),
          borderRadius: _getAsymmetricBorderRadius(),
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tagController.dispose();
    super.dispose();
  }
}
