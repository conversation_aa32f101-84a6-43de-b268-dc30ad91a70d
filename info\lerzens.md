## Contextual Definition for AI Coding Agent

### **Feature: Quick Add Long Press Interaction**

**Design System:** Dark theme using `DesignTokens`, overlay modal with floating action buttons, navbar integration

---

## **INTERACTION TRIGGER & TIMING**

### **Long Press Detection:**
- **Target element:** Darvis button in navbar (center diamond logo)
- **Trigger timing:** 1.5 seconds long press activation
- **Pre-feedback:** 0.5 seconds - subtle button scale + glow hint
- **Full activation:** 1.5 seconds - complete gradient change + overlay appearance
- **Release behavior:** If user lifts finger before 1.5s, cancel gracefully with reverse animation

### **Visual Feedback Sequence:**
1. **0.5s:** Darvis button scales to 1.05x with subtle glow around edges
2. **1.5s:** Full gradient change from `appNameHeaderGradient` to `backgroundGlowBlueGradient`
3. **1.6s:** Darkened overlay appears across entire screen
4. **1.7s:** Left button (Add a Note) springs in from Darvis button position
5. **1.85s:** Right button (Add a Task) springs in from Darvis button position

---

## **OVERLAY SYSTEM**

### **Background Overlay:**
- **Coverage:** Full screen overlay on current content
- **Color:** Black with 0.6 opacity for darkened effect
- **Animation:** Fade in over 200ms ease-out
- **Touch behavior:** Tap anywhere on overlay to dismiss
- **Blur effect:** Optional subtle blur (2px) for depth

### **Dismissal Behavior:**
- **Tap outside buttons:** Dismisses overlay and returns to normal state
- **Animation:** Reverse sequence - buttons disappear, overlay fades, gradient returns
- **Timing:** 300ms total dismissal animation
- **Darvis button:** Returns to `appNameHeaderGradient` smoothly

---

## **FLOATING ACTION BUTTONS**

### **Button Specifications:**
- **Size:** 60px diameter circles
- **Background:** `primaryInteractiveBlue` (#004EEB)
- **Border:** 2px white border with 0.3 opacity
- **Shadow:** Subtle elevation shadow for lift effect
- **Touch target:** 68px for accessibility compliance

### **Button Positioning:**
- **Base distance:** 120px from Darvis button center (horizontal)
- **Responsive positioning:**
  - Small screens (≤375px width): 100px distance
  - Standard screens (376-414px width): 120px distance
  - Large screens (≥415px width): 140px distance
- **Vertical alignment:** Same level as Darvis button center
- **Left button:** Add a Note functionality
- **Right button:** Add a Task functionality

### **Icon Implementation:**
- **Left button icon:** `note_navbar` asset, 24px size, white color
- **Right button icon:** `task_navbar` asset, 24px size, white color
- **Icon positioning:** Centered within button circle

### **Text Descriptions:**
- **Left button text:** "Add a Note"
- **Right button text:** "Add a Task"
- **Typography:** 12px size using `textPrimary` color
- **Positioning:** Centered below each button with 8px gap
- **Total button height:** 60px button + 8px gap + 20px text = 88px total

---

## **ANIMATION SPECIFICATIONS**

### **Button Appearance Animation:**
- **Animation type:** Spring animation with overshoot effect
- **Scale overshoot:** Buttons appear with 1.2x scale, settle to 1.0x
- **Duration:** 400ms with spring curve
- **Staggered timing:** Left button starts, right button follows 150ms later
- **Text animation:** Fade in 100ms after respective button with opacity 0→1

### **Button Interaction States:**
- **Press state:** Scale to 0.95x with glow intensity increase
- **Release:** Return to normal scale (200ms)
- **Haptic feedback:** Light impact on press
- **Navigation:** Immediate screen transition to respective feature

### **Dismissal Animation:**
- **Trigger:** Tap on overlay area outside buttons
- **Sequence:** Buttons and text fade out simultaneously (200ms)
- **Overlay:** Fades out after buttons disappear
- **Darvis button:** Gradient morphs back to `appNameHeaderGradient` (300ms)

---

## **HAPTIC FEEDBACK SEQUENCE**

### **Feedback Timeline:**
- **0.5s (hint):** Light impact vibration
- **1.5s (activation):** Medium impact vibration
- **Button press:** Light impact vibration
- **Dismissal:** No haptic (visual feedback only)

---

## **NAVIGATION INTEGRATION**

### **Target Destinations:**
- **Add a Note:** Navigate to Notes creation screen with new note ready
- **Add a Task:** Navigate to Tasks creation screen with new task modal
- **Transition:** Standard screen slide transition (350ms ease-out)
- **State preservation:** Current screen state preserved for return navigation

### **Cross-Screen Availability:**
- **Available on:** All main app screens (Home, Calendar, Inbox, Profile)
- **Not available on:** Modal screens, settings screens, onboarding flows
- **State management:** Overlay dismisses automatically during navigation

---

## **RESPONSIVE DESIGN**

### **Screen Size Adaptations:**
```
Small devices (iPhone SE): 100px button distance
Standard devices: 120px button distance  
Large devices (Pro Max): 140px button distance
```

### **Safe Area Handling:**
- **Positioning:** Buttons respect screen safe areas
- **Overflow prevention:** Ensure buttons don't extend beyond screen edges
- **Accessibility:** Maintain 68px touch targets regardless of screen size

---

## **ERROR HANDLING & EDGE CASES**

### **Interrupted Gestures:**
- **Screen rotation:** Cancel overlay and return to normal state
- **App backgrounding:** Clean dismissal of overlay
- **Navigation during animation:** Gracefully cancel and proceed with navigation
- **Multiple rapid presses:** Debounce to prevent duplicate activations

### **Performance Considerations:**
- **Memory management:** Proper disposal of overlay when dismissed
- **Animation performance:** Maintain 60fps during all transitions
- **Battery optimization:** Minimal impact due to short interaction duration

---

## **ACCESSIBILITY SUPPORT**

### **Touch Targets:**
- **Minimum 68px** touch area for all interactive elements
- **Clear visual boundaries** for button areas
- **Proper contrast** between button and overlay background

### **VoiceOver Support:**
- **Announce overlay state:** "Quick actions available"
- **Button labels:** "Add a Note button" and "Add a Task button"
- **Dismissal instruction:** "Tap outside to close"

---

## **TECHNICAL IMPLEMENTATION**

### **State Management:**
- **Overlay state:** Boolean flag for overlay visibility
- **Animation controllers:** Separate controllers for different animation phases
- **Gesture detection:** LongPressGestureRecognizer with precise timing
- **Memory cleanup:** Proper disposal of controllers and listeners

### **Integration Points:**
- **Navbar component:** Enhance existing Darvis button with long press detection
- **Overlay system:** Global overlay that works across all compatible screens
- **Navigation service:** Hook into existing navigation system for screen transitions

### **Platform Integration:**
- **iOS haptics:** Use UIImpactFeedbackGenerator for haptic feedback
- **Performance monitoring:** Ensure smooth animations on all device types
- **Testing support:** Enable testing of long press interactions

This creates a sophisticated quick-access system that enhances user productivity while maintaining Darvis's premium interaction design standards.