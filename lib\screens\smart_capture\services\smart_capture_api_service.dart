import 'dart:async';
import 'dart:io';
import '../../../services/api_service.dart';
import '../models/captured_content.dart';
import 'content_processing_service.dart';
import 'smart_capture_error_handler.dart';

/// Service for Smart Capture backend integration
class SmartCaptureApiService implements ContentProcessingService {
  final ApiService _apiService;
  
  // Cache for processing status polling
  final Map<String, Timer> _processingTimers = {};
  final Map<String, StreamController<CapturedContent>> _processingStreams = {};

  SmartCaptureApiService(this._apiService);

  @override
  Future<CapturedContent> processUrl(String url) async {
    // Capture content via API
    final response = await _apiService.captureContent(url: url);

    final content = CapturedContent.fromApi(response);

    // If processing is in progress, start polling for status
    if (content.status == ProcessingStatus.processing) {
      _startProcessingStatusPolling(content);
    }

    return content;
  }

  @override
  Future<CapturedContent> processImage(File image) async {
    try {
      // Upload image and process via API
      final response = await _apiService.uploadAndProcessImage(image);

      final content = CapturedContent.fromApi(response);

      // If processing is in progress, start polling for status
      if (content.status == ProcessingStatus.processing) {
        _startProcessingStatusPolling(content);
      }

      return content;
    } catch (e) {
      // Return error content on failure
      return CapturedContent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        url: image.path,
        title: 'Failed to process image',
        summary: 'Couldn\'t process this image: ${e.toString()}',
        tag: 'Error',
        type: ContentType.image,
        status: ProcessingStatus.error,
        timestamp: DateTime.now(),
        thumbnailUrl: image.path,
      );
    }
  }

  @override
  Future<List<CapturedContent>> loadExistingContent() async {
    try {
      final response = await _apiService.getCapturedContent();
      
      final items = response['items'] as List<dynamic>? ?? [];
      return items.map((json) => CapturedContent.fromApi(json)).toList();
    } catch (e) {
      // Return empty list on error, let UI handle error display
      print('Error loading captured content: $e');
      return [];
    }
  }

  /// Search captured content
  Future<List<CapturedContent>> searchContent({
    required String query,
    String? contentType,
    List<String>? tags,
    String? dateFrom,
    String? dateTo,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final response = await _apiService.searchCapturedContent(
        query: query,
        contentType: contentType,
        tags: tags,
        dateFrom: dateFrom,
        dateTo: dateTo,
        limit: limit,
        offset: offset,
      );
      
      final items = response['items'] as List<dynamic>? ?? [];
      return items.map((json) => CapturedContent.fromApi(json)).toList();
    } catch (e) {
      print('Error searching captured content: $e');
      return [];
    }
  }

  /// Update content (tags, title, etc.)
  Future<CapturedContent?> updateContent({
    required String id,
    String? title,
    String? summary,
    List<String>? tags,
    String? status,
  }) async {
    try {
      final response = await _apiService.updateCapturedContent(
        id: id,
        title: title,
        summary: summary,
        tags: tags,
        status: status,
      );
      
      return CapturedContent.fromApi(response);
    } catch (e) {
      print('Error updating captured content: $e');
      return null;
    }
  }

  /// Delete content
  Future<bool> deleteContent(String id) async {
    try {
      await _apiService.deleteCapturedContent(id);
      return true;
    } catch (e) {
      print('Error deleting captured content: $e');
      return false;
    }
  }

  /// Bulk delete content
  Future<bool> bulkDeleteContent(List<String> ids) async {
    try {
      await _apiService.bulkDeleteCapturedContent(ids);
      return true;
    } catch (e) {
      print('Error bulk deleting captured content: $e');
      return false;
    }
  }

  /// Retry failed processing
  Future<CapturedContent?> retryProcessing(String id) async {
    try {
      final response = await _apiService.retryContentProcessing(id);
      final content = CapturedContent.fromApi(response);
      
      // Start polling if processing
      if (content.status == ProcessingStatus.processing) {
        _startProcessingStatusPolling(content);
      }
      
      return content;
    } catch (e) {
      print('Error retrying content processing: $e');
      return null;
    }
  }

  /// Get available tags
  Future<List<String>> getAvailableTags() async {
    try {
      final response = await _apiService.getCapturedContentTags();
      final tags = response['tags'] as List<dynamic>? ?? [];
      return tags.map((tag) => tag.toString()).toList();
    } catch (e) {
      print('Error getting available tags: $e');
      return [];
    }
  }

  /// Import content to notes
  Future<bool> importToNotes({
    required String contentId,
    String? noteTitle,
    String? noteContent,
    List<String>? tags,
  }) async {
    try {
      await _apiService.importContentToNotes(
        contentId: contentId,
        noteTitle: noteTitle,
        noteContent: noteContent,
        tags: tags,
      );
      return true;
    } catch (e) {
      print('Error importing content to notes: $e');
      return false;
    }
  }

  /// Add content to chat context
  Future<bool> addToChatContext({
    required String contentId,
    String? conversationId,
    String? contextMessage,
  }) async {
    try {
      await _apiService.addContentToChatContext(
        contentId: contentId,
        conversationId: conversationId,
        contextMessage: contextMessage,
      );
      return true;
    } catch (e) {
      print('Error adding content to chat context: $e');
      return false;
    }
  }

  /// Start polling for processing status
  void _startProcessingStatusPolling(CapturedContent content) {
    // Cancel existing timer if any
    _processingTimers[content.id]?.cancel();
    
    // Create stream controller for this content
    final streamController = StreamController<CapturedContent>.broadcast();
    _processingStreams[content.id] = streamController;
    
    // Start polling
    _processingTimers[content.id] = Timer.periodic(
      const Duration(seconds: 3),
      (timer) async {
        try {
          final response = await _apiService.getProcessingStatus(content.id);
          final updatedContent = CapturedContent.fromApi(response);
          
          // Emit updated content
          if (!streamController.isClosed) {
            streamController.add(updatedContent);
          }
          
          // Stop polling if processing is complete
          if (updatedContent.status != ProcessingStatus.processing) {
            timer.cancel();
            _processingTimers.remove(content.id);
            streamController.close();
            _processingStreams.remove(content.id);
          }
        } catch (e) {
          print('Error polling processing status: $e');
          // Continue polling, might be temporary network issue
        }
      },
    );
  }

  /// Get processing status stream for a content item
  Stream<CapturedContent>? getProcessingStatusStream(String contentId) {
    return _processingStreams[contentId]?.stream;
  }

  /// Stop all processing timers
  void dispose() {
    for (final timer in _processingTimers.values) {
      timer.cancel();
    }
    _processingTimers.clear();
    
    for (final controller in _processingStreams.values) {
      controller.close();
    }
    _processingStreams.clear();
  }

  ContentType _detectContentType(String input) {
    if (input.toLowerCase().contains(RegExp(r'\.(jpg|jpeg|png|gif|webp|svg)$'))) {
      return ContentType.image;
    }
    return ContentType.link;
  }
}
