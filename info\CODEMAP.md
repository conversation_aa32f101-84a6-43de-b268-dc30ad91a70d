# Darvis App - Code Map

> **Auto-generated on:** 2025-08-12
> **Last updated:** Smart Capture, Profile Screen, Productivity Analytics Complete
> **Status:** Core Features Implemented - Full App Experience Ready

## 📁 Project Structure (Depth 3)

```
├── CODEMAP.md
├── PROGRESS_TRACKER.md
├── README.md
├── analysis_options.yaml
├── android/
│   ├── app/
│   │   ├── build.gradle.kts
│   │   └── src/
│   │       ├── debug/
│   │       ├── main/
│   │       └── profile/
│   ├── build.gradle.kts
│   ├── darvis_app_android.iml
│   ├── gradle/
│   │   └── wrapper/
│   │       ├── gradle-wrapper.jar
│   │       └── gradle-wrapper.properties
│   ├── gradle.properties
│   ├── gradlew
│   ├── gradlew.bat
│   ├── local.properties
│   └── settings.gradle.kts
├── assets/ # Static assets and resources
│   ├── animations/ # Lottie animations for therapy mode
│   │   └── therapy_mode.json # Blooming flower animation
│   ├── fonts/ # Custom fonts (Outfit, Poppins)
│   │   ├── Outfit-Black.ttf
│   │   ├── Outfit-Bold.ttf
│   │   ├── Outfit-ExtraBold.ttf
│   │   ├── Outfit-ExtraLight.ttf
│   │   ├── Outfit-Light.ttf
│   │   ├── Outfit-Medium.ttf
│   │   ├── Outfit-Regular.ttf
│   │   ├── Outfit-SemiBold.ttf
│   │   ├── Outfit-Thin.ttf
│   │   ├── Poppins-Black.ttf
│   │   ├── Poppins-BlackItalic.ttf
│   │   ├── Poppins-Bold.ttf
│   │   ├── Poppins-BoldItalic.ttf
│   │   ├── Poppins-ExtraBold.ttf
│   │   ├── Poppins-ExtraBoldItalic.ttf
│   │   ├── Poppins-ExtraLight.ttf
│   │   ├── Poppins-ExtraLightItalic.ttf
│   │   ├── Poppins-Italic.ttf
│   │   ├── Poppins-Light.ttf
│   │   ├── Poppins-LightItalic.ttf
│   │   ├── Poppins-Medium.ttf
│   │   ├── Poppins-MediumItalic.ttf
│   │   ├── Poppins-Regular.ttf
│   │   ├── Poppins-SemiBold.ttf
│   │   ├── Poppins-SemiBoldItalic.ttf
│   │   ├── Poppins-Thin.ttf
│   │   └── Poppins-ThinItalic.ttf
│   ├── icons/ # Comprehensive icon library (SVG)
│   │   ├── add_event.svg # Calendar event creation
│   │   ├── addimage.svg # Image upload functionality
│   │   ├── calendar_*.svg # Calendar navigation icons
│   │   ├── chat_*.svg # Chat interface icons
│   │   ├── contact_*.svg # Contact management icons
│   │   ├── copy_*.svg # Copy functionality states
│   │   ├── darvis_*.svg # App branding icons
│   │   ├── empty_contact.svg # Empty state placeholder
│   │   ├── expand_icon.svg # Expandable content indicator
│   │   ├── eye_*.svg # Visibility toggle icons
│   │   ├── google_logo.svg # OAuth integration
│   │   ├── home_*.svg # Navigation icons
│   │   ├── inbox_icon.svg # Message management
│   │   ├── mic_*.svg # Voice interaction states
│   │   ├── profile_icon.svg # User profile
│   │   ├── search_*.svg # Search functionality
│   │   ├── send_icon.svg # Message sending
│   │   └── voice_mode.svg # Voice interaction mode
│   └── images/ # Image assets (logos, illustrations)
│       ├── darvis_main.PNG # Main Darvis logo for profile
│       ├── darvisintelligence.png # Darvis diamond logo for processing
│       ├── darvis_list.png # List view illustration
│       ├── onboarding_brain.PNG
│       ├── onboarding_cube.PNG
│       └── onboarding_orb.PNG
├── darvis-app-build.md
├── darvis_app.iml
├── design.json
├── design_tokens.yaml
├── generate_tokens.bat
├── lib/ # Main Flutter application code
│   ├── blocs/ # State management (BLoC pattern)
│   │   ├── auth/ # Authentication state management
│   │   │   ├── auth_bloc.dart
│   │   │   ├── auth_event.dart
│   │   │   └── auth_state.dart
│   │   ├── notes/
│   │   └── tasks/
│   ├── main.dart # App entry point, Firebase init, BLoC providers
│   ├── models/ # Data models and entities
│   ├── screens/ # UI screens and pages
│   │   ├── auth/ # Authentication flow
│   │   │   ├── login_screen.dart # Email/password login with validation
│   │   │   ├── signup_screen.dart # User registration with form validation
│   │   │   └── welcome_screen.dart # Initial app welcome experience
│   │   ├── calendar/ # Event and contact management
│   │   │   ├── add_contact_screen.dart # Contact creation with camera integration
│   │   │   ├── add_event_screen.dart # Event scheduling with date/time pickers
│   │   │   ├── calendar_screen.dart # Calendar view and navigation
│   │   │   └── contact_list_screen.dart # Contact management with search/sort
│   │   ├── chat/ # Conversational AI interface
│   │   │   └── chat_screen.dart # Real-time chat with AI assistant
│   │   ├── home/ # Main dashboard
│   │   │   └── home_screen.dart # Central navigation hub
│   │   ├── onboarding/ # First-time user experience
│   │   │   └── onboarding_screen.dart # App introduction and setup
│   │   ├── productivity/ # Task and note management
│   │   │   ├── components/ # Reusable productivity components
│   │   │   │   ├── add_task_modal.dart # Task creation with progressive disclosure
│   │   │   │   ├── categories_sidebar.dart # Category management sidebar
│   │   │   │   └── category_creation_flow.dart # Custom category creation
│   │   │   ├── models/ # Productivity data models
│   │   │   │   ├── category.dart # Task categorization system
│   │   │   │   ├── note.dart # Note data structure
│   │   │   │   └── task.dart # Task management models
│   │   │   ├── notes_screen.dart # Note-taking with Google Keep-style UI
│   │   │   ├── productivity_analytics_screen.dart # Data visualization dashboard
│   │   │   └── task_management_screen.dart # Comprehensive task management
│   │   ├── profile/ # User profile and settings system
│   │   │   ├── about_darvis_screen.dart # App information and version details
│   │   │   ├── account_settings_screen.dart # Account management and subscription
│   │   │   ├── contact_us_screen.dart # Contact form and support options
│   │   │   ├── data_storage_screen.dart # Storage management and sync settings
│   │   │   ├── notifications_screen.dart # Notification preferences and quiet hours
│   │   │   ├── privacy_security_screen.dart # Privacy controls and security settings
│   │   │   └── profile_screen.dart # Main profile with stats and navigation
│   │   ├── smart_capture/ # Intelligent content processing
│   │   │   ├── models/ # Smart capture data models
│   │   │   │   └── captured_content.dart # Content processing models
│   │   │   ├── services/ # Content processing services
│   │   │   │   └── content_processing_service.dart # AI content analysis
│   │   │   ├── widgets/ # Smart capture UI components
│   │   │   │   ├── content_card.dart # Content display cards
│   │   │   │   ├── content_detail_modal.dart # Detailed content view
│   │   │   │   └── glassmorphic_input_field.dart # Modern input styling
│   │   │   └── smart_capture_screen.dart # Intelligent content inbox
│   │   ├── therapy/ # Mental wellness features
│   │   │   ├── mind_garden_screen.dart # Meditation and mindfulness hub
│   │   │   └── therapy_progress_screen.dart # Progress tracking and analytics
│   │   └── voice/ # Voice interaction features
│   │       └── voice_screen.dart # Voice-to-text and audio processing
│   ├── services/ # Business logic and external integrations
│   │   ├── api_service.dart
│   │   ├── auth_service.dart
│   │   ├── livekit_service.dart
│   │   ├── service_locator.dart
│   │   └── sync_engine.dart
│   ├── utils/ # Helper functions and utilities
│   │   └── design_tokens.dart
│   └── widgets/ # Reusable UI components
│       ├── common/ # Shared widgets (buttons, cards, inputs)
│       ├── enhanced_bottom_nav_bar.dart # Navigation bar with long press quick actions
│       └── quick_action_overlay.dart # Floating action buttons overlay system
├── pubspec.lock
├── pubspec.yaml
├── test/ # Testing infrastructure
│   ├── factories/ # Test data generation
│   │   └── mock_data_factory.dart
│   └── mock_impl/ # Mock service implementations for testing
│       ├── mock_api_service.dart
│       └── mock_auth_service.dart
├── tool/ # Development tools and scripts
│   ├── gen_codemap.dart
│   └── gen_design_tokens.dart
├── web/
│   ├── favicon.png
│   ├── icons/ # Icon assets (custom icons)
│   │   ├── Icon-192.png
│   │   ├── Icon-512.png
│   │   ├── Icon-maskable-192.png
│   │   └── Icon-maskable-512.png
│   ├── index.html
│   └── manifest.json
└── windows/
    ├── CMakeLists.txt
    ├── flutter/
    │   ├── CMakeLists.txt
    │   ├── ephemeral/
    │   ├── generated_plugin_registrant.cc
    │   ├── generated_plugin_registrant.h
    │   └── generated_plugins.cmake
    └── runner/
        ├── CMakeLists.txt
        ├── Runner.rc
        ├── flutter_window.cpp
        ├── flutter_window.h
        ├── main.cpp
        ├── resource.h
        ├── resources/
        │   └── app_icon.ico
        ├── runner.exe.manifest
        ├── utils.cpp
        ├── utils.h
        ├── win32_window.cpp
        └── win32_window.h
```

## 📦 Dependencies Overview

| Package | Purpose | Key APIs Used |
|---------|---------|---------------|
| `flutter_bloc` | State management | `BlocProvider`, `BlocBuilder`, `BlocConsumer` |
| `equatable` | Value equality | `Equatable` mixin for BLoC states/events |
| `dio` | HTTP client | `get`, `post`, `interceptors` |
| `dio_smart_retry` | API retry logic | `RetryInterceptor`, exponential backoff |
| `livekit_client` | Real-time voice | `Room`, `LocalAudioTrack`, WebRTC |
| `firebase_core` | Firebase initialization | `Firebase.initializeApp()` |
| `firebase_auth` | Authentication | `signInWithEmailAndPassword`, `authStateChanges` |
| `firebase_messaging` | Push notifications | `onMessage`, `onBackgroundMessage` |
| `firebase_crashlytics` | Crash reporting | `recordError`, `log` |
| `flutter_secure_storage` | Secure token storage | `write`, `read`, `delete` |
| `encrypt` | Data encryption | `AES`, `Key.fromSecureRandom` |
| `get_it` | Dependency injection | `registerLazySingleton`, `registerFactory` |
| `cached_network_image` | Image caching | `CachedNetworkImage` widget |
| `flutter_svg` | SVG icon rendering | `SvgPicture.asset`, vector graphics |
| `image_picker` | Camera/gallery access | `pickImage`, `ImageSource.camera` |
| `url_launcher` | External app integration | `launchUrl`, social media links |
| `lottie` | Animation playback | `Lottie.asset`, interactive animations |


## 🔄 How the Pieces Fit

**Data Flow Architecture:**
1. **UI Layer** (`screens/`) triggers events → **BLoC Layer** (`blocs/`)
2. **BLoC Layer** calls → **Service Layer** (`services/`) for business logic
3. **Service Layer** communicates with → **External APIs** and **Local Storage**
4. **Design System** (`utils/design_tokens.dart`) provides → **Consistent UI styling**

**Dependency Flow:**
- `main.dart` → initializes `service_locator.dart` → registers all services
- `screens/` → consume BLoCs via `BlocProvider` → trigger state changes
- `blocs/` → depend on `services/` → never call external APIs directly
- `services/` → handle all external communication → Firebase, HTTP, WebRTC
- `utils/` → provide shared utilities → consumed by all layers

**Current State:** Complete app experience implemented including:
- **Productivity Suite**: Task management, note-taking, analytics dashboard
- **Smart Capture**: AI-powered content processing and organization
- **Profile System**: Comprehensive settings with account management, privacy controls, notifications, data sync
- **Therapy Mode**: Mental wellness features with progress tracking
- **Calendar & Contacts**: Event and contact management
- **Modern UI/UX**: Glassmorphic design, smooth animations, responsive layouts
- **Navigation System**: Enhanced navbar with quick actions and seamless transitions

**Recent Updates (2025-08-12):**
- **Profile Settings Overhaul**: Complete settings system with 7 dedicated screens
- **Enhanced Navigation System**: Long press Darvis button for quick actions (Add Note/Task)
- **Navigation Performance**: Optimized page transitions and animation timing
- **Visual Fixes**: Resolved border obstruction and container flashing issues
- **Functional Improvements**: Profile image picker, subscription sliding, timezone management
- **Design Consistency**: Updated dropdowns and form elements with proper design tokens
- **Quick Action Overlay**: Floating action buttons with elastic animations and haptic feedback

Ready for backend integration and production deployment.
