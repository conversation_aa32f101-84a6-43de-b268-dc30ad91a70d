import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import '../../utils/design_tokens.dart';

class PrivacySecurityScreen extends StatefulWidget {
  const PrivacySecurityScreen({super.key});

  @override
  State<PrivacySecurityScreen> createState() => _PrivacySecurityScreenState();
}

class _PrivacySecurityScreenState extends State<PrivacySecurityScreen>
    with TickerProviderStateMixin {
  
  late AnimationController _pageAnimationController;
  late Animation<Offset> _pageSlideAnimation;
  
  // State variables
  bool _therapyEncryption = true;
  bool _anonymousAnalytics = false;
  bool _autoDeleteConversations = true;
  bool _appLockRequired = false;
  String _sessionTimeout = '30 minutes';

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 350),
      vsync: this,
    );

    _pageSlideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _pageAnimationController,
      curve: Curves.easeOut,
    ));

    // Delay animation start until after page route transition completes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _pageAnimationController.forward();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: SafeArea(
        child: SlideTransition(
          position: _pageSlideAnimation,
          child: CustomScrollView(
            physics: const ClampingScrollPhysics(),
            slivers: [
              _buildHeader(),
              _buildDataPrivacySection(),
              _buildContentControlSection(),
              _buildSecurityFeaturesSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return SliverToBoxAdapter(
      child: Container(
        height: 80,
        padding: const EdgeInsets.fromLTRB(
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingSm,
        ),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                Icons.arrow_back,
                color: DesignTokens.iconPrimary,
              ),
            ),
            Expanded(
              child: Center(
                child: ShaderMask(
                  shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
                  child: Text(
                    'Privacy & Security',
                    style: DesignTokens.therapySessionTitleStyle.copyWith(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 48), // Balance the back button
          ],
        ),
      ),
    );
  }

  Widget _buildDataPrivacySection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Data Privacy',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Therapy session encryption toggle
            _buildToggleItem(
              'Therapy Session Encryption',
              'End-to-end encryption for all therapy conversations',
              _therapyEncryption,
              (value) {
                setState(() {
                  _therapyEncryption = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
            
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Anonymous usage analytics toggle
            _buildToggleItem(
              'Anonymous Usage Analytics',
              'Help improve Drix by sharing anonymous usage data',
              _anonymousAnalytics,
              (value) {
                setState(() {
                  _anonymousAnalytics = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentControlSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Content Control',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Auto-delete old conversations toggle
            _buildToggleItem(
              'Auto-Delete Old Conversations',
              'Automatically delete conversations older than 30 days',
              _autoDeleteConversations,
              (value) {
                setState(() {
                  _autoDeleteConversations = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityFeaturesSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Security Features',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Session timeout dropdown
            _buildDropdownField(
              'Session Timeout',
              'Automatically sign out after inactivity',
              _sessionTimeout,
              ['15 minutes', '30 minutes', '1 hour', '2 hours', 'Never'],
              (value) {
                setState(() {
                  _sessionTimeout = value!;
                });
              },
            ),
            
            const SizedBox(height: DesignTokens.spacingMd),
            
            // App lock requirements toggle
            _buildToggleItem(
              'App Lock Requirements',
              'Require authentication to open the app',
              _appLockRequired,
              (value) {
                setState(() {
                  _appLockRequired = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
            
            const SizedBox(height: DesignTokens.spacingXl),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleItem(String title, String subtitle, bool value, Function(bool) onChanged) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: const EdgeInsets.all(DesignTokens.spacingSm),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: DesignTokens.cardTitleStyle.copyWith(fontSize: 16),
                      ),
                      const SizedBox(height: DesignTokens.spacingXs),
                      Text(
                        subtitle,
                        style: DesignTokens.bodyStyle.copyWith(
                          color: DesignTokens.textSecondary,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: value,
                  onChanged: onChanged,
                  activeColor: DesignTokens.primaryInteractiveBlue,
                  inactiveTrackColor: DesignTokens.navigationInactive,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownField(String title, String subtitle, String value, List<String> options, Function(String?) onChanged) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: DesignTokens.cardTitleStyle.copyWith(fontSize: 16),
              ),
              const SizedBox(height: DesignTokens.spacingXs),
              Text(
                subtitle,
                style: DesignTokens.bodyStyle.copyWith(
                  color: DesignTokens.textSecondary,
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: DesignTokens.spacingMd),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
                decoration: BoxDecoration(
                  color: DesignTokens.backgroundCard,
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                  border: Border.all(
                    color: DesignTokens.borderInteractiveElement,
                    width: 1,
                  ),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: value,
                    isExpanded: true,
                    icon: const Icon(Icons.keyboard_arrow_down, color: DesignTokens.iconPrimary),
                    style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
                    dropdownColor: DesignTokens.backgroundCard,
                    onChanged: onChanged,
                    selectedItemBuilder: (context) {
                      return options.map((String option) {
                        return Container(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            option,
                            style: DesignTokens.bodyStyle.copyWith(
                              color: DesignTokens.primaryInteractiveBlue,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        );
                      }).toList();
                    },
                    items: options.map((String option) {
                      return DropdownMenuItem<String>(
                        value: option,
                        child: Text(option),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _pageAnimationController.dispose();
    super.dispose();
  }
}
