import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:darvis_app/blocs/tasks/tasks_bloc.dart';
import 'package:darvis_app/blocs/tasks/tasks_event.dart';
import 'package:darvis_app/blocs/tasks/tasks_state.dart';
import 'package:darvis_app/screens/productivity/task_management_screen.dart';
import 'package:darvis_app/services/navigation_service.dart';
import 'package:darvis_app/models/isar_models.dart';

import 'tasks_integration_test.mocks.dart';

@GenerateMocks([TasksBloc, NavigationService])
void main() {
  group('Tasks Screen Integration Tests', () {
    late MockTasksBloc mockTasksBloc;
    late MockNavigationService mockNavigationService;

    setUp(() {
      mockTasksBloc = MockTasksBloc();
      mockNavigationService = MockNavigationService();
      
      // Register mocks in GetIt
      GetIt.instance.reset();
      GetIt.instance.registerSingleton<TasksBloc>(mockTasksBloc);
      GetIt.instance.registerSingleton<NavigationService>(mockNavigationService);
    });

    tearDown(() {
      GetIt.instance.reset();
    });

    testWidgets('should display loading state initially', (WidgetTester tester) async {
      // Arrange
      when(mockTasksBloc.state).thenReturn(const TasksLoading());
      when(mockTasksBloc.stream).thenAnswer((_) => Stream.value(const TasksLoading()));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TasksBloc>.value(
            value: mockTasksBloc,
            child: const TaskManagementScreen(),
          ),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display tasks when loaded', (WidgetTester tester) async {
      // Arrange
      final testTasks = [
        LocalTask()
          ..id = 1
          ..title = 'Complete project'
          ..description = 'Finish the mobile app project'
          ..dueDate = DateTime.now().add(const Duration(days: 1))
          ..isCompleted = false
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now(),
        LocalTask()
          ..id = 2
          ..title = 'Review code'
          ..description = 'Review pull requests'
          ..dueDate = DateTime.now().add(const Duration(hours: 2))
          ..isCompleted = true
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now(),
      ];

      final loadedState = TasksLoaded(
        tasks: testTasks,
        filteredTasks: testTasks,
      );

      when(mockTasksBloc.state).thenReturn(loadedState);
      when(mockTasksBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TasksBloc>.value(
            value: mockTasksBloc,
            child: const TaskManagementScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Complete project'), findsOneWidget);
      expect(find.text('Review code'), findsOneWidget);
    });

    testWidgets('should trigger LoadTasks event on initialization', (WidgetTester tester) async {
      // Arrange
      when(mockTasksBloc.state).thenReturn(const TasksLoading());
      when(mockTasksBloc.stream).thenAnswer((_) => Stream.value(const TasksLoading()));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TasksBloc>.value(
            value: mockTasksBloc,
            child: const TaskManagementScreen(),
          ),
        ),
      );

      // Assert
      verify(mockTasksBloc.add(const LoadTasks())).called(1);
    });

    testWidgets('should handle task completion toggle', (WidgetTester tester) async {
      // Arrange
      final testTasks = [
        LocalTask()
          ..id = 1
          ..title = 'Test Task'
          ..description = 'Test description'
          ..dueDate = DateTime.now().add(const Duration(days: 1))
          ..isCompleted = false
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now(),
      ];

      final loadedState = TasksLoaded(
        tasks: testTasks,
        filteredTasks: testTasks,
      );

      when(mockTasksBloc.state).thenReturn(loadedState);
      when(mockTasksBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TasksBloc>.value(
            value: mockTasksBloc,
            child: const TaskManagementScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Find and tap the checkbox to toggle completion
      final checkbox = find.byType(Checkbox);
      if (checkbox.evaluate().isNotEmpty) {
        await tester.tap(checkbox.first);
        await tester.pumpAndSettle();

        // Assert - should trigger toggle completion event
        verify(mockTasksBloc.add(any)).called(greaterThan(1));
      }
    });

    testWidgets('should handle search functionality', (WidgetTester tester) async {
      // Arrange
      final testTasks = [
        LocalTask()
          ..id = 1
          ..title = 'Meeting preparation'
          ..description = 'Prepare for client meeting'
          ..dueDate = DateTime.now().add(const Duration(days: 1))
          ..isCompleted = false
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now(),
        LocalTask()
          ..id = 2
          ..title = 'Code review'
          ..description = 'Review team code'
          ..dueDate = DateTime.now().add(const Duration(hours: 2))
          ..isCompleted = false
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now(),
      ];

      final loadedState = TasksLoaded(
        tasks: testTasks,
        filteredTasks: testTasks,
      );

      when(mockTasksBloc.state).thenReturn(loadedState);
      when(mockTasksBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TasksBloc>.value(
            value: mockTasksBloc,
            child: const TaskManagementScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Find and use search field
      final searchField = find.byType(TextField);
      if (searchField.evaluate().isNotEmpty) {
        await tester.tap(searchField.first);
        await tester.pumpAndSettle();
        
        await tester.enterText(searchField.first, 'meeting');
        await tester.pumpAndSettle();

        // Assert - search should filter tasks locally
        expect(find.text('Meeting preparation'), findsOneWidget);
      }
    });

    testWidgets('should handle filter tabs', (WidgetTester tester) async {
      // Arrange
      final testTasks = [
        LocalTask()
          ..id = 1
          ..title = 'Today Task'
          ..description = 'Task due today'
          ..dueDate = DateTime.now()
          ..isCompleted = false
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now(),
        LocalTask()
          ..id = 2
          ..title = 'Future Task'
          ..description = 'Task due tomorrow'
          ..dueDate = DateTime.now().add(const Duration(days: 1))
          ..isCompleted = false
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now(),
      ];

      final loadedState = TasksLoaded(
        tasks: testTasks,
        filteredTasks: testTasks,
      );

      when(mockTasksBloc.state).thenReturn(loadedState);
      when(mockTasksBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TasksBloc>.value(
            value: mockTasksBloc,
            child: const TaskManagementScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Find filter tabs
      final todayTab = find.text('Today');
      final upcomingTab = find.text('Upcoming');

      expect(todayTab, findsOneWidget);
      expect(upcomingTab, findsOneWidget);

      // Test tab switching
      await tester.tap(upcomingTab);
      await tester.pumpAndSettle();

      // Both tasks should still be visible as the filtering is done locally
      expect(find.text('Today Task'), findsOneWidget);
      expect(find.text('Future Task'), findsOneWidget);
    });

    testWidgets('should display error state correctly', (WidgetTester tester) async {
      // Arrange
      const errorState = TasksOperationError(
        error: 'Failed to load tasks',
        operation: 'load',
      );
      when(mockTasksBloc.state).thenReturn(errorState);
      when(mockTasksBloc.stream).thenAnswer((_) => Stream.value(errorState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TasksBloc>.value(
            value: mockTasksBloc,
            child: const TaskManagementScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Error: Failed to load tasks'), findsOneWidget);
    });

    testWidgets('should handle add task functionality', (WidgetTester tester) async {
      // Arrange
      final loadedState = TasksLoaded(
        tasks: const [],
        filteredTasks: const [],
      );

      when(mockTasksBloc.state).thenReturn(loadedState);
      when(mockTasksBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TasksBloc>.value(
            value: mockTasksBloc,
            child: const TaskManagementScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Find and tap the FAB to add a task
      final fab = find.byType(FloatingActionButton);
      if (fab.evaluate().isNotEmpty) {
        await tester.tap(fab);
        await tester.pumpAndSettle();

        // The add task modal should appear
        // This tests that the UI responds to the FAB tap
        expect(find.byType(BottomSheet), findsOneWidget);
      }
    });

    testWidgets('should handle empty state', (WidgetTester tester) async {
      // Arrange
      final loadedState = TasksLoaded(
        tasks: const [],
        filteredTasks: const [],
      );

      when(mockTasksBloc.state).thenReturn(loadedState);
      when(mockTasksBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TasksBloc>.value(
            value: mockTasksBloc,
            child: const TaskManagementScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert - should show empty state
      expect(find.text('No tasks for today'), findsOneWidget);
      expect(find.text('Add Task'), findsOneWidget);
    });
  });
}
