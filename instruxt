## Contextual Definition for AI Coding Agent

### **Screen: Profile Settings System**

**Design System:** Dark theme using `DesignTokens`, card-based layout with background glow effects, comprehensive micro-animations

---

## **OVERALL VISUAL STRUCTURE**

### **Main Profile Screen Layout:**
- **Background:** `backgroundApp` (black) with `backgroundGlowBlueGradient` radial effect
- **Glow effect:** Static radial gradient (no breathing animation), centered on screen
- **Logo placement:** Darvis diamond logo at top center, positioned with `spacingLg` below header
- **Logo styling:** Medium scale, decorative only (no tap interaction)
- **Content layout:** Vertical scrollable list of setting sections

### **Header Section:**
- **Navigation bar:** Standard bottom navbar matching 
- **Safe area:** Proper top safe area handling for status bar
- **Title:** "Profile" using `cardTitleStyle` if needed in header area

### **Section Card Structure:**
- **Container:** `backgroundCard` (#111D2F) with `borderRadiusLg` (20px)
- **Spacing:** `spacingMd` (16px) between section cards
- **Internal padding:** `spacingMd` on all sides
- **Divider lines:** 1px `borderInteractiveElement` (#262626) between sections

---

## **MAIN PROFILE SCREEN SECTIONS**

### **Section Card Layout:**
```
┌─ [Icon 24px] Section Title          > ─┐
│               Descriptive subtitle      │
└─────────────────────────────────────────┘
```

### **Section Definitions:**
1. **Account & Profile** - "Manage your personal information"
2. **AI Companion** - "Customize Darvis behavior and memory"
3. **Privacy & Security** - "Control your data and security settings"
4. **Modes & Features** - "Configure therapy and productivity tools"
5. **Notifications & Wellness** - "Manage alerts and healthy usage"
6. **Data & Sync** - "Backup and synchronization options"
7. **About & Help** - "App information and support"

### **Section Interaction:**
- **Touch feedback:** Brief scale down to 0.98x on press with light haptic
- **Navigation:** Tap to slide to individual setting screen
- **Visual indicator:** Chevron (>) on right side using `iconSecondary`

---

## **INDIVIDUAL SETTING SCREENS**

### **Screen Transition Animations:**
- **Enter:** Screen slides in from right (350ms ease-out)
- **Exit:** Screen slides out to right (350ms ease-out)  
- **Content stagger:** Setting items appear with 100ms delays between each
- **Subtle spring:** Slight overshoot on setting card appearances

### **Header Layout:**
- **Back button:** Top-left using `iconPrimary`
- **Screen title:** Section name using `cardTitleStyle`
- **Background:** Continues main screen glow effect

### **Setting Item Structure:**
- **Container:** `backgroundCard` with `borderRadiusLg`
- **Layout:** Setting name + description + control element
- **Spacing:** `spacingMd` between setting groups
- **Internal padding:** `spacingMd`

---

## **1. ACCOUNT & PROFILE SCREEN**

### **Personal Information Section:**
- **Name field:** Text input with `backgroundTextInput`, `borderRadiusXl`
- **"What should Darvis call you?" field:** Same styling as name field
- **Profile photo:** Circular image with tap to change functionality
- **Time zone:** Dropdown with current zone displayed

### **Authentication & Security Section:**
- **Change password:** Button leading to password change flow
- **Two-factor authentication:** Toggle (round, `primaryInteractiveBlue` when on)
- **Biometric login:** Toggle for Face ID/Touch ID

### **Subscription & Usage Section:**
- **Current plan card:** 
  - Free tier: `backgroundCard` styling
  - Premium tier: `primaryAccentBlue` background
- **Usage statistics:** Display sessions, notes, tasks in clean metrics
- **Plan comparison cards:**
  ```
  ┌─ DARVIS ESSENTIAL ─────────────┐
  │ Free • Current Plan             │
  │ ✓ 50 conversations/month        │
  │ ✓ Basic therapy sessions        │
  │ ✓ Simple task management        │
  └─────────────────────────────────┘
  
  ┌─ DARVIS UNLIMITED ─────────────┐
  │ $9.99/month                     │
  │ ✓ Unlimited conversations       │
  │ ✓ Advanced therapy insights     │
  │ ✓ Priority AI processing        │
  │ [     UPGRADE NOW     ]         │
  └─────────────────────────────────┘
  ```

### **Account Management Section:**
- **Sign out:** Standard button
- **Delete account:** Destructive action with confirmation

---

## **2. AI COMPANION SCREEN**

### **Personality & Communication Section:**
- **Communication style:** Radio button grid layout
  ```
  ○ Professional    ○ Casual
  ○ Friendly        ● Empathetic
  ```
  - Selected: `primaryInteractiveBlue` background
  - Unselected: `backgroundCard` with `borderInteractiveElement` border

- **Response length:** Similar radio button layout with options: Concise, Detailed, Adaptive

### **Memory & Learning Section:**
- **Conversational memory:** Toggle with clear on/off states
- **Cross-mode learning:** Toggle with descriptive subtitle
  - Subtitle: "Let therapy insights inform productivity suggestions and vice versa"
- **Memory retention period:** Dropdown with options:
  - 1 week, 1 month, 3 months, 6 months, 1 year
  - Container: `backgroundAccentCard` showing current selection

---

## **3. PRIVACY & SECURITY SCREEN**

### **Data Privacy Section:**
- **Therapy session encryption:** Toggle
- **Anonymous usage analytics:** Toggle

### **Content Control Section:**
- **Auto-delete old conversations:** Toggle

### **Security Features Section:**
- **Session timeout settings:** Dropdown with time options
- **App lock requirements:** Toggle

---

## **4. MODES & FEATURES SCREEN**

### **Therapy Mode Section:**
- **Session length preferences:** Dropdown (15min, 30min, 45min, 60min)
- **Session reminders and scheduling:** Toggle

### **Productivity Features Section:**
- **Sync calendar:** Toggle

---

## **5. NOTIFICATIONS & WELLNESS SCREEN**

### **Notification Preferences Section:**
- **Push notification types:** Multiple toggles for different notification categories
- **Quiet hours and do not disturb:** Time picker interface
- **Notification frequency limits:** Dropdown with frequency options

---

## **6. DATA & SYNC SCREEN**

### **Cloud Sync Section:**
- **Cross-device synchronization:** Master toggle
- **Sync frequency settings:** Dropdown (Real-time, Hourly, Daily)
- **Selective sync:** Multiple toggles for different data types

### **Backup & Export Section:**
- **Manual backup creation:** Button to trigger backup process

---

## **7. ABOUT & HELP SCREEN**

### **App Information Section:**
- **Version information:** Display current version number

### **Support & Feedback Section:**
- **Contact support:** Button leading to support interface
- **Feature requests:** Button leading to feedback form

---

## **CONTROL ELEMENT SPECIFICATIONS**

### **Toggle Design:**
- **Dimensions:** Standard iOS toggle size
- **Off state:** `navigationInactive` (#676D75) track, white circle
- **On state:** `primaryInteractiveBlue` (#004EEB) track, white circle
- **Animation:** 300ms smooth transition with medium haptic feedback
- **Touch target:** Minimum 44px for accessibility

### **Dropdown/Selection Design:**
- **Container:** `backgroundAccentCard` (#183C8E) for selected state
- **Text:** `textPrimary` for current selection
- **Arrow:** `iconPrimary` chevron down
- **Expanded state:** `backgroundCard` container with `borderRadiusLg`
- **Options:** Each option in separate row with tap feedback

### **Input Field Design:**
- **Background:** `backgroundTextInput` (#DCDFEA7)
- **Text color:** `textOnLightBackground` (#222425)
- **Border radius:** `borderRadiusXl` (24px)
- **Focus state:** Add `primaryInteractiveBlue` border (2px)
- **Placeholder:** Use appropriate placeholder text in muted color

### **Button Design:**
- **Primary actions:** `primaryInteractiveBlue` background, white text
- **Secondary actions:** `backgroundCard` with `borderInteractiveElement` border
- **Destructive actions:** Red background for critical actions like delete

---

## **MICRO-ANIMATION SPECIFICATIONS**

### **Screen Transitions:**
- **Navigation in:** 350ms ease-out slide from right
- **Navigation out:** 350ms ease-out slide to right
- **Content reveal:** Settings stagger in with 100ms delays
- **Background persistence:** Glow effect continues across navigation

### **Control Interactions:**
- **Toggle animation:** 300ms smooth state change with haptic
- **Dropdown expansion:** 250ms ease-out with staggered option appearance (50ms delays)
- **Button press:** Brief scale down (0.95x) with light haptic
- **Input focus:** Border color transition (200ms) with content shift for keyboard

### **Loading States:**
- **Save operations:** Brief spinner overlay on affected controls
- **Photo upload:** Progress indicator with smooth completion animation
- **Network operations:** Loading state on buttons during processing

---

## **TYPOGRAPHY & COLOR USAGE**

### **Text Hierarchy:**
- **Section titles:** `cardTitleStyle` in `textPrimary`
- **Setting names:** `bodyStyle` in `textPrimary`
- **Descriptions/subtitles:** `bodyStyle` in `textSecondary`
- **Current values:** `textPrimary` with emphasis
- **Helper text:** Smaller size in `textMuted`

### **Color Application:**
- **Interactive elements:** `primaryInteractiveBlue` for active states
- **Secondary elements:** `primaryAccentBlue` for containers
- **Inactive elements:** `navigationInactive` for disabled states
- **Backgrounds:** `backgroundCard` for containers, `backgroundApp` for screens

---

## **RESPONSIVE & ACCESSIBILITY**

### **Touch Targets:**
- **Minimum 44px** for all interactive elements
- **Toggle switches:** Proper touch area around visual element
- **Setting rows:** Full-width touch target for entire row

### **Content Adaptation:**
- **Text scaling:** Respect system font size settings
- **Safe areas:** Proper margin handling for notched devices
- **Keyboard handling:** Smooth content adjustment when keyboard appears

### **Performance Requirements:**
- **60fps animations:** All transitions maintain smooth frame rate
- **Memory management:** Efficient handling of setting screens
- **State persistence:** Remember setting changes across app launches

---

## **TECHNICAL IMPLEMENTATION**

### **Navigation Flow:**
- **State management:** Preserve scroll position and setting states
- **Deep linking:** Support direct navigation to specific setting screens
- **Back navigation:** Consistent back button behavior across all screens

### **Data Handling:**
- **Local storage:** Immediate saving of setting changes
- **Validation:** Input validation with appropriate error messages
- **Sync integration:** Changes propagate to cloud when available

### **Platform Integration:**
- **iOS permissions:** Proper handling of system permission requests
- **Biometric integration:** Native Face ID/Touch ID implementation
- **Photo picker:** System photo picker for profile image changes

