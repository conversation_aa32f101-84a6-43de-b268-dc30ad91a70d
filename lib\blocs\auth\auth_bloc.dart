import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../services/auth_service_interface.dart';
import '../../models/user_model.dart';
import '../../utils/error_handler.dart';
import 'auth_event.dart';
import 'auth_state.dart';

/// BLoC for managing authentication state
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthServiceInterface _authService;
  late StreamSubscription<dynamic> _authStateSubscription;

  AuthBloc({required AuthServiceInterface authService})
      : _authService = authService,
        super(AuthInitial()) {
    
    // Listen to Firebase auth state changes
    _authStateSubscription = _authService.authStateChanges.listen((user) {
      add(AuthStateChanged(isAuthenticated: user != null));
    });

    // Register event handlers
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthSignInRequested>(_onAuthSignInRequested);
    on<AuthSignUpRequested>(_onAuthSignUpRequested);
    on<AuthSignInWithGoogleRequested>(_onAuthSignInWithGoogleRequested);
    on<AuthSignUpWithGoogleRequested>(_onAuthSignUpWithGoogleRequested);
    on<AuthPasswordResetRequested>(_onAuthPasswordResetRequested);
    on<AuthSignOutRequested>(_onAuthSignOutRequested);
    on<AuthStateChanged>(_onAuthStateChanged);
  }

  /// Convert auth service user to UserModel with robust error handling
  UserModel _convertToUserModel(dynamic user) {
    if (user == null) {
      throw Exception('User is null');
    }

    try {
      // Check if it's a MockUser or Firebase User and convert accordingly
      final userType = user.runtimeType.toString();

      if (userType.contains('MockUser')) {
        return UserModel.fromMockUser(user);
      } else {
        // Handle Firebase User with safe property access to avoid pigeon errors
        return _safeFirebaseUserConversion(user);
      }
    } catch (e) {
      // If conversion fails, create a basic UserModel with available data
      print('⚠️ User conversion failed, creating basic UserModel: $e');
      return _createFallbackUserModel(user, e);
    }
  }

  /// Safely convert Firebase User to UserModel to avoid pigeon type casting errors
  UserModel _safeFirebaseUserConversion(dynamic firebaseUser) {
    try {
      // Use safe property access with null checks
      String uid;
      String email;
      String? displayName;
      String? photoURL;
      bool emailVerified;
      DateTime? createdAt;
      DateTime? lastSignInAt;

      // Safely extract properties with try-catch for each property
      try {
        uid = firebaseUser.uid?.toString() ?? 'unknown_uid_${DateTime.now().millisecondsSinceEpoch}';
      } catch (e) {
        uid = 'safe_uid_${DateTime.now().millisecondsSinceEpoch}';
      }

      try {
        email = firebaseUser.email?.toString() ?? '<EMAIL>';
      } catch (e) {
        email = 'safe_email_${DateTime.now().millisecondsSinceEpoch}@example.com';
      }

      try {
        displayName = firebaseUser.displayName?.toString();
      } catch (e) {
        displayName = null;
      }

      try {
        photoURL = firebaseUser.photoURL?.toString();
      } catch (e) {
        photoURL = null;
      }

      try {
        emailVerified = firebaseUser.emailVerified == true;
      } catch (e) {
        emailVerified = false;
      }

      try {
        createdAt = firebaseUser.metadata?.creationTime;
      } catch (e) {
        createdAt = null;
      }

      try {
        lastSignInAt = firebaseUser.metadata?.lastSignInTime;
      } catch (e) {
        lastSignInAt = null;
      }

      return UserModel(
        uid: uid,
        email: email,
        displayName: displayName,
        photoURL: photoURL,
        emailVerified: emailVerified,
        createdAt: createdAt,
        lastSignInAt: lastSignInAt,
      );
    } catch (e) {
      print('❌ Safe Firebase user conversion failed: $e');
      return _createFallbackUserModel(firebaseUser, e);
    }
  }

  /// Create a fallback UserModel when all else fails
  UserModel _createFallbackUserModel(dynamic user, dynamic error) {
    print('🚨 Creating fallback user model due to error: $error');

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return UserModel(
      uid: 'fallback_uid_$timestamp',
      email: 'fallback_$<EMAIL>',
      displayName: 'User',
      emailVerified: false,
      createdAt: DateTime.now(),
      lastSignInAt: DateTime.now(),
    );
  }

  /// Handle authentication check request
  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final user = _authService.currentUser;
      if (user != null) {
        // Verify backend tokens are still valid
        final accessToken = await _authService.getAccessToken();
        if (accessToken != null) {
          emit(AuthAuthenticated(user: _convertToUserModel(user)));
        } else {
          // Try to refresh token
          try {
            await _authService.refreshAccessToken();
            emit(AuthAuthenticated(user: _convertToUserModel(user)));
          } catch (e) {
            // Refresh failed, sign out
            await _authService.signOut();
            emit(AuthUnauthenticated());
          }
        }
      } else {
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle sign in request
  Future<void> _onAuthSignInRequested(
    AuthSignInRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final credential = await _authService.signInWithEmailAndPassword(
        email: event.email,
        password: event.password,
      );

      // Extract user from credential (works for both Firebase and Mock)
      final user = credential?.user ?? credential;
      if (user != null) {
        emit(AuthAuthenticated(user: _convertToUserModel(user)));
      } else {
        emit(const AuthError(message: 'Sign in failed'));
      }
    } on FirebaseAuthException catch (e) {
      emit(AuthError(message: e.message ?? 'An unknown error occurred.'));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle sign up request
  Future<void> _onAuthSignUpRequested(
    AuthSignUpRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      // Validate password confirmation
      if (event.password != event.confirmPassword) {
        emit(const AuthError(message: 'Passwords do not match'));
        return;
      }

      final credential = await _authService.createUserWithEmailAndPassword(
        email: event.email,
        password: event.password,
        name: event.name,
      );

      // Extract user from credential (works for both Firebase and Mock)
      final user = credential?.user ?? credential;
      if (user != null) {
        emit(AuthAuthenticated(user: _convertToUserModel(user)));
      } else {
        emit(const AuthError(message: 'Sign up failed'));
      }
    } on FirebaseAuthException catch (e) {
      emit(AuthError(message: ErrorHandler.handleFirebaseAuthError(e)));
    } catch (e) {
      emit(AuthError(message: ErrorHandler.handleGeneralError(e)));
    }
  }

  /// Handle Google sign in request
  Future<void> _onAuthSignInWithGoogleRequested(
    AuthSignInWithGoogleRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final result = await _authService.signInWithGoogle();

      if (result == null) {
        // User cancelled Google Sign-In
        emit(AuthUnauthenticated());
        return;
      }

      // Don't emit AuthAuthenticated here - let the authStateChanges stream handle it
      // This prevents the double state emission that causes the loop

    } on FirebaseAuthException catch (e) {
      emit(AuthError(message: ErrorHandler.handleFirebaseAuthError(e)));
    } catch (e) {
      emit(AuthError(message: ErrorHandler.handleGeneralError(e)));
    }
  }

  /// Handle Google sign up request
  Future<void> _onAuthSignUpWithGoogleRequested(
    AuthSignUpWithGoogleRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      await _authService.signUpWithGoogle();
      
      // Don't emit AuthAuthenticated here - let the authStateChanges stream handle it
      // This prevents the double state emission that causes the loop
      
    } on FirebaseAuthException catch (e) {
      emit(AuthError(message: e.message ?? 'An unknown error occurred.'));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle sign out request
  Future<void> _onAuthSignOutRequested(
    AuthSignOutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      await _authService.signOut();
      emit(AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle password reset request
  Future<void> _onAuthPasswordResetRequested(
    AuthPasswordResetRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      await _authService.sendPasswordResetEmail(event.email);
      emit(AuthPasswordResetSent(email: event.email));
    } on FirebaseAuthException catch (e) {
      emit(AuthError(message: e.message ?? 'An unknown error occurred.'));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle authentication state changes from auth service
  void _onAuthStateChanged(
    AuthStateChanged event,
    Emitter<AuthState> emit,
  ) {
    if (event.isAuthenticated) {
      final user = _authService.currentUser;
      if (user != null) {
        emit(AuthAuthenticated(user: _convertToUserModel(user)));
      }
    } else {
      emit(AuthUnauthenticated());
    }
  }

  @override
  Future<void> close() {
    _authStateSubscription.cancel();
    return super.close();
  }
}
