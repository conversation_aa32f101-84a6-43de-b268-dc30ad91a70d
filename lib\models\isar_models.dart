import 'package:isar/isar.dart';

part 'isar_models.g.dart';

/// Local task model for Isar database
@collection
class LocalTask {
  Id id = Isar.autoIncrement;
  
  @Index()
  String? serverId; // Server-side ID after sync
  
  late String title;
  String? description;
  late bool isCompleted;
  late String priority; // 'low', 'medium', 'high'
  String? category; // For broader grouping (e.g., 'work', 'personal', 'health')
  List<String> tags = []; // For additional organization
  DateTime? dueDate;
  late DateTime createdAt;
  late DateTime updatedAt;
  
  @Index()
  late bool needsSync; // True if local changes need to be synced
  
  @Index()
  late String syncStatus; // 'synced', 'pending', 'error'
  
  int syncRetryCount = 0;
  DateTime? lastSyncAttempt;
}

/// Local note model for Isar database
@collection
class LocalNote {
  Id id = Isar.autoIncrement;
  
  @Index()
  String? serverId; // Server-side ID after sync
  
  late String title;
  late String content;
  late bool isPinned;
  List<String> tags = [];
  late DateTime createdAt;
  late DateTime updatedAt;
  
  @Index()
  late bool needsSync; // True if local changes need to be synced
  
  @Index()
  late String syncStatus; // 'synced', 'pending', 'error'
  
  int syncRetryCount = 0;
  DateTime? lastSyncAttempt;
}

/// Local user model for Isar database
@collection
class LocalUser {
  Id id = Isar.autoIncrement;
  
  @Index()
  String? serverId; // Server-side ID after sync
  
  late String email;
  String? displayName;
  String? photoUrl;
  late DateTime createdAt;
  late DateTime updatedAt;
  
  @Index()
  late bool needsSync; // True if local changes need to be synced
  
  @Index()
  late String syncStatus; // 'synced', 'pending', 'error'
  
  int syncRetryCount = 0;
  DateTime? lastSyncAttempt;
}

/// Sync operation model for Isar database
@collection
class LocalSyncOperation {
  Id id = Isar.autoIncrement;
  
  late String operationType; // 'create', 'update', 'delete'
  late String entityType; // 'task', 'note', 'user'
  String? entityId; // Server ID if available
  String? localId; // Local ID for new entities
  late String dataJson; // JSON serialized data
  late DateTime timestamp;
  int retryCount = 0;
  DateTime? lastAttempt;
  String? errorMessage;
  
  @Index()
  late String status; // 'pending', 'processing', 'completed', 'failed'
}

/// Dashboard cache model for Isar database
@collection
class LocalDashboardCache {
  Id id = Isar.autoIncrement;
  
  late String dataJson; // JSON serialized dashboard data
  late DateTime cachedAt;
  late DateTime expiresAt;
  
  @Index()
  late String cacheKey; // 'dashboard_data'
}
