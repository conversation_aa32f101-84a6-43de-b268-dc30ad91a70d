/// Stub implementation for Firebase services when not available
/// This is used on platforms where Firebase is not supported or has compatibility issues

class FirebaseServiceStub {
  static Future<void> initializeApp() async {
    // No-op for platforms without Firebase support
    print('Firebase initialization skipped - not supported on this platform');
  }
}

/// Stub implementation for Firebase Auth when not available
class FirebaseAuthStub {
  static FirebaseAuthStub? get instance => null;
  
  Stream<dynamic> authStateChanges() => Stream.value(null);
  
  dynamic get currentUser => null;
  
  Future<dynamic> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    throw UnsupportedError('Firebase Auth not available on this platform');
  }
  
  Future<dynamic> createUserWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    throw UnsupportedError('Firebase Auth not available on this platform');
  }
  
  Future<void> signOut() async {
    throw UnsupportedError('Firebase Auth not available on this platform');
  }
}
