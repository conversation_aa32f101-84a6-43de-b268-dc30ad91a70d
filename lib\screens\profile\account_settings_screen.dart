import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/auth/auth_bloc.dart';
import '../../blocs/auth/auth_event.dart';
import '../../utils/design_tokens.dart';

class AccountSettingsScreen extends StatefulWidget {
  const AccountSettingsScreen({super.key});

  @override
  State<AccountSettingsScreen> createState() => _AccountSettingsScreenState();
}

class _AccountSettingsScreenState extends State<AccountSettingsScreen>
    with TickerProviderStateMixin {
  
  late AnimationController _pageAnimationController;
  late Animation<Offset> _pageSlideAnimation;
  
  // Form controllers
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _darvisNameController = TextEditingController();
  
  // State variables
  bool _twoFactorEnabled = false;
  bool _biometricEnabled = true;
  String _selectedTimeZone = 'UTC-5 (Eastern Time)';
  String _currentPlan = 'Essential';

  // Subscription sliding
  late PageController _subscriptionPageController;
  int _currentSubscriptionPage = 0;

  // Comprehensive timezone list
  final List<String> _timezones = [
    'UTC-12 (Baker Island)',
    'UTC-11 (American Samoa)',
    'UTC-10 (Hawaii)',
    'UTC-9 (Alaska)',
    'UTC-8 (Pacific Time)',
    'UTC-7 (Mountain Time)',
    'UTC-6 (Central Time)',
    'UTC-5 (Eastern Time)',
    'UTC-4 (Atlantic Time)',
    'UTC-3 (Argentina)',
    'UTC-2 (South Georgia)',
    'UTC-1 (Azores)',
    'UTC+0 (GMT/London)',
    'UTC+1 (Central Europe)',
    'UTC+2 (Eastern Europe)',
    'UTC+3 (Moscow)',
    'UTC+4 (Dubai)',
    'UTC+5 (Pakistan)',
    'UTC+5:30 (India)',
    'UTC+6 (Bangladesh)',
    'UTC+7 (Thailand)',
    'UTC+8 (China/Singapore)',
    'UTC+9 (Japan/Korea)',
    'UTC+10 (Australia East)',
    'UTC+11 (Solomon Islands)',
    'UTC+12 (New Zealand)',
  ];
  
  // Usage stats
  final int _sessionsThisMonth = 23;
  final int _notesCreated = 47;
  final int _tasksCompleted = 156;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _nameController.text = 'David';
    _darvisNameController.text = 'David';
    _subscriptionPageController = PageController();
  }

  void _initializeAnimations() {
    _pageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 350),
      vsync: this,
    );

    _pageSlideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _pageAnimationController,
      curve: Curves.easeOut,
    ));

    // Delay animation start until after page route transition completes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _pageAnimationController.forward();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: SafeArea(
        child: SlideTransition(
          position: _pageSlideAnimation,
          child: CustomScrollView(
            physics: const ClampingScrollPhysics(),
            slivers: [
              _buildHeader(),
              _buildPersonalInfoSection(),
              _buildAuthenticationSection(),
              _buildSubscriptionSection(),
              _buildAccountManagementSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return SliverToBoxAdapter(
      child: Container(
        height: 80,
        padding: const EdgeInsets.fromLTRB(
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingSm,
        ),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                Icons.arrow_back,
                color: DesignTokens.iconPrimary,
              ),
            ),
            Expanded(
              child: Center(
                child: ShaderMask(
                  shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
                  child: Text(
                    'Account Settings',
                    style: DesignTokens.therapySessionTitleStyle.copyWith(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 48), // Balance the back button
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Personal Information',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Profile photo
            Center(
              child: GestureDetector(
                onTap: _changeProfilePhoto,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: DesignTokens.primaryInteractiveBlue,
                      width: 3,
                    ),
                  ),
                  child: ClipOval(
                    child: Container(
                      color: DesignTokens.backgroundCard,
                      child: Center(
                        child: Image.asset(
                          'assets/images/darvis_main.PNG',
                          width: 60,
                          height: 60,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: DesignTokens.spacingLg),
            
            // Name field
            _buildInputField(
              'Name',
              _nameController,
              'Enter your name',
            ),
            
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Darvis name field
            _buildInputField(
              'What should Darvis call you?',
              _darvisNameController,
              'How Darvis addresses you',
            ),
            
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Time zone dropdown
            _buildDropdownField(
              'Time Zone',
              _selectedTimeZone,
              _timezones,
              (value) {
                setState(() {
                  _selectedTimeZone = value!;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputField(String label, TextEditingController controller, String hint) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: DesignTokens.bodyStyle.copyWith(
            fontWeight: FontWeight.w600,
            color: DesignTokens.textPrimary,
          ),
        ),
        const SizedBox(height: DesignTokens.spacingSm),
        Container(
          decoration: BoxDecoration(
            color: DesignTokens.backgroundTextInput,
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusXl),
          ),
          child: TextField(
            controller: controller,
            style: DesignTokens.bodyStyle.copyWith(
              color: DesignTokens.textOnLightBackground,
            ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textOnLightBackground.withValues(alpha: 0.6),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusXl),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusXl),
                borderSide: const BorderSide(
                  color: DesignTokens.primaryInteractiveBlue,
                  width: 2,
                ),
              ),
              contentPadding: const EdgeInsets.all(DesignTokens.spacingMd),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField(String label, String value, List<String> options, Function(String?) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: DesignTokens.bodyStyle.copyWith(
            fontWeight: FontWeight.w600,
            color: DesignTokens.textPrimary,
          ),
        ),
        const SizedBox(height: DesignTokens.spacingSm),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
          decoration: BoxDecoration(
            color: DesignTokens.backgroundCard,
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
            border: Border.all(
              color: DesignTokens.borderInteractiveElement,
              width: 1,
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              isExpanded: true,
              icon: const Icon(Icons.keyboard_arrow_down, color: DesignTokens.iconPrimary),
              style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
              dropdownColor: DesignTokens.backgroundCard,
              onChanged: onChanged,
              selectedItemBuilder: (context) {
                return options.map((String option) {
                  return Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      option,
                      style: DesignTokens.bodyStyle.copyWith(
                        color: DesignTokens.primaryInteractiveBlue,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  );
                }).toList();
              },
              items: options.map((String option) {
                return DropdownMenuItem<String>(
                  value: option,
                  child: Text(option),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  void _changeProfilePhoto() {
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Profile photo change coming soon!'),
        backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Widget _buildAuthenticationSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Authentication & Security',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),

            // Change password button
            _buildActionButton(
              'Change Password',
              'Update your account password',
              Icons.lock_outline,
              () => _showComingSoon('Change Password'),
            ),

            const SizedBox(height: DesignTokens.spacingMd),

            // Two-factor authentication toggle
            _buildToggleItem(
              'Two-Factor Authentication',
              'Add an extra layer of security',
              _twoFactorEnabled,
              (value) {
                setState(() {
                  _twoFactorEnabled = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),

            const SizedBox(height: DesignTokens.spacingMd),

            // Biometric login toggle
            _buildToggleItem(
              'Biometric Login',
              'Use Face ID or Touch ID to sign in',
              _biometricEnabled,
              (value) {
                setState(() {
                  _biometricEnabled = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Subscription & Usage',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),

            // Subscription cards with horizontal sliding
            SizedBox(
              height: 280,
              child: PageView(
                controller: _subscriptionPageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentSubscriptionPage = index;
                  });
                },
                children: [
                  // Current plan card
                  _buildSubscriptionCard(
                    'Essential',
                    'Free',
                    _getPlanFeatures('Essential'),
                    isCurrentPlan: _currentPlan == 'Essential',
                    onTap: () => _showPlanDetails('Essential'),
                  ),
                  // Upgrade plan card
                  _buildSubscriptionCard(
                    'Unlimited',
                    '\$9.99/month',
                    _getPlanFeatures('Unlimited'),
                    isCurrentPlan: _currentPlan == 'Unlimited',
                    onTap: () => _showPlanDetails('Unlimited'),
                  ),
                ],
              ),
            ),

            const SizedBox(height: DesignTokens.spacingMd),

            // Page indicators
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildPageIndicator(0),
                const SizedBox(width: DesignTokens.spacingSm),
                _buildPageIndicator(1),
              ],
            ),

            const SizedBox(height: DesignTokens.spacingLg),

            // Usage statistics
            Text(
              'Usage Statistics',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 16),
            ),
            const SizedBox(height: DesignTokens.spacingMd),

            Row(
              children: [
                Expanded(
                  child: _buildStatCard('Sessions', '$_sessionsThisMonth', 'this month'),
                ),
                const SizedBox(width: DesignTokens.spacingMd),
                Expanded(
                  child: _buildStatCard('Notes', '$_notesCreated', 'created'),
                ),
                const SizedBox(width: DesignTokens.spacingMd),
                Expanded(
                  child: _buildStatCard('Tasks', '$_tasksCompleted', 'completed'),
                ),
              ],
            ),

            if (_currentPlan == 'Essential') ...[
              const SizedBox(height: DesignTokens.spacingLg),

              // Upgrade button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => _showComingSoon('Upgrade to Unlimited'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: DesignTokens.primaryInteractiveBlue,
                    padding: const EdgeInsets.symmetric(
                      vertical: DesignTokens.spacingMd,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                    ),
                  ),
                  child: Text(
                    'UPGRADE TO UNLIMITED',
                    style: DesignTokens.bodyStyle.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAccountManagementSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Account Management',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),

            // Sign out button
            _buildActionButton(
              'Sign Out',
              'Sign out of your account',
              Icons.logout,
              () => _showSignOutDialog(),
              isDestructive: false,
            ),

            const SizedBox(height: DesignTokens.spacingMd),

            // Delete account button
            _buildActionButton(
              'Delete Account',
              'Permanently delete your account and data',
              Icons.delete_forever,
              () => _showDeleteAccountDialog(),
              isDestructive: true,
            ),

            const SizedBox(height: DesignTokens.spacingXl),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(String title, String subtitle, IconData icon, VoidCallback onTap, {bool isDestructive = false}) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: Container(
          padding: const EdgeInsets.all(DesignTokens.spacingMd),
          decoration: BoxDecoration(
            color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
            border: Border.all(
              color: isDestructive
                  ? DesignTokens.priorityHigh.withValues(alpha: 0.3)
                  : Colors.white.withValues(alpha: 0.08),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: (isDestructive
                      ? DesignTokens.priorityHigh
                      : DesignTokens.primaryInteractiveBlue).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                ),
                child: Icon(
                  icon,
                  color: isDestructive
                      ? DesignTokens.priorityHigh
                      : DesignTokens.primaryInteractiveBlue,
                  size: 24,
                ),
              ),
              const SizedBox(width: DesignTokens.spacingMd),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: DesignTokens.cardTitleStyle.copyWith(
                        fontSize: 16,
                        color: isDestructive ? DesignTokens.priorityHigh : DesignTokens.textPrimary,
                      ),
                    ),
                    const SizedBox(height: DesignTokens.spacingXs),
                    Text(
                      subtitle,
                      style: DesignTokens.bodyStyle.copyWith(
                        color: DesignTokens.textSecondary,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right,
                color: DesignTokens.iconSecondary,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildToggleItem(String title, String subtitle, bool value, Function(bool) onChanged) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: DesignTokens.cardTitleStyle.copyWith(fontSize: 16),
                ),
                const SizedBox(height: DesignTokens.spacingXs),
                Text(
                  subtitle,
                  style: DesignTokens.bodyStyle.copyWith(
                    color: DesignTokens.textSecondary,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: DesignTokens.primaryInteractiveBlue,
            inactiveTrackColor: DesignTokens.navigationInactive,
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value, String subtitle) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: DesignTokens.cardTitleStyle.copyWith(
              fontSize: 20,
              color: DesignTokens.primaryInteractiveBlue,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingXs),
          Text(
            label,
            style: DesignTokens.bodyStyle.copyWith(
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
          Text(
            subtitle,
            style: DesignTokens.bodyStyle.copyWith(
              color: DesignTokens.textSecondary,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  List<String> _getPlanFeatures(String plan) {
    if (plan == 'Essential') {
      return [
        '50 conversations/month',
        'Basic therapy sessions',
        'Simple task management',
        'Standard support',
      ];
    } else {
      return [
        'Unlimited conversations',
        'Advanced therapy insights',
        'Priority AI processing',
        'Premium support',
        'Advanced analytics',
        'Custom integrations',
      ];
    }
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature coming soon!'),
        backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showSignOutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DesignTokens.backgroundCard,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        ),
        title: Text(
          'Sign Out',
          style: DesignTokens.cardTitleStyle,
        ),
        content: Text(
          'Are you sure you want to sign out?',
          style: DesignTokens.bodyStyle.copyWith(
            color: DesignTokens.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textSecondary,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Dispatch real sign-out
              context.read<AuthBloc>().add(AuthSignOutRequested());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: DesignTokens.priorityHigh,
            ),
            child: Text(
              'Sign Out',
              style: DesignTokens.bodyStyle.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DesignTokens.backgroundCard,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        ),
        title: Text(
          'Delete Account',
          style: DesignTokens.cardTitleStyle.copyWith(
            color: DesignTokens.priorityHigh,
          ),
        ),
        content: Text(
          'This action cannot be undone. All your data will be permanently deleted.',
          style: DesignTokens.bodyStyle.copyWith(
            color: DesignTokens.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textSecondary,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoon('Delete Account');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: DesignTokens.priorityHigh,
            ),
            child: Text(
              'Delete',
              style: DesignTokens.bodyStyle.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionCard(String planName, String price, List<String> features, {required bool isCurrentPlan, required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingSm),
        padding: const EdgeInsets.all(DesignTokens.spacingLg),
        decoration: BoxDecoration(
          color: isCurrentPlan
              ? DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.2)
              : DesignTokens.backgroundCard,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          border: Border.all(
            color: isCurrentPlan
                ? DesignTokens.primaryInteractiveBlue
                : Colors.white.withValues(alpha: 0.1),
            width: isCurrentPlan ? 2 : 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'DARVIS $planName'.toUpperCase(),
                  style: DesignTokens.cardTitleStyle.copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: isCurrentPlan ? DesignTokens.primaryInteractiveBlue : DesignTokens.textPrimary,
                  ),
                ),
                if (isCurrentPlan)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: DesignTokens.spacingSm,
                      vertical: DesignTokens.spacingXs,
                    ),
                    decoration: BoxDecoration(
                      color: DesignTokens.primaryInteractiveBlue,
                      borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
                    ),
                    child: Text(
                      'Current',
                      style: DesignTokens.bodyStyle.copyWith(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: DesignTokens.spacingSm),
            Text(
              price,
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textSecondary,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: DesignTokens.spacingMd),

            // Features list
            ...features.take(4).map((feature) => Padding(
              padding: const EdgeInsets.only(bottom: DesignTokens.spacingXs),
              child: Row(
                children: [
                  Icon(
                    Icons.check,
                    color: isCurrentPlan ? DesignTokens.primaryInteractiveBlue : DesignTokens.textSecondary,
                    size: 16,
                  ),
                  const SizedBox(width: DesignTokens.spacingSm),
                  Expanded(
                    child: Text(
                      feature,
                      style: DesignTokens.bodyStyle.copyWith(
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            )),

            if (features.length > 4)
              Text(
                '+${features.length - 4} more features',
                style: DesignTokens.bodyStyle.copyWith(
                  color: DesignTokens.textMuted,
                  fontSize: 12,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPageIndicator(int index) {
    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: _currentSubscriptionPage == index
            ? DesignTokens.primaryInteractiveBlue
            : DesignTokens.navigationInactive,
      ),
    );
  }

  void _showPlanDetails(String planName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DesignTokens.backgroundCard,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        ),
        title: Text(
          'DARVIS $planName'.toUpperCase(),
          style: DesignTokens.cardTitleStyle,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              planName == 'Essential' ? 'Free' : '\$9.99/month',
              style: DesignTokens.bodyStyle.copyWith(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: DesignTokens.primaryInteractiveBlue,
              ),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            Text(
              'Features:',
              style: DesignTokens.bodyStyle.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: DesignTokens.spacingSm),
            ..._getPlanFeatures(planName).map((feature) => Padding(
              padding: const EdgeInsets.only(bottom: DesignTokens.spacingXs),
              child: Row(
                children: [
                  const Icon(
                    Icons.check,
                    color: DesignTokens.primaryInteractiveBlue,
                    size: 16,
                  ),
                  const SizedBox(width: DesignTokens.spacingSm),
                  Expanded(
                    child: Text(
                      feature,
                      style: DesignTokens.bodyStyle.copyWith(fontSize: 14),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Close',
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textSecondary,
              ),
            ),
          ),
          if (planName != _currentPlan)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _showComingSoon('Upgrade to $planName');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: DesignTokens.primaryInteractiveBlue,
              ),
              child: Text(
                planName == 'Essential' ? 'Downgrade' : 'Upgrade',
                style: DesignTokens.bodyStyle.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _pageAnimationController.dispose();
    _subscriptionPageController.dispose();
    _nameController.dispose();
    _darvisNameController.dispose();
    super.dispose();
  }
}
