import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:isar/isar.dart';
import '../../models/isar_models.dart';
import '../../services/api_service.dart';
import '../../services/sync_engine.dart';
import 'tasks_event.dart';
import 'tasks_state.dart';

/// BLoC for managing tasks with offline-first approach
class TasksBloc extends Bloc<TasksEvent, TasksState> {
  final Isar _isar;
  final ApiService _apiService;
  final SyncEngine _syncEngine;

  TasksBloc({
    required Isar isar,
    required ApiService apiService,
    required SyncEngine syncEngine,
  })  : _isar = isar,
        _apiService = apiService,
        _syncEngine = syncEngine,
        super(const TasksInitial()) {
    
    // Register event handlers
    on<LoadTasks>(_onLoadTasks);
    on<RefreshTasks>(_onRefreshTasks);
    on<AddTask>(_onAddTask);
    on<UpdateTask>(_onUpdateTask);
    on<DeleteTask>(_onDeleteTask);
    on<ToggleTaskCompletion>(_onToggleTaskCompletion);
    on<SearchTasks>(_onSearchTasks);
    on<FilterTasksByStatus>(_onFilterTasksByStatus);
    on<FilterTasksByPriority>(_onFilterTasksByPriority);
    on<FilterTasksByCategory>(_onFilterTasksByCategory);
    on<FilterTasksByDueDate>(_onFilterTasksByDueDate);
    on<SortTasks>(_onSortTasks);
    on<ClearTasksFilters>(_onClearTasksFilters);
    on<ToggleTaskSelection>(_onToggleTaskSelection);
    on<ClearTaskSelections>(_onClearTaskSelections);
    on<DeleteSelectedTasks>(_onDeleteSelectedTasks);
    on<CompleteSelectedTasks>(_onCompleteSelectedTasks);
    on<SetPriorityForSelectedTasks>(_onSetPriorityForSelectedTasks);

    on<ExportTasks>(_onExportTasks);
    on<SyncTasks>(_onSyncTasks);
  }

  /// Load tasks from local database
  Future<void> _onLoadTasks(
    LoadTasks event,
    Emitter<TasksState> emit,
  ) async {
    emit(const TasksLoading());

    try {
      final tasks = await _isar.localTasks.where().sortByCreatedAtDesc().findAll();
      final availableCategories = _extractAvailableCategories(tasks);
      final availableTags = _extractAvailableTags(tasks);
      
      emit(TasksLoaded(
        tasks: tasks,
        filteredTasks: tasks,
        availableCategories: availableCategories,
        availableTags: availableTags,
      ));

      // Trigger background sync
      add(const SyncTasks());
    } catch (e) {
      emit(TasksOperationError(
        error: 'Failed to load tasks: $e',
        operation: 'load',
      ));
    }
  }

  /// Refresh tasks from server
  Future<void> _onRefreshTasks(
    RefreshTasks event,
    Emitter<TasksState> emit,
  ) async {
    try {
      // Force sync with server
      await _syncEngine.sync();
      
      // Reload from local database
      add(const LoadTasks());
    } catch (e) {
      emit(TasksOperationError(
        error: 'Failed to refresh tasks: $e',
        operation: 'refresh',
      ));
    }
  }

  /// Add a new task
  Future<void> _onAddTask(
    AddTask event,
    Emitter<TasksState> emit,
  ) async {
    emit(const TasksOperationInProgress(operation: 'add'));

    try {
      final now = DateTime.now();
      final task = LocalTask()
        ..title = event.title
        ..description = event.description
        ..dueDate = event.dueDate
        ..priority = event.priority
        ..category = event.category
        ..tags = event.tags
        ..isCompleted = false
        ..createdAt = now
        ..updatedAt = now
        ..needsSync = true
        ..syncStatus = 'pending'
        ..syncRetryCount = 0;

      await _isar.writeTxn(() async {
        await _isar.localTasks.put(task);
      });

      emit(TasksOperationSuccess(
        message: 'Task created successfully',
        operation: 'add',
        task: task,
      ));

      // Reload tasks
      add(const LoadTasks());

      // Queue sync operation
      _syncEngine.queueCreate(
        entityType: 'task',
        data: {
          'title': task.title,
          'description': task.description,
          'dueDate': task.dueDate?.toIso8601String(),
          'priority': task.priority,
          'category': task.category,
          'tags': task.tags,
          'isCompleted': task.isCompleted,
        },
        localId: task.id.toString(),
      );
    } catch (e) {
      emit(TasksOperationError(
        error: 'Failed to create task: $e',
        operation: 'add',
      ));
    }
  }

  /// Update an existing task
  Future<void> _onUpdateTask(
    UpdateTask event,
    Emitter<TasksState> emit,
  ) async {
    emit(TasksOperationInProgress(operation: 'update', taskId: event.taskId));

    try {
      final taskId = int.tryParse(event.taskId);
      if (taskId == null) {
        throw Exception('Invalid task ID');
      }

      final task = await _isar.localTasks.get(taskId);
      if (task == null) {
        throw Exception('Task not found');
      }

      // Update fields
      if (event.title != null) task.title = event.title!;
      if (event.description != null) task.description = event.description!;
      if (event.dueDate != null) task.dueDate = event.dueDate;
      if (event.priority != null) task.priority = event.priority!;
      if (event.category != null) task.category = event.category!;
      if (event.tags != null) task.tags = event.tags!;
      if (event.isCompleted != null) task.isCompleted = event.isCompleted!;
      
      task.updatedAt = DateTime.now();
      task.needsSync = true;
      task.syncStatus = 'pending';

      await _isar.writeTxn(() async {
        await _isar.localTasks.put(task);
      });

      emit(TasksOperationSuccess(
        message: 'Task updated successfully',
        operation: 'update',
        task: task,
      ));

      // Reload tasks
      add(const LoadTasks());

      // Queue sync operation
      _syncEngine.queueUpdate(
        entityType: 'task',
        id: task.serverId ?? task.id.toString(),
        data: {
          'title': task.title,
          'description': task.description,
          'dueDate': task.dueDate?.toIso8601String(),
          'priority': task.priority,
          'category': task.category,
          'tags': task.tags,
          'isCompleted': task.isCompleted,
        },
      );
    } catch (e) {
      emit(TasksOperationError(
        error: 'Failed to update task: $e',
        operation: 'update',
        taskId: event.taskId,
      ));
    }
  }

  /// Delete a task
  Future<void> _onDeleteTask(
    DeleteTask event,
    Emitter<TasksState> emit,
  ) async {
    emit(TasksOperationInProgress(operation: 'delete', taskId: event.taskId));

    try {
      final taskId = int.tryParse(event.taskId);
      if (taskId == null) {
        throw Exception('Invalid task ID');
      }

      await _isar.writeTxn(() async {
        await _isar.localTasks.delete(taskId);
      });

      emit(const TasksOperationSuccess(
        message: 'Task deleted successfully',
        operation: 'delete',
      ));

      // Reload tasks
      add(const LoadTasks());

      // Queue delete operation
      _syncEngine.queueDelete(
        entityType: 'task',
        id: event.taskId,
      );
    } catch (e) {
      emit(TasksOperationError(
        error: 'Failed to delete task: $e',
        operation: 'delete',
        taskId: event.taskId,
      ));
    }
  }

  /// Toggle task completion status
  Future<void> _onToggleTaskCompletion(
    ToggleTaskCompletion event,
    Emitter<TasksState> emit,
  ) async {
    try {
      final taskId = int.tryParse(event.taskId);
      if (taskId == null) {
        throw Exception('Invalid task ID');
      }

      final task = await _isar.localTasks.get(taskId);
      if (task == null) {
        throw Exception('Task not found');
      }

      task.isCompleted = !task.isCompleted;
      task.updatedAt = DateTime.now();
      task.needsSync = true;
      task.syncStatus = 'pending';

      await _isar.writeTxn(() async {
        await _isar.localTasks.put(task);
      });

      // Reload tasks
      add(const LoadTasks());

      // Queue update operation
      _syncEngine.queueUpdate(
        entityType: 'task',
        id: task.serverId ?? task.id.toString(),
        data: {
          'isCompleted': task.isCompleted,
        },
      );
    } catch (e) {
      emit(TasksOperationError(
        error: 'Failed to toggle task completion: $e',
        operation: 'toggle_completion',
        taskId: event.taskId,
      ));
    }
  }

  /// Search tasks by query
  Future<void> _onSearchTasks(
    SearchTasks event,
    Emitter<TasksState> emit,
  ) async {
    if (state is! TasksLoaded) return;

    final currentState = state as TasksLoaded;
    final filteredTasks = _filterAndSortTasks(
      currentState.tasks,
      searchQuery: event.query,
      statusFilter: currentState.statusFilter,
      priorityFilter: currentState.priorityFilter,
      categoryFilter: currentState.categoryFilter,
      dueDateFilter: currentState.dueDateFilter,
      sortType: currentState.sortType,
    );

    emit(currentState.copyWith(
      filteredTasks: filteredTasks,
      searchQuery: event.query,
    ));
  }

  /// Filter tasks by status
  Future<void> _onFilterTasksByStatus(
    FilterTasksByStatus event,
    Emitter<TasksState> emit,
  ) async {
    if (state is! TasksLoaded) return;

    final currentState = state as TasksLoaded;
    final filteredTasks = _filterAndSortTasks(
      currentState.tasks,
      searchQuery: currentState.searchQuery,
      statusFilter: event.status,
      priorityFilter: currentState.priorityFilter,
      categoryFilter: currentState.categoryFilter,
      dueDateFilter: currentState.dueDateFilter,
      sortType: currentState.sortType,
    );

    emit(currentState.copyWith(
      filteredTasks: filteredTasks,
      statusFilter: event.status,
    ));
  }

  /// Filter tasks by priority
  Future<void> _onFilterTasksByPriority(
    FilterTasksByPriority event,
    Emitter<TasksState> emit,
  ) async {
    if (state is! TasksLoaded) return;

    final currentState = state as TasksLoaded;
    final filteredTasks = _filterAndSortTasks(
      currentState.tasks,
      searchQuery: currentState.searchQuery,
      statusFilter: currentState.statusFilter,
      priorityFilter: event.priority,
      categoryFilter: currentState.categoryFilter,
      dueDateFilter: currentState.dueDateFilter,
      sortType: currentState.sortType,
    );

    emit(currentState.copyWith(
      filteredTasks: filteredTasks,
      priorityFilter: event.priority,
    ));
  }

  /// Filter tasks by category
  Future<void> _onFilterTasksByCategory(
    FilterTasksByCategory event,
    Emitter<TasksState> emit,
  ) async {
    if (state is! TasksLoaded) return;

    final currentState = state as TasksLoaded;
    final filteredTasks = _filterAndSortTasks(
      currentState.tasks,
      searchQuery: currentState.searchQuery,
      statusFilter: currentState.statusFilter,
      priorityFilter: currentState.priorityFilter,
      categoryFilter: event.category,
      dueDateFilter: currentState.dueDateFilter,
      sortType: currentState.sortType,
    );

    emit(currentState.copyWith(
      filteredTasks: filteredTasks,
      categoryFilter: event.category,
    ));
  }

  /// Filter tasks by due date
  Future<void> _onFilterTasksByDueDate(
    FilterTasksByDueDate event,
    Emitter<TasksState> emit,
  ) async {
    if (state is! TasksLoaded) return;

    final currentState = state as TasksLoaded;
    final filteredTasks = _filterAndSortTasks(
      currentState.tasks,
      searchQuery: currentState.searchQuery,
      statusFilter: currentState.statusFilter,
      priorityFilter: currentState.priorityFilter,
      categoryFilter: currentState.categoryFilter,
      dueDateFilter: event.dueDateFilter,
      sortType: currentState.sortType,
    );

    emit(currentState.copyWith(
      filteredTasks: filteredTasks,
      dueDateFilter: event.dueDateFilter,
    ));
  }

  /// Sort tasks
  Future<void> _onSortTasks(
    SortTasks event,
    Emitter<TasksState> emit,
  ) async {
    if (state is! TasksLoaded) return;

    final currentState = state as TasksLoaded;
    final filteredTasks = _filterAndSortTasks(
      currentState.tasks,
      searchQuery: currentState.searchQuery,
      statusFilter: currentState.statusFilter,
      priorityFilter: currentState.priorityFilter,
      categoryFilter: currentState.categoryFilter,
      dueDateFilter: currentState.dueDateFilter,
      sortType: event.sortType,
    );

    emit(currentState.copyWith(
      filteredTasks: filteredTasks,
      sortType: event.sortType,
    ));
  }

  /// Clear search and filters
  Future<void> _onClearTasksFilters(
    ClearTasksFilters event,
    Emitter<TasksState> emit,
  ) async {
    if (state is! TasksLoaded) return;

    final currentState = state as TasksLoaded;
    final filteredTasks = _filterAndSortTasks(
      currentState.tasks,
      sortType: currentState.sortType,
    );

    emit(currentState.copyWith(
      filteredTasks: filteredTasks,
      clearSearchQuery: true,
      clearStatusFilter: true,
      clearPriorityFilter: true,
      clearCategoryFilter: true,
      clearDueDateFilter: true,
    ));
  }

  /// Toggle task selection for bulk operations
  Future<void> _onToggleTaskSelection(
    ToggleTaskSelection event,
    Emitter<TasksState> emit,
  ) async {
    if (state is! TasksLoaded) return;

    final currentState = state as TasksLoaded;
    final selectedIds = Set<String>.from(currentState.selectedTaskIds);

    if (selectedIds.contains(event.taskId)) {
      selectedIds.remove(event.taskId);
    } else {
      selectedIds.add(event.taskId);
    }

    emit(currentState.copyWith(
      selectedTaskIds: selectedIds,
      isSelectionMode: selectedIds.isNotEmpty,
    ));
  }

  /// Clear all task selections
  Future<void> _onClearTaskSelections(
    ClearTaskSelections event,
    Emitter<TasksState> emit,
  ) async {
    if (state is! TasksLoaded) return;

    final currentState = state as TasksLoaded;
    emit(currentState.copyWith(
      selectedTaskIds: <String>{},
      isSelectionMode: false,
    ));
  }

  /// Delete selected tasks
  Future<void> _onDeleteSelectedTasks(
    DeleteSelectedTasks event,
    Emitter<TasksState> emit,
  ) async {
    if (state is! TasksLoaded) return;

    final currentState = state as TasksLoaded;
    final selectedIds = currentState.selectedTaskIds;

    if (selectedIds.isEmpty) return;

    emit(TasksBulkOperationInProgress(
      operation: 'delete',
      totalCount: selectedIds.length,
    ));

    try {
      await _isar.writeTxn(() async {
        for (final taskId in selectedIds) {
          final id = int.tryParse(taskId);
          if (id != null) {
            await _isar.localTasks.delete(id);
          }
        }
      });

      emit(TasksBulkOperationSuccess(
        operation: 'delete',
        processedCount: selectedIds.length,
        message: '${selectedIds.length} tasks deleted successfully',
      ));

      // Reload tasks
      add(const LoadTasks());

      // Trigger sync for bulk delete
      await _syncEngine.sync();
    } catch (e) {
      emit(TasksBulkOperationError(
        operation: 'delete',
        error: 'Failed to delete tasks: $e',
      ));
    }
  }

  /// Complete selected tasks
  Future<void> _onCompleteSelectedTasks(
    CompleteSelectedTasks event,
    Emitter<TasksState> emit,
  ) async {
    if (state is! TasksLoaded) return;

    final currentState = state as TasksLoaded;
    final selectedIds = currentState.selectedTaskIds;

    if (selectedIds.isEmpty) return;

    emit(TasksBulkOperationInProgress(
      operation: 'complete',
      totalCount: selectedIds.length,
    ));

    try {
      await _isar.writeTxn(() async {
        for (final taskId in selectedIds) {
          final id = int.tryParse(taskId);
          if (id != null) {
            final task = await _isar.localTasks.get(id);
            if (task != null) {
              task.isCompleted = true;
              task.updatedAt = DateTime.now();
              task.needsSync = true;
              task.syncStatus = 'pending';
              await _isar.localTasks.put(task);
            }
          }
        }
      });

      emit(TasksBulkOperationSuccess(
        operation: 'complete',
        processedCount: selectedIds.length,
        message: '${selectedIds.length} tasks completed successfully',
      ));

      // Reload tasks
      add(const LoadTasks());

      // Trigger sync for bulk complete
      await _syncEngine.sync();
    } catch (e) {
      emit(TasksBulkOperationError(
        operation: 'complete',
        error: 'Failed to complete tasks: $e',
      ));
    }
  }

  /// Set priority for selected tasks
  Future<void> _onSetPriorityForSelectedTasks(
    SetPriorityForSelectedTasks event,
    Emitter<TasksState> emit,
  ) async {
    if (state is! TasksLoaded) return;

    final currentState = state as TasksLoaded;
    final selectedIds = currentState.selectedTaskIds;

    if (selectedIds.isEmpty) return;

    emit(TasksBulkOperationInProgress(
      operation: 'set_priority',
      totalCount: selectedIds.length,
    ));

    try {
      await _isar.writeTxn(() async {
        for (final taskId in selectedIds) {
          final id = int.tryParse(taskId);
          if (id != null) {
            final task = await _isar.localTasks.get(id);
            if (task != null) {
              task.priority = event.priority;
              task.updatedAt = DateTime.now();
              task.needsSync = true;
              task.syncStatus = 'pending';
              await _isar.localTasks.put(task);
            }
          }
        }
      });

      emit(TasksBulkOperationSuccess(
        operation: 'set_priority',
        processedCount: selectedIds.length,
        message: 'Priority set to "${event.priority}" for ${selectedIds.length} tasks',
      ));

      // Reload tasks
      add(const LoadTasks());

      // Trigger sync
      await _syncEngine.sync();
    } catch (e) {
      emit(TasksBulkOperationError(
        operation: 'set_priority',
        error: 'Failed to set priority: $e',
      ));
    }
  }

  /// Export tasks
  Future<void> _onExportTasks(
    ExportTasks event,
    Emitter<TasksState> emit,
  ) async {
    emit(const TasksExportInProgress());

    try {
      List<LocalTask> tasksToExport;

      if (event.taskIds != null) {
        // Export specific tasks
        tasksToExport = [];
        for (final taskId in event.taskIds!) {
          final id = int.tryParse(taskId);
          if (id != null) {
            final task = await _isar.localTasks.get(id);
            if (task != null) {
              tasksToExport.add(task);
            }
          }
        }
      } else {
        // Export all tasks
        tasksToExport = await _isar.localTasks.where().findAll();
      }

      // TODO: Implement actual export functionality
      // For now, just simulate success
      emit(TasksExportSuccess(
        filePath: '/path/to/exported/tasks.${event.format}',
        exportedCount: tasksToExport.length,
        format: event.format,
      ));
    } catch (e) {
      emit(TasksExportError('Failed to export tasks: $e'));
    }
  }

  /// Sync tasks with server
  Future<void> _onSyncTasks(
    SyncTasks event,
    Emitter<TasksState> emit,
  ) async {
    emit(const TasksSyncInProgress());

    try {
      await _syncEngine.sync();

      final syncedCount = await _isar.localTasks.where().syncStatusEqualTo('synced').count();

      emit(TasksSyncSuccess(
        syncedCount: syncedCount,
        message: 'Tasks synchronized successfully',
      ));

      // Reload tasks after sync
      add(const LoadTasks());
    } catch (e) {
      emit(TasksSyncError('Failed to sync tasks: $e'));
    }
  }

  /// Helper method to filter and sort tasks
  List<LocalTask> _filterAndSortTasks(
    List<LocalTask> tasks, {
    String? searchQuery,
    TaskStatusFilter? statusFilter,
    String? priorityFilter,
    String? categoryFilter,
    TaskDueDateFilter? dueDateFilter,
    TasksSortType sortType = TasksSortType.dueDateAsc,
  }) {
    var filteredTasks = List<LocalTask>.from(tasks);

    // Apply search filter
    if (searchQuery != null && searchQuery.isNotEmpty) {
      filteredTasks = filteredTasks.where((task) {
        final query = searchQuery.toLowerCase();
        return task.title.toLowerCase().contains(query) ||
               (task.description?.toLowerCase().contains(query) ?? false) ||
               (task.category?.toLowerCase().contains(query) ?? false) ||
               task.tags.any((tag) => tag.toLowerCase().contains(query));
      }).toList();
    }

    // Apply status filter
    if (statusFilter != null) {
      switch (statusFilter) {
        case TaskStatusFilter.pending:
          filteredTasks = filteredTasks.where((task) => !task.isCompleted).toList();
          break;
        case TaskStatusFilter.completed:
          filteredTasks = filteredTasks.where((task) => task.isCompleted).toList();
          break;
        case TaskStatusFilter.overdue:
          final now = DateTime.now();
          filteredTasks = filteredTasks.where((task) {
            return !task.isCompleted &&
                   task.dueDate != null &&
                   task.dueDate!.isBefore(now);
          }).toList();
          break;
        case TaskStatusFilter.all:
          // No filtering
          break;
      }
    }

    // Apply priority filter
    if (priorityFilter != null && priorityFilter.isNotEmpty) {
      filteredTasks = filteredTasks.where((task) {
        return task.priority == priorityFilter;
      }).toList();
    }

    // Apply category filter
    if (categoryFilter != null && categoryFilter.isNotEmpty) {
      filteredTasks = filteredTasks.where((task) {
        return task.category == categoryFilter;
      }).toList();
    }

    // Apply due date filter
    if (dueDateFilter != null) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final tomorrow = today.add(const Duration(days: 1));
      final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
      final endOfWeek = startOfWeek.add(const Duration(days: 7));
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 1);

      switch (dueDateFilter) {
        case TaskDueDateFilter.today:
          filteredTasks = filteredTasks.where((task) {
            return task.dueDate != null &&
                   task.dueDate!.isAfter(today) &&
                   task.dueDate!.isBefore(tomorrow);
          }).toList();
          break;
        case TaskDueDateFilter.tomorrow:
          final dayAfterTomorrow = tomorrow.add(const Duration(days: 1));
          filteredTasks = filteredTasks.where((task) {
            return task.dueDate != null &&
                   task.dueDate!.isAfter(tomorrow) &&
                   task.dueDate!.isBefore(dayAfterTomorrow);
          }).toList();
          break;
        case TaskDueDateFilter.thisWeek:
          filteredTasks = filteredTasks.where((task) {
            return task.dueDate != null &&
                   task.dueDate!.isAfter(startOfWeek) &&
                   task.dueDate!.isBefore(endOfWeek);
          }).toList();
          break;
        case TaskDueDateFilter.thisMonth:
          filteredTasks = filteredTasks.where((task) {
            return task.dueDate != null &&
                   task.dueDate!.isAfter(startOfMonth) &&
                   task.dueDate!.isBefore(endOfMonth);
          }).toList();
          break;
        case TaskDueDateFilter.overdue:
          filteredTasks = filteredTasks.where((task) {
            return task.dueDate != null && task.dueDate!.isBefore(now);
          }).toList();
          break;
        case TaskDueDateFilter.noDate:
          filteredTasks = filteredTasks.where((task) => task.dueDate == null).toList();
          break;
        case TaskDueDateFilter.all:
          // No filtering
          break;
      }
    }

    // Apply sorting
    switch (sortType) {
      case TasksSortType.createdAtDesc:
        filteredTasks.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case TasksSortType.createdAtAsc:
        filteredTasks.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case TasksSortType.updatedAtDesc:
        filteredTasks.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
        break;
      case TasksSortType.updatedAtAsc:
        filteredTasks.sort((a, b) => a.updatedAt.compareTo(b.updatedAt));
        break;
      case TasksSortType.titleAsc:
        filteredTasks.sort((a, b) => a.title.compareTo(b.title));
        break;
      case TasksSortType.titleDesc:
        filteredTasks.sort((a, b) => b.title.compareTo(a.title));
        break;
      case TasksSortType.dueDateAsc:
        filteredTasks.sort((a, b) {
          if (a.dueDate == null && b.dueDate == null) return 0;
          if (a.dueDate == null) return 1;
          if (b.dueDate == null) return -1;
          return a.dueDate!.compareTo(b.dueDate!);
        });
        break;
      case TasksSortType.dueDateDesc:
        filteredTasks.sort((a, b) {
          if (a.dueDate == null && b.dueDate == null) return 0;
          if (a.dueDate == null) return 1;
          if (b.dueDate == null) return -1;
          return b.dueDate!.compareTo(a.dueDate!);
        });
        break;
      case TasksSortType.priorityHigh:
        filteredTasks.sort((a, b) {
          const priorityOrder = {'high': 0, 'medium': 1, 'low': 2};
          final aPriority = priorityOrder[a.priority] ?? 3;
          final bPriority = priorityOrder[b.priority] ?? 3;
          return aPriority.compareTo(bPriority);
        });
        break;
      case TasksSortType.priorityLow:
        filteredTasks.sort((a, b) {
          const priorityOrder = {'low': 0, 'medium': 1, 'high': 2};
          final aPriority = priorityOrder[a.priority] ?? 3;
          final bPriority = priorityOrder[b.priority] ?? 3;
          return aPriority.compareTo(bPriority);
        });
        break;
      case TasksSortType.status:
        filteredTasks.sort((a, b) {
          if (a.isCompleted && !b.isCompleted) return 1;
          if (!a.isCompleted && b.isCompleted) return -1;
          return a.title.compareTo(b.title);
        });
        break;
    }

    return filteredTasks;
  }

  /// Helper method to extract available categories from tasks
  List<String> _extractAvailableCategories(List<LocalTask> tasks) {
    final categories = <String>{};
    for (final task in tasks) {
      if (task.category != null && task.category!.isNotEmpty) {
        categories.add(task.category!);
      }
    }
    return categories.toList()..sort();
  }

  /// Helper method to extract available tags from tasks
  List<String> _extractAvailableTags(List<LocalTask> tasks) {
    final tags = <String>{};
    for (final task in tasks) {
      tags.addAll(task.tags);
    }
    return tags.toList()..sort();
  }
}
