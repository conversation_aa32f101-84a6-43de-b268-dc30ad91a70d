import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import '../../utils/design_tokens.dart';

class DataStorageScreen extends StatefulWidget {
  const DataStorageScreen({super.key});

  @override
  State<DataStorageScreen> createState() => _DataStorageScreenState();
}

class _DataStorageScreenState extends State<DataStorageScreen>
    with TickerProviderStateMixin {
  
  late AnimationController _pageAnimationController;
  late Animation<Offset> _pageSlideAnimation;
  
  // State variables
  bool _crossDeviceSync = true;
  bool _syncConversations = true;
  bool _syncNotes = true;
  bool _syncTasks = true;
  bool _syncSettings = true;
  String _syncFrequency = 'Real-time';
  
  // Storage stats
  final double _usedStorage = 2.3; // GB
  final double _totalStorage = 5.0; // GB

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 350),
      vsync: this,
    );

    _pageSlideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _pageAnimationController,
      curve: Curves.easeOut,
    ));

    // Delay animation start until after page route transition completes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _pageAnimationController.forward();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: SafeArea(
        child: SlideTransition(
          position: _pageSlideAnimation,
          child: CustomScrollView(
            physics: const ClampingScrollPhysics(),
            slivers: [
              _buildHeader(),
              _buildStorageOverviewSection(),
              _buildCloudSyncSection(),
              _buildBackupSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return SliverToBoxAdapter(
      child: Container(
        height: 80,
        padding: const EdgeInsets.fromLTRB(
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingSm,
        ),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                Icons.arrow_back,
                color: DesignTokens.iconPrimary,
              ),
            ),
            Expanded(
              child: Center(
                child: ShaderMask(
                  shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
                  child: Text(
                    'Data & Storage',
                    style: DesignTokens.therapySessionTitleStyle.copyWith(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 48), // Balance the back button
          ],
        ),
      ),
    );
  }

  Widget _buildStorageOverviewSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Storage Overview',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            
            Container(
              padding: const EdgeInsets.all(DesignTokens.spacingLg),
              decoration: BoxDecoration(
                color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.08),
                  width: 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Used Storage',
                            style: DesignTokens.cardTitleStyle.copyWith(fontSize: 16),
                          ),
                          Text(
                            '${_usedStorage.toStringAsFixed(1)} GB of ${_totalStorage.toStringAsFixed(1)} GB',
                            style: DesignTokens.bodyStyle.copyWith(
                              color: DesignTokens.textSecondary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: DesignTokens.spacingMd),
                      
                      // Storage progress bar
                      Container(
                        height: 8,
                        decoration: BoxDecoration(
                          color: DesignTokens.navigationInactive.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: _usedStorage / _totalStorage,
                          child: Container(
                            decoration: BoxDecoration(
                              color: DesignTokens.primaryInteractiveBlue,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: DesignTokens.spacingMd),
                      
                      // Storage breakdown
                      Row(
                        children: [
                          Expanded(
                            child: _buildStorageItem('Conversations', '1.2 GB', Icons.chat_bubble_outline),
                          ),
                          Expanded(
                            child: _buildStorageItem('Notes', '0.8 GB', Icons.note_outlined),
                          ),
                          Expanded(
                            child: _buildStorageItem('Tasks', '0.3 GB', Icons.task_outlined),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCloudSyncSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Cloud Sync',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Master sync toggle
            _buildToggleItem(
              'Cross-Device Synchronization',
              'Sync your data across all your devices',
              _crossDeviceSync,
              (value) {
                setState(() {
                  _crossDeviceSync = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
            
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Sync frequency dropdown
            _buildDropdownField(
              'Sync Frequency',
              'How often to sync your data',
              _syncFrequency,
              ['Real-time', 'Hourly', 'Daily'],
              (value) {
                setState(() {
                  _syncFrequency = value!;
                });
              },
            ),
            
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Selective sync options
            Text(
              'Selective Sync',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 16),
            ),
            const SizedBox(height: DesignTokens.spacingSm),
            
            _buildToggleItem(
              'Conversations',
              'Sync therapy and chat conversations',
              _syncConversations,
              (value) {
                setState(() {
                  _syncConversations = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
            
            const SizedBox(height: DesignTokens.spacingSm),
            
            _buildToggleItem(
              'Notes',
              'Sync all your notes and documents',
              _syncNotes,
              (value) {
                setState(() {
                  _syncNotes = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
            
            const SizedBox(height: DesignTokens.spacingSm),
            
            _buildToggleItem(
              'Tasks',
              'Sync tasks and productivity data',
              _syncTasks,
              (value) {
                setState(() {
                  _syncTasks = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
            
            const SizedBox(height: DesignTokens.spacingSm),
            
            _buildToggleItem(
              'Settings',
              'Sync app preferences and settings',
              _syncSettings,
              (value) {
                setState(() {
                  _syncSettings = value;
                });
                HapticFeedback.mediumImpact();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Backup & Export',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Manual backup button
            _buildActionButton(
              'Create Manual Backup',
              'Generate a backup of all your data',
              Icons.backup,
              () => _createBackup(),
            ),
            
            const SizedBox(height: DesignTokens.spacingMd),
            
            // Export data button
            _buildActionButton(
              'Export Data',
              'Download your data in a portable format',
              Icons.download,
              () => _exportData(),
            ),
            
            const SizedBox(height: DesignTokens.spacingXl),
          ],
        ),
      ),
    );
  }

  Widget _buildStorageItem(String label, String size, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: DesignTokens.primaryInteractiveBlue,
          size: 24,
        ),
        const SizedBox(height: DesignTokens.spacingXs),
        Text(
          size,
          style: DesignTokens.bodyStyle.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        ),
        Text(
          label,
          style: DesignTokens.bodyStyle.copyWith(
            color: DesignTokens.textSecondary,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildToggleItem(String title, String subtitle, bool value, Function(bool) onChanged) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: const EdgeInsets.all(DesignTokens.spacingSm),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: DesignTokens.cardTitleStyle.copyWith(fontSize: 16),
                      ),
                      const SizedBox(height: DesignTokens.spacingXs),
                      Text(
                        subtitle,
                        style: DesignTokens.bodyStyle.copyWith(
                          color: DesignTokens.textSecondary,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: value,
                  onChanged: onChanged,
                  activeColor: DesignTokens.primaryInteractiveBlue,
                  inactiveTrackColor: DesignTokens.navigationInactive,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownField(String title, String subtitle, String value, List<String> options, Function(String?) onChanged) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: DesignTokens.cardTitleStyle.copyWith(fontSize: 16),
              ),
              const SizedBox(height: DesignTokens.spacingXs),
              Text(
                subtitle,
                style: DesignTokens.bodyStyle.copyWith(
                  color: DesignTokens.textSecondary,
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: DesignTokens.spacingMd),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
                decoration: BoxDecoration(
                  color: DesignTokens.backgroundAccentCard,
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: value,
                    isExpanded: true,
                    icon: const Icon(Icons.keyboard_arrow_down, color: DesignTokens.iconPrimary),
                    style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
                    dropdownColor: DesignTokens.backgroundCard,
                    onChanged: onChanged,
                    items: options.map((String option) {
                      return DropdownMenuItem<String>(
                        value: option,
                        child: Text(option),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(String title, String subtitle, IconData icon, VoidCallback onTap) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: Container(
          padding: const EdgeInsets.all(DesignTokens.spacingMd),
          decoration: BoxDecoration(
            color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.08),
              width: 1,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                    ),
                    child: Icon(
                      icon,
                      color: DesignTokens.primaryInteractiveBlue,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: DesignTokens.spacingMd),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: DesignTokens.cardTitleStyle.copyWith(fontSize: 16),
                        ),
                        const SizedBox(height: DesignTokens.spacingXs),
                        Text(
                          subtitle,
                          style: DesignTokens.bodyStyle.copyWith(
                            color: DesignTokens.textSecondary,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(
                    Icons.chevron_right,
                    color: DesignTokens.iconSecondary,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _createBackup() {
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Creating backup...'),
        backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _exportData() {
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Preparing data export...'),
        backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  void dispose() {
    _pageAnimationController.dispose();
    super.dispose();
  }
}
