Below is a detailed breakdown of its core functionalities:

I. Home Screen (Left Screen)

The Home Screen serves as the user's central hub, offering a quick overview and personalized interactions.

Dynamic Greeting: The "Good morning, [User Name]" greeting is dynamic. Upon the first login of the day, it displays a time-appropriate greeting (Good morning/afternoon/evening). For subsequent check-ins, it provides a more casual message, such as "What's up, [User Name]?"

Profile Picture Integration: The profile picture displayed at the top of the home screen is directly linked to the profile settings. Any changes made to the image in the profile settings will automatically reflect on both the profile screen and the home screen.

Quick Look Card:

This is the primary card on the home screen, providing an overview of the user's day.

It displays the current date and a message like "Here's what's on your plate today."

Content includes a summary of tasks, notes, and events scheduled for that day.

A "Tap to more" button allows the user to expand this into a scrollable pop-up card, revealing all daily details.

General Task Widget: All events and tasks should also reflect within a general task widget, which is positioned below the dynamic greeting on the home screen. (Consider a concise name for this widget, e.g., "Daily Planner" or "My Day At a Glance").

II. Notification Engine

The app will feature a robust notification engine to keep users informed and engaged.

Notification Types:

Reminders: For upcoming tasks, events, and therapy sessions.

Daily Check-ins: Prompts for mood tracking or reflections.

Weekly Reports: Notification when personal progress summaries are ready.

System Updates: Announcements for new features or app maintenance.

Contextual AI Prompts: Based on user activity (e.g., "You haven't added a note in a while," or "Time to review your progress").

Display: Notifications should be displayed prominently, potentially as banners, in-app alerts, or on a dedicated notification center.

Interaction: Tapping a notification should navigate the user directly to the relevant section or item within the app (e.g., tapping a task reminder opens the task details).

III. Calendar Screen

The Calendar Screen provides a comprehensive view of the user's schedule, emphasizing seamless interaction between the calendar and daily schedule.

Today's Schedule Widget: This widget dynamically displays all events and meetings scheduled for the selected day, drawing data directly from the user's calendar.

Calendar Widget Synchronization: The "Today's Schedule" widget is fully synced with the main calendar widget.

Tapping on a specific date within the calendar widget will update the "Today's Schedule" widget to display events and tasks for that chosen date.

Tapping outside the selected date area or refreshing the screen will revert the schedule view back to the current day.

IV. Add an Event

This functionality allows users to easily schedule new events.

Event Input: Users can fill in event details.

Error Handling: Robust error handling ensures data integrity during input.

Save Functionality: Upon pressing "Save Event," the event is stored and synced with the user's calendar.

Data Synchronization: Events will include parameters (e.g., date, time, title, description, participants, location) that are saved and synced across the app's features (e.g., Quick Look card, Calendar).

V. Add Contact

This feature enables users to save new contacts efficiently, with a focus on local saving and quick actions.

Information Fields: Users can save various details about a person.

"Digits Only" Field with Link Icon:

A dedicated field for phone numbers ("Digits Only") includes a "link" button beside it.

If a user types a number in this field and taps the link icon, the app will utilize the .vcf format to save the person's number locally to the phone's contacts.

Naming Convention: To facilitate context, the app will append "where did our parts cross today" as the contact's last name when saving locally.

Success Message: A confirmation message will be displayed upon successful contact saving.

"Save Contacts" Button: Tapping this button will navigate the user to the "View Contacts" repository, where a list of all saved contacts is accessible.

VI. View Contacts

The View Contacts screen provides an organized and interactive list of saved contacts.

Scrollable List: Contacts are displayed in a scrollable list format.

Substring Search: A search bar allows users to find contacts using substring search.

Minimized View: In the minimized list view, each contact entry displays:

The person's name.

A brief note on where they were met (e.g., the "where did our parts cross today" information).

A default image for the person (if no custom image is set).

Detailed View (Tap Interaction): Tapping on a contact expands their entry to show more details, including social media links and other stored information.

Social Media "Find" Button:

Beside each social media link in the detailed view, there will be a "find" icon (a circular button).

Tapping this icon will automatically open the corresponding social media app on the user's phone and pre-fill the search bar with the contact's name, facilitating quick social media lookup.

VII. Smart Capture

Smart Capture is designed to intelligently process and organize web content.

Link Processing: Users can paste a link into a designated input box. The app will process the link in the background.

AI-Powered Summarization: Using AI, Drix will read through the linked content and generate a concise summary to help the user quickly understand its essence.

Categorization: Users have the ability to categorize the captured content.

Add to Notes Integration: A "Add to Notes" option allows users to sync the captured content with the Notes feature. Users can select a specific tab/tag within Notes and also edit associated tags.

"Chat with Drix" Integration: Tapping the "Chat with Drix" button will open a new chat session with the captured information preloaded, enabling the user to discuss the content with Drix.

Search Feature: Smart Capture also includes a search functionality to easily find previously captured content.

VIII. Profile & Settings

The Profile section allows users to manage their account, personal information, and app preferences.

Profile Image: Syncs with the home screen profile image, allowing changes to reflect across both.

Member Since: Tracks and displays the date the user signed up for Drix.

Current Streak: Displays the number of consecutive days the user has completed tasks. This metric is synced with the user's task completion data.

Total Sessions: Tracks and displays the total number of therapy sessions the user has had.

Usage Statistics: Provides insights into app usage, including:

Number of therapy sessions completed.

Number of notes created.

Number of tasks completed this month.

Subscription Management:

Displays the current plan (Essential/Unlimited).

Provides an option to upgrade to the Unlimited plan, with more information available upon tapping.

Detailed Profile Settings Outline:

The profile section is structured with dedicated screens for various settings:

A. Account Settings (account_settings_screen.dart)

Personal Information:

Profile Photo: Button/Image Picker to change the profile picture.

Name: Text field for the user's display name.

What should Drix call you?: Text field for the name the AI assistant uses.

Time Zone: Dropdown with a comprehensive list of UTC offsets (e.g., 'UTC-5 (Eastern Time)').

Authentication & Security:

Change Password: Button to navigate to a password change flow.

Two-Factor Authentication: Toggle to enable/disable.

Biometric Login: Toggle for Face ID/Touch ID sign-in.

Subscription & Usage:

Subscription Plan: PageView with interactive cards for 'Essential' and 'Unlimited' plans.

Upgrade to Unlimited: Button to initiate the upgrade process.

Account Management:

Sign Out: Button with confirmation dialog.

Delete Account: Button with confirmation dialog for permanent account deletion.

B. Privacy & Security (privacy_security_screen.dart)

Data Privacy:

Therapy Session Encryption: Toggle for end-to-end encryption of therapy conversations.

Anonymous Usage Analytics: Toggle to opt-in/out of sharing anonymous data.

Content Control:

Auto-Delete Old Conversations: Toggle to automatically delete conversations older than 30 days.

Security Features:

Session Timeout: Dropdown options ('15 minutes', '30 minutes', '1 hour', '2 hours', 'Never') for automatic sign-out after inactivity.

App Lock Requirements: Toggle to require biometric/passcode authentication to open the app.

C. Notifications (notifications_screen.dart)

Notification Types: Toggles for various notification categories:

Therapy Reminders

Task Notifications

Daily Check-ins

Weekly Reports

System Updates

Quiet Hours:

Do Not Disturb: Two Time Pickers (Start and End) to set a period without notifications.

Notification Frequency: Dropdown options ('Low', 'Normal', 'High') to control the rate of non-critical notifications.

D. Data & Storage (data_storage_screen.dart)

Cloud Sync:

Cross-Device Synchronization: Master toggle to enable/disable data sync.

Sync Frequency: Dropdown options ('Real-time', 'Hourly', 'Daily').

Selective Sync: Toggles to sync specific data types:

Conversations

Notes

Tasks

Settings

Data Management:

Manage Storage: Button to clear storage space.

Export Data: Button to export data in a portable format.

Import Data: Button to import data from a backup file.

Clear Cache: Button to clear locally cached data.

IX. Voice Screen

The Voice Screen facilitates conversational interaction with Drix using speech.

Mic Activation: Users tap the microphone icon to activate voice input.

Conversational States: The interface dynamically changes to indicate different states:

User Speaking: When the user is actively talking.

Drix Responding: When Drix is generating a response.

Voice Activity Detection (VAD): Utilizes VAD (e.g., from LiveKids) to accurately detect when the user is speaking and pausing, enabling natural turn-taking in the conversation.

Contextual Understanding & Memory: Conversations with Drix are stored. The system is designed to build a contextual memory, allowing Drix to understand and parse information in real-time for coherent and relevant responses in ongoing discussions.

X. Chat Screen

The Chat Screen is the primary interface for text-based communication with Drix, offering various modes and rich interactions.

Chat Modes:

A side menu allows users to select different modes (e.g., Personal, Learn, Lessons), which alter Drix's behavior and conversational style for that specific chat.

Custom Modes (Future): Future functionality will enable users to create and define their own custom modes with specific instructions for Drix.

Chat History:

Similar to popular AI chat UIs (e.g., ChatGPT), users can view a scrollable list of all past chat sessions in a history sidebar.

Automatic Chat Naming: Drix automatically summarizes the first message of each chat to generate a concise, word-limited header/name for that conversation, used for storage and display in the history.

Recency Sorting: The most recent chats appear at the top of the history list.

Mode Tagging in History (Suggestion): To indicate which mode was used for a particular chat, consider adding a small icon or text tag next to the chat name in the history list (e.g., "[Personal] Chat Summary," or a distinct color/shape associated with each mode).

Streamable Responses: Drix's responses are streamed to the user with a distinct animation pattern for a dynamic and engaging experience.

Copy Functionality: Users can easily copy Drix's responses.

Web Function:

A "Web Function" option allows Drix to access web services.

This will involve Drix AI connecting to a web service via an MCP (MODEL CONTEXT PROTOCOL) or API service.

Voice Option: Tapping the voice option seamlessly transitions the user to the Voice Screen for spoken interaction.

Image Picker (Vision Capability):

An image picker allows users to upload images to the chat.

In the background, this action switches the AI model to one with vision capabilities, enabling users to ask questions or discuss the uploaded image with Drix.

Send Message: Pressing the "Enter" button sends the user's message to Drix.

New Chat Button: A button in the top right allows users to start a fresh, new conversation with Drix.

XI. Therapy Mode (Voice)

The Voice component of Therapy Mode focuses on emotional well-being and progress tracking.

"Thought for You" (Quotes): On the mind-guarding screen, a widget displays interesting quotes that users can refresh (front-end templating).

Daily Reflections: A new reflection prompt appears daily for user engagement.

Days of Growth Widget:

This widget tracks and displays the user's progress in therapy or personal growth.

Logic Consideration: The user is seeking advice on how to best define "continuous sessions" or "days of growth."

Suggestion: Define "continuous" based on a configurable time frame (e.g., a session within the last 7 days constitutes continuity). Handle edge cases where sessions are infrequent by displaying "No recent sessions" or providing encouraging prompts.

"How are you feeling today?" (Mood Engine Integration): Tapping this prompt directs the user to the Mood Engine, where they can log and track their mood.

"You are growing beautifully" Widget: (User is uncertain about this widget's placement or necessity; review its integration, perhaps linked to "Days of Growth" or mood tracking).

Begin Session Button: A clear call-to-action to start a therapy session.



XV. Therapy Analytics Screen (New)

The Therapy Analytics screen provides users with visual insights into their emotional well-being and therapy progress.

Emotional Trends Circle Chart:

A prominent circle chart visualizes the user's top emotions.

The size of each circle is dynamically scaled to indicate the higher presence or frequency of that emotion in recent sessions.

A pre-designed color guide will be used to differentiate between positive and negative emotions.

Timeframe Toggle: Users can switch the data displayed on the chart using toggles for "This Week," "This Month," and "Three Months." The system is designed to allow for the addition of more timeframe options in the future.

Drix Session Summary:

Below the chart, Drix provides a concise summary of the user's last five therapy sessions. This summary offers a general overview of key themes, progress, or patterns identified by Drix during those sessions.

"Chat with Drix about this" Button: A dedicated button allows users to initiate a chat session with Drix, pre-loading the context of the last five sessions' summary for further discussion and deeper insights.

Settings Button: Located in the top right corner, a settings button will directly link the user to the dedicated "Therapy Mode Settings" within the main Profile section for easy configuration.

Export Progress:

This feature allows users to export their therapy-related data.

Clarity on Exported Data: When a user taps "Export Progress," the system will compile and provide downloadable access to a comprehensive record of their therapy journey. This typically includes:

Emotional Data: Raw data points and frequencies of emotions tracked (as displayed in the circle chart).

Session Summaries: All generated Drix summaries of past therapy sessions.

Therapy Chat Transcripts: Transcripts of all conversations within the dedicated "Therapy Mode Chat" history.

Mood Tracking Data: If integrated, data from daily mood check-ins.

Growth Metrics: Any quantifiable progress metrics (e.g., "Days of Growth" or task completion related to therapy goals).

The data would typically be exported in a user-friendly, portable format such as CSV, JSON, or a PDF summary report, allowing users to keep a personal record or share it with a healthcare professional if they choose.

XII. Therapy Mode (Chat)

The Chat component of Therapy Mode provides a private and context-aware conversational space for therapy progress.

Contextual Access to Therapy Data: When chatting about progress, Drix can access all relevant information from past therapy sessions (both voice and text-based) including:

Dates of sessions.

Key topics discussed by the user.

Drix's responses and insights.

Identified takeaways or action items.

Overall session summaries.

Bidirectional Conversation: Users can engage in natural, back-and-forth dialogue with Drix about their therapy journey.

Distinct Therapy Chat History: The therapy chat will have its own dedicated history, separate from general Drix conversations, ensuring privacy and focused content related only to therapy sessions.

XIII. Notes Section

The Notes section provides robust tools for capturing and organizing information, inspired by Google Keep.

Add Notes: Users can easily create new notes.

Tagging: Notes can be tagged for organization. A side menu displays a list of available tags.

Substring Search: A search feature allows users to find notes using substring search.

Export Notes: Users have the ability to export their notes.

Pin Notes: Users can "pin" important notes to the top of the list for quick access, mirroring the functionality found in Google Keep.

XIV. Tasks Section

The Tasks section helps users manage their daily productivity with clear categorization and a dynamic calendar view.

Tabbed View: Tasks are organized into three distinct tabs with horizontal scrolling:

Today: Lists all tasks due for the current day.

Upcoming: Displays tasks scheduled for future dates.

Past Tasks: Shows tasks that have been completed or are overdue from previous days.

Calendar Widget Integration: A calendar widget positioned at the top allows users to:

Select any date to view tasks scheduled for that specific day. The main task view will dynamically update to show the selected day's tasks.

Returning to today's date (by tapping, navigating out, or refreshing the screen) will reset the view back to the "Today" tab.

Task Categorization & Priority: Each task can be assigned:

Tags: For additional organization.

Categories: For broader grouping.

Priority: To indicate urgency (e.g., High, Medium, Low).

Add Tasks: Users can add new tasks, including input fields for description, category, and priority.