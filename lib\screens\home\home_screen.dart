import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'package:darvis_app/services/navigation_service.dart';
import 'package:darvis_app/widgets/quick_action_overlay.dart';
import 'package:darvis_app/blocs/dashboard/dashboard_bloc.dart';
import 'package:darvis_app/blocs/dashboard/dashboard_event.dart';
import 'package:darvis_app/blocs/dashboard/dashboard_state.dart';
import 'package:darvis_app/models/dashboard_models.dart';
import 'package:darvis_app/widgets/profile_picture_widget.dart';

// Reusable asset path constants
const String _kUserProfilePic = 'assets/images/profile_pic.png';
const String _kGreetingIcon = 'assets/icons/morning.svg';
const String _kNotificationIcon = 'assets/icons/notification.svg';
const String _kDarvisCardLogo = 'assets/images/darvisintelligence.png';

/// HomeScreen: The central hub of the application after login.
/// Displays user greeting, a summary card, mode selection, and main navigation.
class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<DashboardBloc>()..add(const LoadDashboard()),
      child: const _HomeScreenContent(),
    );
  }
}

class _HomeScreenContent extends StatefulWidget {
  const _HomeScreenContent();

  @override
  State<_HomeScreenContent> createState() => _HomeScreenContentState();
}

class _HomeScreenContentState extends State<_HomeScreenContent> {
  OverlayEntry? _overlayEntry;
  bool _showingOverlay = false;

  @override
  Widget build(BuildContext context) {
    // A GlobalKey is needed to open the endDrawer programmatically.
    final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
    final navigationService = GetIt.instance<NavigationService>();

    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        return _buildHomeScreen(context, scaffoldKey, navigationService, state);
      },
    );
  }

  Widget _buildHomeScreen(
    BuildContext context,
    GlobalKey<ScaffoldState> scaffoldKey,
    NavigationService navigationService,
    DashboardState state,
  ) {

    // Handle different dashboard states
    if (state is DashboardLoading) {
      return _buildLoadingScreen(scaffoldKey, navigationService);
    } else if (state is DashboardError) {
      return _buildErrorScreen(scaffoldKey, navigationService, state.message);
    } else if (state is DashboardLoaded || state is DashboardRefreshing) {
      final data = state is DashboardLoaded
          ? state.data
          : (state as DashboardRefreshing).currentData;
      return _buildLoadedScreen(scaffoldKey, navigationService, data, state is DashboardRefreshing);
    } else {
      return _buildLoadingScreen(scaffoldKey, navigationService);
    }
  }

  Widget _buildLoadedScreen(
    GlobalKey<ScaffoldState> scaffoldKey,
    NavigationService navigationService,
    DashboardData data,
    bool isRefreshing,
  ) {
    return Scaffold(
      key: scaffoldKey,
      backgroundColor: DesignTokens.backgroundApp,
      endDrawer: const _NotificationDrawer(),
      // Bottom navigation bar is now provided by the TabScreenWrapper
      body: RefreshIndicator(
        onRefresh: () async {
          context.read<DashboardBloc>().add(const RefreshDashboard());
        },
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Column(
                children: [
                  const SizedBox(height: 20), // Top padding
                  _TopAppBar(
                    greeting: data.greeting,
                    greetingIconPath: _kGreetingIcon,
                    onNotificationsTapped: () {
                      scaffoldKey.currentState?.openEndDrawer();
                    },
                    onProfileTapped: () {
                      navigationService.navigateToTab(4); // Navigate to profile tab
                    },
                  ),
                ],
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: DesignTokens.spacingLg),
                child: Column(
                  children: [
                    const SizedBox(height: DesignTokens.spacingLg),
                    _TodaysPlateCard(
                      date: _formatDate(DateTime.now()),
                      tasks: data.taskSummary.todayTasks.map((task) => task.title).toList(),
                      dashboardData: data,
                      onTap: () {
                        navigationService.navigateToTab(1); // Navigate to calendar
                      },
                    ),
                    const SizedBox(height: DesignTokens.spacingXl),
                    _ModeSelectionGrid(
                      onLearningHubTapped: () {
                        navigationService.navigateToChat();
                      },
                      onPersonalSpaceTapped: () {
                        navigationService.navigateToPersonalSpace();
                      },
                      onProductivityTapped: _showQuickActionOverlay,
                      onUpcomingEventsTapped: () {
                        navigationService.navigateToUpcomingEvents();
                      },
                    ),
                    const SizedBox(
                        height: DesignTokens
                            .spacingXxl), // Space before bottom nav bar
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingScreen(
    GlobalKey<ScaffoldState> scaffoldKey,
    NavigationService navigationService,
  ) {
    return Scaffold(
      key: scaffoldKey,
      backgroundColor: DesignTokens.backgroundApp,
      endDrawer: const _NotificationDrawer(),
      body: const Center(
        child: CircularProgressIndicator(
          color: DesignTokens.primaryInteractiveBlue,
        ),
      ),
    );
  }

  Widget _buildErrorScreen(
    GlobalKey<ScaffoldState> scaffoldKey,
    NavigationService navigationService,
    String errorMessage,
  ) {
    return Scaffold(
      key: scaffoldKey,
      backgroundColor: DesignTokens.backgroundApp,
      endDrawer: const _NotificationDrawer(),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: DesignTokens.priorityHigh,
            ),
            const SizedBox(height: DesignTokens.spacingLg),
            Text(
              'Oops! Something went wrong',
              style: DesignTokens.cardTitleStyle,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: DesignTokens.spacingSm),
            Text(
              errorMessage,
              style: DesignTokens.bodyStyle,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: DesignTokens.spacingXl),
            ElevatedButton(
              onPressed: () {
                context.read<DashboardBloc>().add(const LoadDashboard());
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: DesignTokens.primaryInteractiveBlue,
                foregroundColor: Colors.white,
              ),
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    final months = ['January', 'February', 'March', 'April', 'May', 'June',
                   'July', 'August', 'September', 'October', 'November', 'December'];

    final weekday = weekdays[date.weekday - 1];
    final day = date.day;
    final month = months[date.month - 1];

    String suffix = 'th';
    if (day % 10 == 1 && day != 11) suffix = 'st';
    else if (day % 10 == 2 && day != 12) suffix = 'nd';
    else if (day % 10 == 3 && day != 13) suffix = 'rd';

    return '$weekday, $day$suffix $month';
  }

  void _showQuickActionOverlay() {
    if (_showingOverlay) return;
    
    setState(() {
      _showingOverlay = true;
    });
    
    _overlayEntry = OverlayEntry(
      builder: (context) => QuickActionOverlay(
        onAddNote: () {
          _dismissOverlay();
          GetIt.instance<NavigationService>().navigateToNotes();
        },
        onAddTask: () {
          _dismissOverlay();
          GetIt.instance<NavigationService>().navigateToTasks();
        },
        onDismiss: _dismissOverlay,
      ),
    );
    
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _dismissOverlay() {
    if (!_showingOverlay) return;
    
    _overlayEntry?.remove();
    _overlayEntry = null;
    
    setState(() {
      _showingOverlay = false;
    });
  }

  @override
  void dispose() {
    _dismissOverlay();
    super.dispose();
  }
}

// --- Top App Bar and its components ---

class _TopAppBar extends StatelessWidget {
  final String greeting;
  final String greetingIconPath;
  final VoidCallback onNotificationsTapped;
  final VoidCallback? onProfileTapped;

  const _TopAppBar({
    required this.greeting,
    required this.greetingIconPath,
    required this.onNotificationsTapped,
    this.onProfileTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(
        DesignTokens.spacingLg,
        DesignTokens.spacingMd,
        DesignTokens.spacingLg,
        DesignTokens.spacingMd,
      ),
      child: Row(
        children: [
          ProfilePictureWidget(
            radius: 24,
            onTap: onProfileTapped,
            fallbackAsset: _kUserProfilePic,
          ),
          const SizedBox(width: DesignTokens.spacingMd),
          Expanded(
            child: Row(
              children: [
                Text(
                  greeting,
                  style: DesignTokens.greetingTextStyle.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 4), // Reduce spacing here
                SvgPicture.asset(greetingIconPath, height: 24, width: 24),
              ],
            ),
          ),
          const SizedBox(width: DesignTokens.spacingXxl),
          IconButton(
            icon: SvgPicture.asset(_kNotificationIcon, height: 22, width: 22),
            onPressed: onNotificationsTapped,
          ),
        ],
      ),
    );
  }
}

// --- "Today's Plate" Card and its components ---

class _TodaysPlateCard extends StatelessWidget {
  final String date;
  final List<String> tasks;
  final DashboardData dashboardData;
  final VoidCallback onTap;

  const _TodaysPlateCard({
    required this.date,
    required this.tasks,
    required this.dashboardData,
    required this.onTap,
  });

  void _showExpandedView(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _ExpandedDayView(
        date: date,
        dashboardData: dashboardData,
        onNavigateToCalendar: onTap,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showExpandedView(context),
      child: Container(
        padding: const EdgeInsets.all(DesignTokens.spacingLg),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          gradient: DesignTokens.todayCardBackgroundGradient,
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(date, style: DesignTokens.cardLargeHeadingStyle),
                  const SizedBox(height: DesignTokens.spacingXs),
                  Text('Here\'s what\'s on your plate today',
                      style: DesignTokens.cardSubheadingStyle),
                  const SizedBox(height: DesignTokens.spacingMd),
                  ...tasks.map((task) => Padding(
                        padding: const EdgeInsets.only(
                            bottom: DesignTokens.spacingXs),
                        child: Text('• $task',
                            style: DesignTokens.cardListItemStyle),
                      )),
                ],
              ),
            ),
            const SizedBox(width: DesignTokens.spacingMd),
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const _AnimatedCardLogo(),
                const SizedBox(height: DesignTokens.spacingLg),
                Text('Tap for More', style: DesignTokens.linkTextStyle),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _AnimatedCardLogo extends StatefulWidget {
  const _AnimatedCardLogo();

  @override
  State<_AnimatedCardLogo> createState() => _AnimatedCardLogoState();
}

class _AnimatedCardLogoState extends State<_AnimatedCardLogo>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<Offset> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 5), // Slower animation as requested
      vsync: this,
    )..repeat(reverse: true);
    _animation = Tween<Offset>(
      begin: const Offset(0, -0.02),
      end: const Offset(0, 0.02),
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _animation,
      child: Image.asset(_kDarvisCardLogo, height: 80),
    );
  }
}

// --- Mode Selection Grid ---

class _ModeSelectionGrid extends StatelessWidget {
  final VoidCallback onLearningHubTapped;
  final VoidCallback onPersonalSpaceTapped;
  final VoidCallback onProductivityTapped;
  final VoidCallback onUpcomingEventsTapped;

  const _ModeSelectionGrid({
    required this.onLearningHubTapped,
    required this.onPersonalSpaceTapped,
    required this.onProductivityTapped,
    required this.onUpcomingEventsTapped,
  });

  @override
  Widget build(BuildContext context) {
    // Using a GridView for the 2x2 layout.
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: DesignTokens.spacingMd,
      mainAxisSpacing: DesignTokens.spacingMd,
      childAspectRatio: 1.0, // Makes the cards square
      children: [
        _ModeCard(
          title: 'Chat with Drix',
          color: DesignTokens.primaryDeepBlue,
          onTap: onLearningHubTapped,
        ),
        _ModeCard(
          title: 'Personal Space',
          color: DesignTokens.primaryAccentBlue,
          onTap: onPersonalSpaceTapped,
        ),
        _ModeCard(
          title: 'Productivity',
          color: DesignTokens.primaryAccentBlue,
          onTap: onProductivityTapped,
        ),
        _ModeCard(
          title: 'Upcoming Events',
          color: DesignTokens.primaryDeepBlue,
          onTap: onUpcomingEventsTapped,
        ),
      ],
    );
  }
}

class _ModeCard extends StatelessWidget {
  final String title;
  final Color color;
  final VoidCallback onTap;

  const _ModeCard(
      {required this.title, required this.color, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          // Note: Inner shadows are complex. A simple border or gradient can be used
          // instead to avoid performance issues. Ditching the effect as requested.
        ),
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        alignment: Alignment.center,
        child: Text(title,
            style: DesignTokens.cardTitleStyle, textAlign: TextAlign.center),
      ),
    );
  }
}



// --- Notification Drawer ---

class _NotificationDrawer extends StatelessWidget {
  const _NotificationDrawer();

  @override
  Widget build(BuildContext context) {
    return Drawer(
      width: MediaQuery.of(context).size.width *
          0.6, // Using a bit more than 40% for better content fit
      backgroundColor: DesignTokens.backgroundCard,
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          const SizedBox(
            height: 120,
            child: DrawerHeader(
              decoration: BoxDecoration(color: DesignTokens.primaryDeepBlue),
              child: Text('Notifications', style: DesignTokens.cardTitleStyle),
            ),
          ),
          ListTile(
            leading: const Icon(Icons.info_outline,
                color: DesignTokens.iconSecondary),
            title:
                const Text('Welcome to Drix!', style: DesignTokens.bodyStyle),
            subtitle: const Text('Your setup is complete.',
                style: DesignTokens.navBarLabelStyle),
            onTap: () {/* Handle tap */},
          ),
          const Divider(
              color: DesignTokens.borderInteractiveElement, height: 1),
          ListTile(
            leading:
                const Icon(Icons.task_alt, color: DesignTokens.iconSecondary),
            title:
                const Text('New Task Assigned', style: DesignTokens.bodyStyle),
            subtitle: const Text('Review the project brief.',
                style: DesignTokens.navBarLabelStyle),
            onTap: () {/* Handle tap */},
          ),
          const Divider(
              color: DesignTokens.borderInteractiveElement, height: 1),
          ListTile(
            leading: const Icon(Icons.summarize_outlined,
                color: DesignTokens.iconSecondary),
            title: const Text('Article Summary Ready',
                style: DesignTokens.bodyStyle),
            subtitle: const Text('Your summary for "AI in 2025" is available.',
                style: DesignTokens.navBarLabelStyle),
            onTap: () {/* Handle tap */},
          ),
        ],
      ),
    );
  }
}

// --- Expanded Day View Modal ---

class _ExpandedDayView extends StatelessWidget {
  final String date;
  final DashboardData dashboardData;
  final VoidCallback onNavigateToCalendar;

  const _ExpandedDayView({
    required this.date,
    required this.dashboardData,
    required this.onNavigateToCalendar,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: DesignTokens.backgroundCard,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: DesignTokens.textMuted,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(DesignTokens.spacingLg),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  date,
                  style: DesignTokens.cardTitleStyle.copyWith(fontSize: 20),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close, color: DesignTokens.textSecondary),
                ),
              ],
            ),
          ),

          // Scrollable content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingLg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tasks section
                  _buildSection(
                    'Today\'s Tasks',
                    dashboardData.taskSummary.todayTasks.map((task) => task.title).toList(),
                    Icons.task_alt,
                  ),

                  const SizedBox(height: DesignTokens.spacingLg),

                  // Upcoming events section
                  _buildSection(
                    'Upcoming Events',
                    dashboardData.upcomingEvents.map((event) => event.title).toList(),
                    Icons.event,
                  ),

                  const SizedBox(height: DesignTokens.spacingLg),

                  // Notes section
                  _buildSection(
                    'Recent Notes',
                    ['Note 1', 'Note 2'], // Placeholder - would come from dashboard data
                    Icons.note,
                  ),

                  const SizedBox(height: DesignTokens.spacingXl),

                  // Navigate to calendar button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        onNavigateToCalendar();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: DesignTokens.primaryInteractiveBlue,
                        padding: const EdgeInsets.symmetric(vertical: DesignTokens.spacingMd),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                        ),
                      ),
                      child: const Text(
                        'View Full Calendar',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: DesignTokens.spacingXl),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, List<String> items, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: DesignTokens.primaryInteractiveBlue, size: 20),
            const SizedBox(width: DesignTokens.spacingSm),
            Text(
              title,
              style: DesignTokens.cardSubheadingStyle.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ],
        ),
        const SizedBox(height: DesignTokens.spacingSm),
        if (items.isEmpty)
          Padding(
            padding: const EdgeInsets.only(left: 28),
            child: Text(
              'Nothing scheduled',
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textMuted,
                fontStyle: FontStyle.italic,
              ),
            ),
          )
        else
          ...items.map((item) => Padding(
            padding: const EdgeInsets.only(left: 28, bottom: DesignTokens.spacingXs),
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 4,
                  decoration: const BoxDecoration(
                    color: DesignTokens.primaryInteractiveBlue,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: DesignTokens.spacingSm),
                Expanded(
                  child: Text(
                    item,
                    style: DesignTokens.bodyStyle,
                  ),
                ),
              ],
            ),
          )),
      ],
    );
  }
}
