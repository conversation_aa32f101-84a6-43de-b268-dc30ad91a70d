import 'package:equatable/equatable.dart';
import '../../models/isar_models.dart';
import 'notes_event.dart';

/// Base class for all notes states
abstract class NotesState extends Equatable {
  const NotesState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class NotesInitial extends NotesState {
  const NotesInitial();
}

/// Loading state
class NotesLoading extends NotesState {
  const NotesLoading();
}

/// Notes loaded successfully
class NotesLoaded extends NotesState {
  final List<LocalNote> notes;
  final List<LocalNote> filteredNotes;
  final String? searchQuery;
  final String? selectedTag;
  final NotesSortType sortType;
  final Set<String> selectedNoteIds;
  final List<String> availableTags;
  final bool isSelectionMode;

  const NotesLoaded({
    required this.notes,
    required this.filteredNotes,
    this.searchQuery,
    this.selectedTag,
    this.sortType = NotesSortType.updatedAtDesc,
    this.selectedNoteIds = const {},
    this.availableTags = const [],
    this.isSelectionMode = false,
  });

  @override
  List<Object?> get props => [
        notes,
        filteredNotes,
        searchQuery,
        selectedTag,
        sortType,
        selectedNoteIds,
        availableTags,
        isSelectionMode,
      ];

  NotesLoaded copyWith({
    List<LocalNote>? notes,
    List<LocalNote>? filteredNotes,
    String? searchQuery,
    String? selectedTag,
    NotesSortType? sortType,
    Set<String>? selectedNoteIds,
    List<String>? availableTags,
    bool? isSelectionMode,
    bool clearSearchQuery = false,
    bool clearSelectedTag = false,
  }) {
    return NotesLoaded(
      notes: notes ?? this.notes,
      filteredNotes: filteredNotes ?? this.filteredNotes,
      searchQuery: clearSearchQuery ? null : (searchQuery ?? this.searchQuery),
      selectedTag: clearSelectedTag ? null : (selectedTag ?? this.selectedTag),
      sortType: sortType ?? this.sortType,
      selectedNoteIds: selectedNoteIds ?? this.selectedNoteIds,
      availableTags: availableTags ?? this.availableTags,
      isSelectionMode: isSelectionMode ?? this.isSelectionMode,
    );
  }

  /// Get pinned notes
  List<LocalNote> get pinnedNotes => filteredNotes.where((note) => note.isPinned).toList();

  /// Get unpinned notes
  List<LocalNote> get unpinnedNotes => filteredNotes.where((note) => !note.isPinned).toList();

  /// Get selected notes
  List<LocalNote> get selectedNotes => 
      notes.where((note) => selectedNoteIds.contains(note.serverId ?? note.id.toString())).toList();

  /// Check if any notes are selected
  bool get hasSelectedNotes => selectedNoteIds.isNotEmpty;

  /// Get total notes count
  int get totalNotesCount => notes.length;

  /// Get filtered notes count
  int get filteredNotesCount => filteredNotes.length;

  /// Get pinned notes count
  int get pinnedNotesCount => pinnedNotes.length;

  /// Check if note is selected
  bool isNoteSelected(LocalNote note) {
    final noteId = note.serverId ?? note.id.toString();
    return selectedNoteIds.contains(noteId);
  }
}

/// Notes operation in progress
class NotesOperationInProgress extends NotesState {
  final String operation;
  final String? noteId;

  const NotesOperationInProgress({
    required this.operation,
    this.noteId,
  });

  @override
  List<Object?> get props => [operation, noteId];
}

/// Notes operation completed successfully
class NotesOperationSuccess extends NotesState {
  final String message;
  final String operation;
  final LocalNote? note;

  const NotesOperationSuccess({
    required this.message,
    required this.operation,
    this.note,
  });

  @override
  List<Object?> get props => [message, operation, note];
}

/// Notes operation failed
class NotesOperationError extends NotesState {
  final String error;
  final String operation;
  final String? noteId;

  const NotesOperationError({
    required this.error,
    required this.operation,
    this.noteId,
  });

  @override
  List<Object?> get props => [error, operation, noteId];
}

/// Notes sync in progress
class NotesSyncInProgress extends NotesState {
  const NotesSyncInProgress();
}

/// Notes sync completed
class NotesSyncSuccess extends NotesState {
  final int syncedCount;
  final String message;

  const NotesSyncSuccess({
    required this.syncedCount,
    required this.message,
  });

  @override
  List<Object?> get props => [syncedCount, message];
}

/// Notes sync failed
class NotesSyncError extends NotesState {
  final String error;

  const NotesSyncError(this.error);

  @override
  List<Object?> get props => [error];
}

/// Notes export in progress
class NotesExportInProgress extends NotesState {
  const NotesExportInProgress();
}

/// Notes export completed
class NotesExportSuccess extends NotesState {
  final String filePath;
  final int exportedCount;
  final String format;

  const NotesExportSuccess({
    required this.filePath,
    required this.exportedCount,
    required this.format,
  });

  @override
  List<Object?> get props => [filePath, exportedCount, format];
}

/// Notes export failed
class NotesExportError extends NotesState {
  final String error;

  const NotesExportError(this.error);

  @override
  List<Object?> get props => [error];
}

/// Bulk operation in progress
class NotesBulkOperationInProgress extends NotesState {
  final String operation;
  final int totalCount;

  const NotesBulkOperationInProgress({
    required this.operation,
    required this.totalCount,
  });

  @override
  List<Object?> get props => [operation, totalCount];
}

/// Bulk operation completed
class NotesBulkOperationSuccess extends NotesState {
  final String operation;
  final int processedCount;
  final String message;

  const NotesBulkOperationSuccess({
    required this.operation,
    required this.processedCount,
    required this.message,
  });

  @override
  List<Object?> get props => [operation, processedCount, message];
}

/// Bulk operation failed
class NotesBulkOperationError extends NotesState {
  final String operation;
  final String error;

  const NotesBulkOperationError({
    required this.operation,
    required this.error,
  });

  @override
  List<Object?> get props => [operation, error];
}
