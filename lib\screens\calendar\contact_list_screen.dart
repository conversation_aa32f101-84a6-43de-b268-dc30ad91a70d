import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'package:darvis_app/services/navigation_service.dart';
import 'package:darvis_app/services/data_service.dart';
import 'package:darvis_app/services/social_media_service.dart';
import 'package:darvis_app/services/vcf_service.dart';
import 'package:darvis_app/services/device_contacts_service.dart';
import 'package:darvis_app/screens/calendar/edit_contact_screen.dart';
import 'package:darvis_app/widgets/enhanced_bottom_nav_bar.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io';

class ContactListScreen extends StatefulWidget {
  const ContactListScreen({super.key});

  @override
  State<ContactListScreen> createState() => _ContactListScreenState();
}

class _ContactListScreenState extends State<ContactListScreen> with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  List<Contact> _allContacts = [];
  List<Contact> _filteredContacts = [];
  bool _isLoading = false;
  String _sortBy = 'name'; // 'name', 'date', 'location'
  
  late AnimationController _refreshController;
  
  @override
  void initState() {
    super.initState();
    _refreshController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _loadContacts();
    _searchController.addListener(_filterContacts);
    
    // Listen to data service changes
    DataService().addListener(_onDataChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _refreshController.dispose();
    DataService().removeListener(_onDataChanged);
    super.dispose();
  }

  void _onDataChanged() {
    _loadContacts();
  }

  void _loadContacts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load contacts from backend via DataService
      await DataService().loadContacts(forceRefresh: true);

      if (mounted) {
        setState(() {
          _allContacts = DataService().contacts;
          _isLoading = false;
        });
        _sortContacts();
        _filterContacts();
      }
    } catch (e) {
      debugPrint('Error loading contacts: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load contacts: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _filterContacts() async {
    final query = _searchController.text.trim();

    if (query.isEmpty) {
      setState(() {
        _filteredContacts = List.from(_allContacts);
      });
      return;
    }

    try {
      // Use backend search for better results
      final searchResults = await DataService().searchContacts(query);

      if (mounted) {
        setState(() {
          _filteredContacts = searchResults;
        });
      }
    } catch (e) {
      debugPrint('Search error: $e');
      // Fallback to local search
      setState(() {
        _filteredContacts = _allContacts.where((contact) {
          final queryLower = query.toLowerCase();
          return contact.name.toLowerCase().contains(queryLower) ||
                 contact.location.toLowerCase().contains(queryLower) ||
                 contact.memoryPrompt.toLowerCase().contains(queryLower);
        }).toList();
      });
    }
  }

  void _sortContacts() {
    switch (_sortBy) {
      case 'name':
        _allContacts.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'date':
        _allContacts.sort((a, b) => b.dateAdded.compareTo(a.dateAdded));
        break;
      case 'location':
        _allContacts.sort((a, b) => a.location.compareTo(b.location));
        break;
    }
    _filteredContacts = List.from(_allContacts);
    _filterContacts();
  }

  Future<void> _refreshContacts() async {
    _refreshController.repeat();
    await Future.delayed(const Duration(seconds: 1));
    _loadContacts();
    _refreshController.stop();
  }

  void _showContactDetails(Contact contact) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _ContactDetailsModal(contact: contact),
    );
  }

  void _showSortMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: DesignTokens.backgroundCard,
      builder: (context) => _SortMenuModal(
        currentSort: _sortBy,
        onSortChanged: (sortBy) {
          setState(() {
            _sortBy = sortBy;
          });
          _sortContacts();
          Navigator.pop(context);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Column(
          children: [
            _Header(
              onBackTap: () => GetIt.instance<NavigationService>().navigateBack(),
              onSortTap: _showSortMenu,
            ),
            _SearchBar(controller: _searchController),
            Expanded(
              child: _isLoading
                ? _LoadingState()
                : _filteredContacts.isEmpty
                  ? _EmptyState()
                  : _ContactList(
                      contacts: _filteredContacts,
                      onContactTap: _showContactDetails,
                      onRefresh: _refreshContacts,
                      refreshController: _refreshController,
                    ),
            ),
            EnhancedBottomNavBar(
              selectedIndex: 1,
              onTabSelected: (index) {
                if (index == NavigationService.calendarTab) {
                  // Navigate back to main calendar screen
                  GetIt.instance<NavigationService>().navigateToTab(NavigationService.calendarTab);
                } else {
                  // Handle other tab navigation normally
                  GetIt.instance<NavigationService>().navigateToTab(index);
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}

// Header Component
class _Header extends StatelessWidget {
  final VoidCallback onBackTap;
  final VoidCallback onSortTap;

  const _Header({
    required this.onBackTap,
    required this.onSortTap,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: false,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingSm,
        ),
        child: Column(
          children: [
            const SizedBox(height: DesignTokens.spacingXl), // Increased top spacing for better separation
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: onBackTap,
                  child: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const _GradientTitle(text: 'Contact List'),
                GestureDetector(
                  onTap: onSortTap,
                  child: SvgPicture.asset(
                    'assets/icons/contact_list.svg',
                    width: 24,
                    height: 24,
                    colorFilter: const ColorFilter.mode(
                      Colors.white,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Gradient Title Component
class _GradientTitle extends StatelessWidget {
  final String text;

  const _GradientTitle({required this.text});

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
      child: Text(
        text,
        style: DesignTokens.appNameStyle.copyWith(
          color: Colors.white,
          fontSize: 35,
        ),
      ),
    );
  }
}

// Search Bar Component
class _SearchBar extends StatelessWidget {
  final TextEditingController controller;

  const _SearchBar({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingMd,
        vertical: DesignTokens.spacingSm,
      ),
      child: TextField(
        controller: controller,
        style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
        decoration: InputDecoration(
          filled: true,
          fillColor: DesignTokens.formFieldBackground,
          hintText: 'Search contacts...',
          hintStyle: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textMuted),
          prefixIcon: const Icon(
            Icons.search,
            color: DesignTokens.textMuted,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: DesignTokens.spacingMd,
            vertical: DesignTokens.spacingSm,
          ),
        ),
      ),
    );
  }
}

// Contact List Component
class _ContactList extends StatelessWidget {
  final List<Contact> contacts;
  final Function(Contact) onContactTap;
  final Future<void> Function() onRefresh;
  final AnimationController refreshController;

  const _ContactList({
    required this.contacts,
    required this.onContactTap,
    required this.onRefresh,
    required this.refreshController,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: onRefresh,
      backgroundColor: DesignTokens.backgroundCard,
      color: DesignTokens.primaryInteractiveBlue,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
        itemCount: contacts.length,
        itemBuilder: (context, index) {
          final contact = contacts[index];
          return _ContactEntry(
            contact: contact,
            onTap: () => onContactTap(contact),
          );
        },
      ),
    );
  }
}

// Contact Entry Component
class _ContactEntry extends StatelessWidget {
  final Contact contact;
  final VoidCallback onTap;

  const _ContactEntry({
    required this.contact,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: DesignTokens.spacingSm),
      child: Material(
        color: DesignTokens.backgroundCard,
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          child: Padding(
            padding: const EdgeInsets.all(DesignTokens.spacingMd),
            child: Row(
              children: [
                // Profile Thumbnail
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                    color: DesignTokens.formFieldBackground,
                  ),
                  child: contact.imagePath != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                        child: Image.file(
                          File(contact.imagePath!),
                          fit: BoxFit.cover,
                        ),
                      )
                    : Center(
                        child: SvgPicture.asset(
                          'assets/icons/empty_contact.svg',
                          width: 24,
                          height: 24,
                          colorFilter: const ColorFilter.mode(
                            DesignTokens.textMuted,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                ),

                const SizedBox(width: DesignTokens.spacingMd),

                // Contact Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        contact.name,
                        style: DesignTokens.onboardingBodyStyle.copyWith(
                          color: DesignTokens.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: DesignTokens.spacingXs),
                      Text(
                        contact.location,
                        style: DesignTokens.menuItemTextStyle.copyWith(
                          color: DesignTokens.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),

                // Expand Button
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: DesignTokens.primaryAccentBlue,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: SvgPicture.asset(
                      'assets/icons/expand_icon.svg',
                      width: 16,
                      height: 16,
                      colorFilter: const ColorFilter.mode(
                        DesignTokens.iconPrimary,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Loading State Component
class _LoadingState extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.only(bottom: DesignTokens.spacingSm),
          child: Material(
            color: DesignTokens.backgroundCard,
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            child: Padding(
              padding: const EdgeInsets.all(DesignTokens.spacingMd),
              child: Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: DesignTokens.formFieldBackground,
                      borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                    ),
                  ),
                  const SizedBox(width: DesignTokens.spacingMd),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 16,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: DesignTokens.formFieldBackground,
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        const SizedBox(height: DesignTokens.spacingXs),
                        Container(
                          height: 12,
                          width: 120,
                          decoration: BoxDecoration(
                            color: DesignTokens.formFieldBackground,
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: DesignTokens.formFieldBackground,
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Empty State Component
class _EmptyState extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            'assets/icons/empty_contact.svg',
            width: 80,
            height: 80,
            colorFilter: const ColorFilter.mode(
              DesignTokens.textMuted,
              BlendMode.srcIn,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingLg),
          Text(
            'No contacts saved yet',
            style: DesignTokens.onboardingBodyStyle.copyWith(
              color: DesignTokens.textPrimary,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingSm),
          Text(
            'Add your first contact to get started',
            style: DesignTokens.menuItemTextStyle.copyWith(
              color: DesignTokens.textSecondary,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingLg),
          GestureDetector(
            onTap: () {
              // Navigate to Add Contact screen
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: DesignTokens.spacingLg,
                vertical: DesignTokens.spacingSm,
              ),
              decoration: BoxDecoration(
                color: DesignTokens.primaryInteractiveBlue,
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              ),
              child: const Text(
                'Add Contact',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Contact Details Modal
class _ContactDetailsModal extends StatelessWidget {
  final Contact contact;

  const _ContactDetailsModal({required this.contact});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: DesignTokens.backgroundCard,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(DesignTokens.borderRadiusLg),
          topRight: Radius.circular(DesignTokens.borderRadiusLg),
        ),
      ),
      child: Column(
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(DesignTokens.spacingMd),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Contact Details',
                  style: DesignTokens.cardTitleStyle,
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),

          const Divider(color: DesignTokens.borderInteractiveElement),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(DesignTokens.spacingMd),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Image
                  Center(
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                        color: DesignTokens.formFieldBackground,
                      ),
                      child: contact.imagePath != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                            child: Image.file(
                              File(contact.imagePath!),
                              fit: BoxFit.cover,
                            ),
                          )
                        : Center(
                            child: SvgPicture.asset(
                              'assets/icons/empty_contact.svg',
                              width: 40,
                              height: 40,
                              colorFilter: const ColorFilter.mode(
                                DesignTokens.textMuted,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                    ),
                  ),

                  const SizedBox(height: DesignTokens.spacingLg),

                  // Contact Information
                  _DetailField(label: 'Name', value: contact.name),
                  _DetailField(label: 'Phone', value: contact.phone),
                  _DetailField(label: 'Meeting Location', value: contact.location),
                  _DetailField(label: 'Social Media', value: '${contact.socialPlatform}: ${contact.socialUsername}'),
                  _DetailField(label: 'Notes', value: contact.notes),

                  const SizedBox(height: DesignTokens.spacingLg),

                  // Action Buttons
                  _ContactActionButtons(contact: contact),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Detail Field Component
class _DetailField extends StatelessWidget {
  final String label;
  final String value;

  const _DetailField({
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: DesignTokens.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: DesignTokens.formLabelStyle,
          ),
          const SizedBox(height: DesignTokens.spacingXs),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(DesignTokens.spacingMd),
            decoration: BoxDecoration(
              color: DesignTokens.formFieldBackground,
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            ),
            child: Text(
              value,
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Sort Menu Modal
class _SortMenuModal extends StatelessWidget {
  final String currentSort;
  final Function(String) onSortChanged;

  const _SortMenuModal({
    required this.currentSort,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Sort Contacts',
            style: DesignTokens.cardTitleStyle,
          ),
          const SizedBox(height: DesignTokens.spacingMd),
          _SortOption(
            title: 'Name (A-Z)',
            value: 'name',
            currentSort: currentSort,
            onTap: () => onSortChanged('name'),
          ),
          _SortOption(
            title: 'Date Added (Newest)',
            value: 'date',
            currentSort: currentSort,
            onTap: () => onSortChanged('date'),
          ),
          _SortOption(
            title: 'Meeting Location',
            value: 'location',
            currentSort: currentSort,
            onTap: () => onSortChanged('location'),
          ),
        ],
      ),
    );
  }
}

// Sort Option Component
class _SortOption extends StatelessWidget {
  final String title;
  final String value;
  final String currentSort;
  final VoidCallback onTap;

  const _SortOption({
    required this.title,
    required this.value,
    required this.currentSort,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = currentSort == value;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingSm,
        ),
        margin: const EdgeInsets.only(bottom: DesignTokens.spacingXs),
        decoration: BoxDecoration(
          color: isSelected
            ? DesignTokens.primaryAccentBlue
            : DesignTokens.formFieldBackground,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        ),
        child: Text(
          title,
          style: DesignTokens.bodyStyle.copyWith(
            color: isSelected
              ? DesignTokens.iconPrimary
              : DesignTokens.textPrimary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ),
    );
  }
}

// Contact Action Buttons Component
class _ContactActionButtons extends StatelessWidget {
  final Contact contact;

  const _ContactActionButtons({required this.contact});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Primary Actions Row
        Row(
          children: [
            Expanded(
              child: _ActionButton(
                icon: Icons.phone,
                label: 'Call',
                color: Colors.green,
                onTap: () => _makePhoneCall(contact.phone),
              ),
            ),
            const SizedBox(width: DesignTokens.spacingSm),
            Expanded(
              child: _ActionButton(
                icon: Icons.message,
                label: 'SMS',
                color: Colors.blue,
                onTap: () => _sendSMS(contact.phone),
              ),
            ),
            const SizedBox(width: DesignTokens.spacingSm),
            Expanded(
              child: _ActionButton(
                icon: Icons.share,
                label: 'Share',
                color: DesignTokens.primaryInteractiveBlue,
                onTap: () => _shareContact(context, contact),
              ),
            ),
          ],
        ),

        const SizedBox(height: DesignTokens.spacingSm),

        // Secondary Actions Row
        Row(
          children: [
            Expanded(
              child: _ActionButton(
                icon: Icons.edit,
                label: 'Edit',
                color: Colors.orange,
                onTap: () => _editContact(context, contact),
              ),
            ),
            const SizedBox(width: DesignTokens.spacingSm),
            Expanded(
              child: _ActionButton(
                icon: Icons.open_in_new,
                label: 'Social',
                color: Colors.purple,
                onTap: () => _openSocialMedia(contact),
              ),
            ),
            const SizedBox(width: DesignTokens.spacingSm),
            Expanded(
              child: _ActionButton(
                icon: Icons.download,
                label: 'Save',
                color: Colors.teal,
                onTap: () => _saveToDevice(context, contact),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _makePhoneCall(String phoneNumber) async {
    final uri = Uri.parse('tel:$phoneNumber');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _sendSMS(String phoneNumber) async {
    final uri = Uri.parse('sms:$phoneNumber');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _shareContact(BuildContext context, Contact contact) async {
    try {
      final vcfService = VcfService();
      await vcfService.shareVcfFile(contact);
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing contact: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _editContact(BuildContext context, Contact contact) async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => EditContactScreen(contact: contact),
      ),
    );

    if (result == true && context.mounted) {
      // Close the modal and refresh the contact list
      Navigator.of(context).pop();
    }
  }

  void _openSocialMedia(Contact contact) async {
    if (contact.socialPlatform.isNotEmpty && contact.socialUsername.isNotEmpty) {
      final socialMediaService = SocialMediaService();
      await socialMediaService.openProfile(
        contact.socialPlatform.toLowerCase(),
        contact.socialUsername,
      );
    }
  }

  void _saveToDevice(BuildContext context, Contact contact) async {
    try {
      final deviceContactsService = DeviceContactsService();
      final success = await deviceContactsService.saveContactToDevice(contact);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                ? 'Contact saved to device!'
                : 'Failed to save contact. Check permissions.',
            ),
            backgroundColor: success ? Colors.green : Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving contact: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

// Action Button Component
class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;

  const _ActionButton({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: DesignTokens.spacingSm,
          horizontal: DesignTokens.spacingXs,
        ),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: color,
              size: 20,
            ),
            const SizedBox(height: DesignTokens.spacingXs),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
