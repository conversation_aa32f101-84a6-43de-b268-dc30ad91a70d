import 'package:flutter_bloc/flutter_bloc.dart';
import '../../services/api_service.dart';
import '../../services/greeting_service.dart';
import '../../models/dashboard_models.dart';
import 'dashboard_event.dart';
import 'dashboard_state.dart';

/// Dashboard BLoC following the AuthBloc pattern exactly
class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final ApiService _apiService;
  final GreetingService _greetingService;

  DashboardBloc({
    required ApiService apiService,
    required GreetingService greetingService,
  })  : _apiService = apiService,
        _greetingService = greetingService,
        super(const DashboardInitial()) {
    
    // Register event handlers
    on<LoadDashboard>(_onLoadDashboard);
    on<RefreshDashboard>(_onRefreshDashboard);
    on<UpdateGreeting>(_onUpdateGreeting);
  }

  /// Handle load dashboard event
  Future<void> _onLoadDashboard(
    LoadDashboard event,
    Emitter<DashboardState> emit,
  ) async {
    try {
      emit(const DashboardLoading());

      final response = await _apiService.getDashboardData();
      final dashboardData = DashboardData.fromJson(response);

      // Generate dynamic greeting
      final dynamicGreeting = await _greetingService.generateGreeting(dashboardData.userName);

      // Update dashboard data with dynamic greeting
      final updatedData = DashboardData(
        userName: dashboardData.userName,
        greeting: dynamicGreeting,
        taskSummary: dashboardData.taskSummary,
        noteSummary: dashboardData.noteSummary,
        upcomingEvents: dashboardData.upcomingEvents,
        therapyProgress: dashboardData.therapyProgress,
        quickActions: dashboardData.quickActions,
      );

      emit(DashboardLoaded(updatedData));
    } catch (e) {
      emit(DashboardError(_getErrorMessage(e)));
    }
  }

  /// Handle refresh dashboard event
  Future<void> _onRefreshDashboard(
    RefreshDashboard event,
    Emitter<DashboardState> emit,
  ) async {
    try {
      // If we have current data, show refreshing state
      if (state is DashboardLoaded) {
        final currentData = (state as DashboardLoaded).data;
        emit(DashboardRefreshing(currentData));
      } else {
        emit(const DashboardLoading());
      }
      
      final response = await _apiService.getDashboardData();
      final dashboardData = DashboardData.fromJson(response);
      
      emit(DashboardLoaded(dashboardData));
    } catch (e) {
      // If refresh fails and we had data, go back to loaded state
      if (state is DashboardRefreshing) {
        final currentData = (state as DashboardRefreshing).currentData;
        emit(DashboardLoaded(currentData));
      } else {
        emit(DashboardError(_getErrorMessage(e)));
      }
    }
  }

  /// Handle update greeting event
  Future<void> _onUpdateGreeting(
    UpdateGreeting event,
    Emitter<DashboardState> emit,
  ) async {
    if (state is DashboardLoaded) {
      final currentData = (state as DashboardLoaded).data;
      final updatedData = DashboardData(
        userName: currentData.userName,
        greeting: event.greeting,
        taskSummary: currentData.taskSummary,
        noteSummary: currentData.noteSummary,
        upcomingEvents: currentData.upcomingEvents,
        therapyProgress: currentData.therapyProgress,
        quickActions: currentData.quickActions,
      );
      emit(DashboardLoaded(updatedData));
    }
  }

  /// Convert exception to user-friendly error message
  String _getErrorMessage(dynamic error) {
    if (error.toString().contains('401')) {
      return 'Authentication required. Please log in again.';
    } else if (error.toString().contains('403')) {
      return 'Access denied. Please check your permissions.';
    } else if (error.toString().contains('404')) {
      return 'Dashboard data not found.';
    } else if (error.toString().contains('500')) {
      return 'Server error. Please try again later.';
    } else if (error.toString().contains('network') || 
               error.toString().contains('connection')) {
      return 'Network error. Please check your connection.';
    } else {
      return 'Failed to load dashboard data. Please try again.';
    }
  }
}
