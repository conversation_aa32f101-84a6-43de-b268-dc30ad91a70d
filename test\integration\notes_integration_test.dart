import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:darvis_app/blocs/notes/notes_bloc.dart';
import 'package:darvis_app/blocs/notes/notes_event.dart';
import 'package:darvis_app/blocs/notes/notes_state.dart';
import 'package:darvis_app/screens/productivity/notes_screen.dart';
import 'package:darvis_app/services/navigation_service.dart';
import 'package:darvis_app/models/isar_models.dart';
import 'package:darvis_app/utils/design_tokens.dart';

import 'notes_integration_test.mocks.dart';

@GenerateMocks([NotesBloc, NavigationService])
void main() {
  group('Notes Screen Integration Tests', () {
    late MockNotesBloc mockNotesBloc;
    late MockNavigationService mockNavigationService;

    setUp(() {
      mockNotesBloc = MockNotesBloc();
      mockNavigationService = MockNavigationService();
      
      // Register mocks in GetIt
      GetIt.instance.reset();
      GetIt.instance.registerSingleton<NotesBloc>(mockNotesBloc);
      GetIt.instance.registerSingleton<NavigationService>(mockNavigationService);
    });

    tearDown(() {
      GetIt.instance.reset();
    });

    testWidgets('should display loading state initially', (WidgetTester tester) async {
      // Arrange
      when(mockNotesBloc.state).thenReturn(const NotesLoading());
      when(mockNotesBloc.stream).thenAnswer((_) => Stream.value(const NotesLoading()));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<NotesBloc>.value(
            value: mockNotesBloc,
            child: const NotesScreen(),
          ),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display notes when loaded', (WidgetTester tester) async {
      // Arrange
      final testNotes = [
        LocalNote()
          ..id = 1
          ..title = 'Test Note 1'
          ..content = 'Test content 1'
          ..tags = ['test', 'work']
          ..isPinned = true
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now(),
        LocalNote()
          ..id = 2
          ..title = 'Test Note 2'
          ..content = 'Test content 2'
          ..tags = ['personal']
          ..isPinned = false
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now(),
      ];

      final loadedState = NotesLoaded(
        notes: testNotes,
        filteredNotes: testNotes,
        availableTags: ['test', 'work', 'personal'],
      );

      when(mockNotesBloc.state).thenReturn(loadedState);
      when(mockNotesBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<NotesBloc>.value(
            value: mockNotesBloc,
            child: const NotesScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Test Note 1'), findsOneWidget);
      expect(find.text('Test Note 2'), findsOneWidget);
      expect(find.text('Test content 1'), findsOneWidget);
      expect(find.text('Test content 2'), findsOneWidget);
    });

    testWidgets('should trigger LoadNotes event on initialization', (WidgetTester tester) async {
      // Arrange
      when(mockNotesBloc.state).thenReturn(const NotesLoading());
      when(mockNotesBloc.stream).thenAnswer((_) => Stream.value(const NotesLoading()));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<NotesBloc>.value(
            value: mockNotesBloc,
            child: const NotesScreen(),
          ),
        ),
      );

      // Assert
      verify(mockNotesBloc.add(const LoadNotes())).called(1);
    });

    testWidgets('should handle search functionality', (WidgetTester tester) async {
      // Arrange
      final testNotes = [
        LocalNote()
          ..id = 1
          ..title = 'Meeting Notes'
          ..content = 'Important meeting content'
          ..tags = ['work']
          ..isPinned = false
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now(),
        LocalNote()
          ..id = 2
          ..title = 'Personal Journal'
          ..content = 'Personal thoughts'
          ..tags = ['personal']
          ..isPinned = false
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now(),
      ];

      final loadedState = NotesLoaded(
        notes: testNotes,
        filteredNotes: testNotes,
        availableTags: ['work', 'personal'],
      );

      when(mockNotesBloc.state).thenReturn(loadedState);
      when(mockNotesBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<NotesBloc>.value(
            value: mockNotesBloc,
            child: const NotesScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Find and tap search field
      final searchField = find.byType(TextField);
      expect(searchField, findsOneWidget);
      
      await tester.tap(searchField);
      await tester.pumpAndSettle();
      
      // Enter search text
      await tester.enterText(searchField, 'meeting');
      await tester.pumpAndSettle();

      // Assert - search should filter notes locally
      // The filtered notes should only show "Meeting Notes"
      expect(find.text('Meeting Notes'), findsOneWidget);
    });

    testWidgets('should handle note selection and deletion', (WidgetTester tester) async {
      // Arrange
      final testNotes = [
        LocalNote()
          ..id = 1
          ..title = 'Test Note'
          ..content = 'Test content'
          ..tags = ['test']
          ..isPinned = false
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now(),
      ];

      final loadedState = NotesLoaded(
        notes: testNotes,
        filteredNotes: testNotes,
        availableTags: ['test'],
      );

      when(mockNotesBloc.state).thenReturn(loadedState);
      when(mockNotesBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<NotesBloc>.value(
            value: mockNotesBloc,
            child: const NotesScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Long press on note to enter selection mode
      await tester.longPress(find.text('Test Note'));
      await tester.pumpAndSettle();

      // Find and tap delete button
      final deleteButton = find.byIcon(Icons.delete);
      if (deleteButton.evaluate().isNotEmpty) {
        await tester.tap(deleteButton);
        await tester.pumpAndSettle();

        // Assert - should trigger delete events
        verify(mockNotesBloc.add(any)).called(greaterThan(1));
      }
    });

    testWidgets('should display error state correctly', (WidgetTester tester) async {
      // Arrange
      const errorState = NotesOperationError(
        error: 'Failed to load notes',
        operation: 'load',
      );
      when(mockNotesBloc.state).thenReturn(errorState);
      when(mockNotesBloc.stream).thenAnswer((_) => Stream.value(errorState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<NotesBloc>.value(
            value: mockNotesBloc,
            child: const NotesScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Error: Failed to load notes'), findsOneWidget);
    });

    testWidgets('should handle tag filtering', (WidgetTester tester) async {
      // Arrange
      final testNotes = [
        LocalNote()
          ..id = 1
          ..title = 'Work Note'
          ..content = 'Work content'
          ..tags = ['work', 'important']
          ..isPinned = false
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now(),
        LocalNote()
          ..id = 2
          ..title = 'Personal Note'
          ..content = 'Personal content'
          ..tags = ['personal']
          ..isPinned = false
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now(),
      ];

      final loadedState = NotesLoaded(
        notes: testNotes,
        filteredNotes: testNotes,
        availableTags: const ['work', 'important', 'personal'],
      );

      when(mockNotesBloc.state).thenReturn(loadedState);
      when(mockNotesBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<NotesBloc>.value(
            value: mockNotesBloc,
            child: const NotesScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Both notes should be visible initially
      expect(find.text('Work Note'), findsOneWidget);
      expect(find.text('Personal Note'), findsOneWidget);

      // Test tag filtering by opening sidebar (swipe from left)
      await tester.dragFrom(
        const Offset(0, 300),
        const Offset(200, 300),
      );
      await tester.pumpAndSettle();

      // The tag sidebar should be visible
      expect(find.text('work'), findsOneWidget);
      expect(find.text('personal'), findsOneWidget);
    });
  });
}
