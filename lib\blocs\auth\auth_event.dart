import 'package:equatable/equatable.dart';

/// Base class for authentication events
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// Event to check current authentication status
class AuthCheckRequested extends AuthEvent {}

/// Event to sign in with email and password
class AuthSignInRequested extends AuthEvent {
  final String email;
  final String password;

  const AuthSignInRequested({
    required this.email,
    required this.password,
  });

  @override
  List<Object?> get props => [email, password];
}

/// Event to sign up with email, password, and name
class AuthSignUpRequested extends AuthEvent {
  final String email;
  final String password;
  final String confirmPassword;
  final String name;

  const AuthSignUpRequested({
    required this.email,
    required this.password,
    required this.confirmPassword,
    required this.name,
  });

  @override
  List<Object?> get props => [email, password, confirmPassword, name];
}

/// Event to sign in with Google
class AuthSignInWithGoogleRequested extends AuthEvent {}

/// Event to sign up with Google
class AuthSignUpWithGoogleRequested extends AuthEvent {}

/// Event to request password reset
class AuthPasswordResetRequested extends AuthEvent {
  final String email;

  const AuthPasswordResetRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

/// Event to sign out
class AuthSignOutRequested extends AuthEvent {}

/// Event when authentication state changes (from Firebase)
class AuthStateChanged extends AuthEvent {
  final bool isAuthenticated;

  const AuthStateChanged({required this.isAuthenticated});

  @override
  List<Object?> get props => [isAuthenticated];
}
