import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:ui';
import '../../utils/design_tokens.dart';
import '../../blocs/auth/auth_bloc.dart';
import '../../blocs/auth/auth_event.dart';
import '../../blocs/auth/auth_state.dart';
import '../../widgets/enhanced_bottom_nav_bar.dart';
import '../../widgets/profile_picture_widget.dart';
import 'account_settings_screen.dart';
import 'privacy_security_screen.dart';
import 'notifications_screen.dart';
import 'data_storage_screen.dart';
import 'about_darvis_screen.dart';
import 'contact_us_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with TickerProviderStateMixin {
  
  late AnimationController _pageAnimationController;
  late AnimationController _cardAnimationController;
  late Animation<Offset> _pageSlideAnimation;
  late Animation<double> _cardFadeAnimation;
  
  // User data
  final String _userName = 'David';
  final String _userEmail = '<EMAIL>';
  final int _streakDays = 12;
  final int _totalSessions = 47;
  final String _memberSince = 'March 2024';

  void _handleLogout() {
    context.read<AuthBloc>().add(AuthSignOutRequested());
  }

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 350),
      vsync: this,
    );
    
    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _pageSlideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _pageAnimationController,
      curve: Curves.easeOut,
    ));
    
    _cardFadeAnimation = CurvedAnimation(
      parent: _cardAnimationController,
      curve: Curves.easeOut,
    );
    
    _pageAnimationController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _cardAnimationController.forward();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthUnauthenticated) {
          // Navigate to login screen on successful logout
          Navigator.of(context).pushNamedAndRemoveUntil(
            '/login',
            (route) => false,
          );
        } else if (state is AuthError) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: DesignTokens.backgroundApp,
        body: Stack(
        children: [
          // Background glow
          const _BackgroundGlow(),
          
          // Main content
          SafeArea(
            child: SlideTransition(
              position: _pageSlideAnimation,
              child: CustomScrollView(
                physics: const ClampingScrollPhysics(),
                slivers: [
                  _buildHeader(),
                  _buildProfileCard(),
                  _buildStatsSection(),
                  _buildSettingsSection(),
                  _buildSupportSection(),
                  _buildLogoutSection(),
                ],
              ),
            ),
          ),
        ],
      ),
      // The bottom navigation bar is now provided by the TabScreenWrapper
      ),
    );
  }

  Widget _buildHeader() {
    return SliverToBoxAdapter(
      child: Container(
        height: 80,
        padding: const EdgeInsets.fromLTRB(
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingSm,
        ),
        child: Center(
          child: ShaderMask(
            shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
            child: Text(
              'Profile',
              style: DesignTokens.therapySessionTitleStyle.copyWith(
                color: Colors.white,
                fontSize: 32,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileCard() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: AnimatedBuilder(
          animation: _cardFadeAnimation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, 20 * (1 - _cardFadeAnimation.value)),
              child: Opacity(
                opacity: _cardFadeAnimation.value,
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(DesignTokens.spacingLg),
                  decoration: BoxDecoration(
                    color: DesignTokens.backgroundCard.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.1),
                      width: 1,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                      child: Column(
                        children: [
                          // Profile picture with Cloudinary integration
                          const ProfilePictureWidget(
                            radius: 50,
                            showEditIcon: true,
                            fallbackAsset: 'assets/images/darvis_main.PNG',
                          ),
                          
                          const SizedBox(height: DesignTokens.spacingLg),
                          
                          // User name
                          Text(
                            _userName,
                            style: DesignTokens.cardTitleStyle.copyWith(
                              fontSize: 24,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          
                          const SizedBox(height: DesignTokens.spacingSm),
                          
                          // User email
                          Text(
                            _userEmail,
                            style: DesignTokens.bodyStyle.copyWith(
                              color: DesignTokens.textSecondary,
                            ),
                          ),
                          
                          const SizedBox(height: DesignTokens.spacingSm),
                          
                          // Member since
                          Text(
                            'Member since $_memberSince',
                            style: DesignTokens.bodyStyle.copyWith(
                              color: DesignTokens.textMuted,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatsSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
        child: AnimatedBuilder(
          animation: _cardFadeAnimation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, 30 * (1 - _cardFadeAnimation.value)),
              child: Opacity(
                opacity: _cardFadeAnimation.value,
                child: Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'Current Streak',
                        '$_streakDays days',
                        Icons.local_fire_department,
                        DesignTokens.priorityHigh,
                      ),
                    ),
                    const SizedBox(width: DesignTokens.spacingMd),
                    Expanded(
                      child: _buildStatCard(
                        'Total Sessions',
                        '$_totalSessions',
                        Icons.psychology,
                        DesignTokens.primaryInteractiveBlue,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Column(
            children: [
              Icon(
                icon,
                color: color,
                size: 32,
              ),
              const SizedBox(height: DesignTokens.spacingSm),
              Text(
                value,
                style: DesignTokens.cardTitleStyle.copyWith(
                  fontSize: 20,
                  color: color,
                ),
              ),
              const SizedBox(height: DesignTokens.spacingXs),
              Text(
                title,
                style: DesignTokens.bodyStyle.copyWith(
                  color: DesignTokens.textSecondary,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: AnimatedBuilder(
          animation: _cardFadeAnimation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, 40 * (1 - _cardFadeAnimation.value)),
              child: Opacity(
                opacity: _cardFadeAnimation.value,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Settings',
                      style: DesignTokens.cardTitleStyle.copyWith(
                        fontSize: 20,
                      ),
                    ),
                    const SizedBox(height: DesignTokens.spacingMd),
                    _buildSettingsItem(
                      'Account Settings',
                      'Manage your account details',
                      Icons.person_outline,
                      () => Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AccountSettingsScreen(),
                        ),
                      ),
                    ),
                    _buildSettingsItem(
                      'Privacy & Security',
                      'Control your privacy settings',
                      Icons.security,
                      () => Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const PrivacySecurityScreen(),
                        ),
                      ),
                    ),
                    _buildSettingsItem(
                      'Notifications',
                      'Customize your notifications',
                      Icons.notifications_outlined,
                      () => Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const NotificationsScreen(),
                        ),
                      ),
                    ),
                    _buildSettingsItem(
                      'Data & Storage',
                      'Manage your data and storage',
                      Icons.storage,
                      () => Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const DataStorageScreen(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSupportSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: AnimatedBuilder(
          animation: _cardFadeAnimation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, 50 * (1 - _cardFadeAnimation.value)),
              child: Opacity(
                opacity: _cardFadeAnimation.value,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Support',
                      style: DesignTokens.cardTitleStyle.copyWith(
                        fontSize: 20,
                      ),
                    ),
                    const SizedBox(height: DesignTokens.spacingMd),
                    _buildSettingsItem(
                      'Contact Us',
                      'Reach out to our team',
                      Icons.mail_outline,
                      () => Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const ContactUsScreen(),
                        ),
                      ),
                    ),
                    _buildSettingsItem(
                      'About Drix',
                      'Learn more about Drix',
                      Icons.info_outline,
                      () => Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AboutDarvisScreen(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLogoutSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: AnimatedBuilder(
          animation: _cardFadeAnimation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, 60 * (1 - _cardFadeAnimation.value)),
              child: Opacity(
                opacity: _cardFadeAnimation.value,
                child: Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(bottom: DesignTokens.spacingXl),
                  child: ElevatedButton(
                    onPressed: _showLogoutDialog,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: DesignTokens.priorityHigh.withValues(alpha: 0.2),
                      padding: const EdgeInsets.symmetric(
                        vertical: DesignTokens.spacingMd,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                        side: BorderSide(
                          color: DesignTokens.priorityHigh,
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.logout,
                          color: DesignTokens.priorityHigh,
                          size: 20,
                        ),
                        const SizedBox(width: DesignTokens.spacingSm),
                        Text(
                          'Sign Out',
                          style: DesignTokens.bodyStyle.copyWith(
                            color: DesignTokens.priorityHigh,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSettingsItem(String title, String subtitle, IconData icon, VoidCallback onTap) {
    return Container(
      margin: const EdgeInsets.only(bottom: DesignTokens.spacingSm),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          child: Container(
            padding: const EdgeInsets.all(DesignTokens.spacingMd),
            decoration: BoxDecoration(
              color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.08),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                      ),
                      child: Icon(
                        icon,
                        color: DesignTokens.primaryInteractiveBlue,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: DesignTokens.spacingMd),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: DesignTokens.cardTitleStyle.copyWith(
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: DesignTokens.spacingXs),
                          Text(
                            subtitle,
                            style: DesignTokens.bodyStyle.copyWith(
                              color: DesignTokens.textSecondary,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.chevron_right,
                      color: DesignTokens.iconSecondary,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }



  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature coming soon!'),
        backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DesignTokens.backgroundCard,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        ),
        title: Text(
          'Sign Out',
          style: DesignTokens.cardTitleStyle,
        ),
        content: Text(
          'Are you sure you want to sign out?',
          style: DesignTokens.bodyStyle.copyWith(
            color: DesignTokens.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textSecondary,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _handleLogout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: DesignTokens.priorityHigh,
            ),
            child: Text(
              'Sign Out',
              style: DesignTokens.bodyStyle.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _pageAnimationController.dispose();
    _cardAnimationController.dispose();
    super.dispose();
  }
}

// Background Glow Component
class _BackgroundGlow extends StatelessWidget {
  const _BackgroundGlow();

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.1,
        decoration: const BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Color.fromARGB(255, 9, 38, 107),
              blurRadius: 150.0,
              spreadRadius: 50.0,
            ),
          ],
        ),
      ),
    );
  }
}


