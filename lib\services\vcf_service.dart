import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:vcf_dart/vcf_dart.dart';
import '../services/data_service.dart';

/// Service for generating and sharing VCF (vCard) files
class VcfService {
  static final VcfService _instance = VcfService._internal();
  factory VcfService() => _instance;
  VcfService._internal();

  /// Generate VCF content from a Contact object (backend-integrated)
  Future<String> generateVcfContent(Contact contact) async {
    // Try to get VCF from backend first
    try {
      if (contact.id.isNotEmpty) {
        final backendVcf = await DataService().getContactVCF(contact.id);
        if (backendVcf.isNotEmpty) {
          return backendVcf;
        }
      }
    } catch (e) {
      debugPrint('Backend VCF generation failed, using local fallback: $e');
    }

    // Fallback to local VCF generation
    return _generateLocalVcfContent(contact);
  }

  /// Generate VCF content locally (fallback method)
  String _generateLocalVcfContent(Contact contact) {
    final buffer = StringBuffer();

    // VCF Header
    buffer.writeln('BEGIN:VCARD');
    buffer.writeln('VERSION:3.0');

    // Name fields
    final nameParts = contact.name.split(' ');
    final lastName = nameParts.length > 1 ? nameParts.last : '';
    final firstName = nameParts.first;

    // N: Last;First;;;
    buffer.writeln('N:$lastName;$firstName;;;');

    // FN: Full Name
    buffer.writeln('FN:${contact.name}');

    // Phone number
    if (contact.phone.isNotEmpty) {
      buffer.writeln('TEL;TYPE=CELL:${contact.phone}');
    }

    // Email
    if (contact.email.isNotEmpty) {
      buffer.writeln('EMAIL;TYPE=HOME:${contact.email}');
    }

    // Organization/Location
    if (contact.location.isNotEmpty) {
      buffer.writeln('ORG:Met at: ${contact.location}');
    }

    // Social media URL - updated for new model structure
    if (contact.socialMedia.isNotEmpty) {
      final platform = contact.socialMedia.keys.first;
      final username = contact.socialMedia.values.first;
      final socialUrl = _generateSocialMediaUrl(platform, username);
      if (socialUrl.isNotEmpty) {
        buffer.writeln('URL:$socialUrl');
      }
    }

    // Notes - updated for new model structure
    if (contact.memoryPrompt.isNotEmpty) {
      buffer.writeln('NOTE:${contact.memoryPrompt}');
    }

    // Revision timestamp
    buffer.writeln('REV:${contact.createdAt.toIso8601String()}');

    // VCF Footer
    buffer.writeln('END:VCARD');

    return buffer.toString();
  }

  /// Generate social media URL for VCF
  String _generateSocialMediaUrl(String platform, String username) {
    switch (platform.toLowerCase()) {
      case 'twitter':
        return 'https://twitter.com/$username';
      case 'instagram':
        return 'https://instagram.com/$username';
      case 'linkedin':
        return 'https://linkedin.com/in/$username';
      case 'whatsapp':
        return 'https://wa.me/$username';
      default:
        return '';
    }
  }

  /// Save VCF file to device and return file path
  Future<String> saveVcfFile(Contact contact) async {
    try {
      final vcfContent = await generateVcfContent(contact);
      
      // Get app documents directory
      final directory = await getApplicationDocumentsDirectory();
      final vcfDirectory = Directory('${directory.path}/vcf_contacts');
      
      // Create directory if it doesn't exist
      if (!await vcfDirectory.exists()) {
        await vcfDirectory.create(recursive: true);
      }
      
      // Create file with contact name and timestamp
      final fileName = '${contact.name.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.vcf';
      final file = File('${vcfDirectory.path}/$fileName');
      
      // Write VCF content to file
      await file.writeAsString(vcfContent);
      
      return file.path;
    } catch (e) {
      debugPrint('Error saving VCF file: $e');
      rethrow;
    }
  }

  /// Share VCF file using the device's share functionality
  Future<void> shareVcfFile(Contact contact) async {
    try {
      final filePath = await saveVcfFile(contact);
      final file = XFile(filePath);
      
      await Share.shareXFiles(
        [file],
        text: 'Contact: ${contact.name}',
        subject: 'Contact Information - ${contact.name}',
      );
    } catch (e) {
      debugPrint('Error sharing VCF file: $e');
      rethrow;
    }
  }

  /// Generate VCF file with custom naming convention for local contacts
  /// Uses "where did our paths cross today" format
  Future<String> generateContactForLocalSave(Contact contact) async {
    try {
      final vcfContent = await generateVcfContent(contact);
      
      // Get temporary directory for sharing
      final directory = await getTemporaryDirectory();
      
      // Use the naming convention from the location field
      final meetingLocation = contact.location.isNotEmpty 
          ? contact.location 
          : 'where our paths crossed today';
      
      final fileName = '${contact.name} - $meetingLocation.vcf';
      final file = File('${directory.path}/$fileName');
      
      // Write VCF content to file
      await file.writeAsString(vcfContent);
      
      return file.path;
    } catch (e) {
      debugPrint('Error generating VCF for local save: $e');
      rethrow;
    }
  }

  /// Clean up old VCF files to prevent storage bloat
  Future<void> cleanupOldVcfFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final vcfDirectory = Directory('${directory.path}/vcf_contacts');
      
      if (await vcfDirectory.exists()) {
        final files = vcfDirectory.listSync();
        final now = DateTime.now();
        
        for (final file in files) {
          if (file is File && file.path.endsWith('.vcf')) {
            final stat = await file.stat();
            final daysSinceCreation = now.difference(stat.modified).inDays;
            
            // Delete files older than 30 days
            if (daysSinceCreation > 30) {
              await file.delete();
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error cleaning up VCF files: $e');
    }
  }

  /// Validate VCF content
  bool validateVcfContent(String vcfContent) {
    try {
      final stack = VCardStack.fromData(vcfContent);
      return stack.vcardStack.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
