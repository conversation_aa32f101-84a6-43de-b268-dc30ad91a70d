import 'package:equatable/equatable.dart';
import 'package:image_picker/image_picker.dart';
import '../../models/profile_models.dart';

/// Base profile event
abstract class ProfileEvent extends Equatable {
  const ProfileEvent();

  @override
  List<Object?> get props => [];
}

/// Load user profile
class LoadUserProfile extends ProfileEvent {
  final String userId;

  const LoadUserProfile(this.userId);

  @override
  List<Object> get props => [userId];
}

/// Update user profile
class UpdateUserProfile extends ProfileEvent {
  final UserProfile profile;

  const UpdateUserProfile(this.profile);

  @override
  List<Object> get props => [profile];
}

/// Update display name
class UpdateDisplayName extends ProfileEvent {
  final String userId;
  final String displayName;

  const UpdateDisplayName({
    required this.userId,
    required this.displayName,
  });

  @override
  List<Object> get props => [userId, displayName];
}

/// Update Darvis name (what Dr<PERSON> should call the user)
class UpdateDarvisName extends ProfileEvent {
  final String userId;
  final String darvisName;

  const UpdateDarvisName({
    required this.userId,
    required this.darvisName,
  });

  @override
  List<Object> get props => [userId, darvisName];
}

/// Change profile picture
class ChangeProfilePicture extends ProfileEvent {
  final String userId;
  final ImageSource source;

  const ChangeProfilePicture({
    required this.userId,
    required this.source,
  });

  @override
  List<Object> get props => [userId, source];
}

/// Delete profile picture
class DeleteProfilePicture extends ProfileEvent {
  final String userId;

  const DeleteProfilePicture(this.userId);

  @override
  List<Object> get props => [userId];
}

/// Update time zone
class UpdateTimeZone extends ProfileEvent {
  final String userId;
  final String timeZone;

  const UpdateTimeZone({
    required this.userId,
    required this.timeZone,
  });

  @override
  List<Object> get props => [userId, timeZone];
}

/// Update language
class UpdateLanguage extends ProfileEvent {
  final String userId;
  final String language;

  const UpdateLanguage({
    required this.userId,
    required this.language,
  });

  @override
  List<Object> get props => [userId, language];
}

/// Update theme
class UpdateTheme extends ProfileEvent {
  final String userId;
  final String theme;

  const UpdateTheme({
    required this.userId,
    required this.theme,
  });

  @override
  List<Object> get props => [userId, theme];
}

/// Update biometric authentication
class UpdateBiometricAuth extends ProfileEvent {
  final String userId;
  final bool enabled;

  const UpdateBiometricAuth({
    required this.userId,
    required this.enabled,
  });

  @override
  List<Object> get props => [userId, enabled];
}

/// Update session timeout
class UpdateSessionTimeout extends ProfileEvent {
  final String userId;
  final String timeout;

  const UpdateSessionTimeout({
    required this.userId,
    required this.timeout,
  });

  @override
  List<Object> get props => [userId, timeout];
}

/// Update app lock requirement
class UpdateAppLockRequired extends ProfileEvent {
  final String userId;
  final bool required;

  const UpdateAppLockRequired({
    required this.userId,
    required this.required,
  });

  @override
  List<Object> get props => [userId, required];
}

/// Update cloud sync setting
class UpdateCloudSync extends ProfileEvent {
  final String userId;
  final bool enabled;

  const UpdateCloudSync({
    required this.userId,
    required this.enabled,
  });

  @override
  List<Object> get props => [userId, enabled];
}

/// Update auto backup setting
class UpdateAutoBackup extends ProfileEvent {
  final String userId;
  final bool enabled;

  const UpdateAutoBackup({
    required this.userId,
    required this.enabled,
  });

  @override
  List<Object> get props => [userId, enabled];
}

/// Update current streak
class UpdateCurrentStreak extends ProfileEvent {
  final String userId;
  final int streak;

  const UpdateCurrentStreak({
    required this.userId,
    required this.streak,
  });

  @override
  List<Object> get props => [userId, streak];
}

/// Update subscription plan
class UpdateSubscriptionPlan extends ProfileEvent {
  final String userId;
  final String plan;
  final DateTime? expiryDate;
  final bool isActive;

  const UpdateSubscriptionPlan({
    required this.userId,
    required this.plan,
    this.expiryDate,
    required this.isActive,
  });

  @override
  List<Object?> get props => [userId, plan, expiryDate, isActive];
}

/// Load profile picture history
class LoadProfilePictureHistory extends ProfileEvent {
  final String userId;

  const LoadProfilePictureHistory(this.userId);

  @override
  List<Object> get props => [userId];
}

/// Sync profile with backend
class SyncProfile extends ProfileEvent {
  final String userId;

  const SyncProfile(this.userId);

  @override
  List<Object> get props => [userId];
}

/// Reset profile to defaults
class ResetProfile extends ProfileEvent {
  final String userId;

  const ResetProfile(this.userId);

  @override
  List<Object> get props => [userId];
}

/// Export profile data
class ExportProfileData extends ProfileEvent {
  final String userId;
  final String format; // 'json', 'csv', etc.

  const ExportProfileData({
    required this.userId,
    required this.format,
  });

  @override
  List<Object> get props => [userId, format];
}

/// Import profile data
class ImportProfileData extends ProfileEvent {
  final String userId;
  final String filePath;

  const ImportProfileData({
    required this.userId,
    required this.filePath,
  });

  @override
  List<Object> get props => [userId, filePath];
}

/// Delete user account
class DeleteUserAccount extends ProfileEvent {
  final String userId;
  final String confirmationText;

  const DeleteUserAccount({
    required this.userId,
    required this.confirmationText,
  });

  @override
  List<Object> get props => [userId, confirmationText];
}

/// Backup profile data
class BackupProfileData extends ProfileEvent {
  final String userId;

  const BackupProfileData(this.userId);

  @override
  List<Object> get props => [userId];
}

/// Restore profile data
class RestoreProfileData extends ProfileEvent {
  final String userId;
  final String backupId;

  const RestoreProfileData({
    required this.userId,
    required this.backupId,
  });

  @override
  List<Object> get props => [userId, backupId];
}
