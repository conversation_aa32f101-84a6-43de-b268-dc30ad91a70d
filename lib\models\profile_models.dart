import 'package:isar/isar.dart';

part 'profile_models.g.dart';

/// User profile model for local storage
@collection
class UserProfile {
  Id id = Isar.autoIncrement;
  
  @Index()
  late String userId; // Firebase UID or backend user ID
  
  // Basic profile information
  late String displayName;
  late String email;
  String? darvisName; // What Drix should call the user
  
  // Profile picture information
  String? profilePictureUrl; // Cloudinary URL
  String? profilePicturePublicId; // Cloudinary public ID for management
  String? localProfilePicturePath; // Local cache path
  
  // Account information
  DateTime? memberSince;
  int currentStreak = 0;
  DateTime? lastActivityDate;
  
  // Subscription information
  String subscriptionPlan = 'Essential'; // 'Essential', 'Unlimited'
  DateTime? subscriptionExpiryDate;
  bool isSubscriptionActive = false;
  
  // Settings
  String timeZone = 'UTC';
  String language = 'en';
  String theme = 'system'; // 'light', 'dark', 'system'
  
  // Privacy settings
  bool biometricAuthEnabled = false;
  String sessionTimeout = '30 minutes'; // '15 minutes', '30 minutes', '1 hour', '2 hours', 'Never'
  bool appLockRequired = false;
  
  // Data sync settings
  bool cloudSyncEnabled = true;
  bool autoBackupEnabled = true;
  DateTime? lastBackupDate;
  
  // Timestamps
  late DateTime createdAt;
  late DateTime updatedAt;
  
  @Index()
  late bool needsSync;
  
  @Index()
  late String syncStatus; // 'synced', 'pending', 'error'
}

/// Profile picture upload state
enum ProfilePictureUploadState {
  idle,
  selecting,
  uploading,
  processing,
  success,
  error,
}

/// Profile picture management model
@collection
class ProfilePictureHistory {
  Id id = Isar.autoIncrement;
  
  @Index()
  late String userId;
  
  late String cloudinaryUrl;
  late String cloudinaryPublicId;
  String? localPath;
  
  bool isActive = false; // Current profile picture
  
  late DateTime uploadedAt;
  String? uploadSource; // 'camera', 'gallery', 'default'
  
  // Cloudinary transformation info
  String? transformationUrl; // URL with applied transformations
  String transformationsJson = '{}'; // Applied transformations as JSON
  
  @Index()
  late bool needsSync;
}

/// Data transfer objects for API communication
class UserProfileDto {
  final String? id;
  final String userId;
  final String displayName;
  final String email;
  final String? darvisName;
  final String? profilePictureUrl;
  final String? profilePicturePublicId;
  final DateTime? memberSince;
  final int currentStreak;
  final DateTime? lastActivityDate;
  final String subscriptionPlan;
  final DateTime? subscriptionExpiryDate;
  final bool isSubscriptionActive;
  final String timeZone;
  final String language;
  final String theme;
  final bool biometricAuthEnabled;
  final String sessionTimeout;
  final bool appLockRequired;
  final bool cloudSyncEnabled;
  final bool autoBackupEnabled;
  final DateTime? lastBackupDate;

  UserProfileDto({
    this.id,
    required this.userId,
    required this.displayName,
    required this.email,
    this.darvisName,
    this.profilePictureUrl,
    this.profilePicturePublicId,
    this.memberSince,
    required this.currentStreak,
    this.lastActivityDate,
    required this.subscriptionPlan,
    this.subscriptionExpiryDate,
    required this.isSubscriptionActive,
    required this.timeZone,
    required this.language,
    required this.theme,
    required this.biometricAuthEnabled,
    required this.sessionTimeout,
    required this.appLockRequired,
    required this.cloudSyncEnabled,
    required this.autoBackupEnabled,
    this.lastBackupDate,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'user_id': userId,
    'display_name': displayName,
    'email': email,
    'darvis_name': darvisName,
    'profile_picture_url': profilePictureUrl,
    'profile_picture_public_id': profilePicturePublicId,
    'member_since': memberSince?.toIso8601String(),
    'current_streak': currentStreak,
    'last_activity_date': lastActivityDate?.toIso8601String(),
    'subscription_plan': subscriptionPlan,
    'subscription_expiry_date': subscriptionExpiryDate?.toIso8601String(),
    'is_subscription_active': isSubscriptionActive,
    'time_zone': timeZone,
    'language': language,
    'theme': theme,
    'biometric_auth_enabled': biometricAuthEnabled,
    'session_timeout': sessionTimeout,
    'app_lock_required': appLockRequired,
    'cloud_sync_enabled': cloudSyncEnabled,
    'auto_backup_enabled': autoBackupEnabled,
    'last_backup_date': lastBackupDate?.toIso8601String(),
  };

  factory UserProfileDto.fromJson(Map<String, dynamic> json) => UserProfileDto(
    id: json['id'],
    userId: json['user_id'],
    displayName: json['display_name'],
    email: json['email'],
    darvisName: json['darvis_name'],
    profilePictureUrl: json['profile_picture_url'],
    profilePicturePublicId: json['profile_picture_public_id'],
    memberSince: json['member_since'] != null ? DateTime.parse(json['member_since']) : null,
    currentStreak: json['current_streak'] ?? 0,
    lastActivityDate: json['last_activity_date'] != null ? DateTime.parse(json['last_activity_date']) : null,
    subscriptionPlan: json['subscription_plan'] ?? 'Essential',
    subscriptionExpiryDate: json['subscription_expiry_date'] != null ? DateTime.parse(json['subscription_expiry_date']) : null,
    isSubscriptionActive: json['is_subscription_active'] ?? false,
    timeZone: json['time_zone'] ?? 'UTC',
    language: json['language'] ?? 'en',
    theme: json['theme'] ?? 'system',
    biometricAuthEnabled: json['biometric_auth_enabled'] ?? false,
    sessionTimeout: json['session_timeout'] ?? '30 minutes',
    appLockRequired: json['app_lock_required'] ?? false,
    cloudSyncEnabled: json['cloud_sync_enabled'] ?? true,
    autoBackupEnabled: json['auto_backup_enabled'] ?? true,
    lastBackupDate: json['last_backup_date'] != null ? DateTime.parse(json['last_backup_date']) : null,
  );
}

class CloudinaryUploadResponse {
  final String publicId;
  final String secureUrl;
  final String url;
  final String format;
  final int bytes;
  final int width;
  final int height;
  final DateTime createdAt;

  CloudinaryUploadResponse({
    required this.publicId,
    required this.secureUrl,
    required this.url,
    required this.format,
    required this.bytes,
    required this.width,
    required this.height,
    required this.createdAt,
  });

  factory CloudinaryUploadResponse.fromJson(Map<String, dynamic> json) => CloudinaryUploadResponse(
    publicId: json['public_id'],
    secureUrl: json['secure_url'],
    url: json['url'],
    format: json['format'],
    bytes: json['bytes'],
    width: json['width'],
    height: json['height'],
    createdAt: DateTime.parse(json['created_at']),
  );
}

class ProfilePictureUpdateRequest {
  final String userId;
  final String cloudinaryUrl;
  final String cloudinaryPublicId;
  final String? previousPublicId; // For cleanup

  ProfilePictureUpdateRequest({
    required this.userId,
    required this.cloudinaryUrl,
    required this.cloudinaryPublicId,
    this.previousPublicId,
  });

  Map<String, dynamic> toJson() => {
    'user_id': userId,
    'cloudinary_url': cloudinaryUrl,
    'cloudinary_public_id': cloudinaryPublicId,
    'previous_public_id': previousPublicId,
  };
}
