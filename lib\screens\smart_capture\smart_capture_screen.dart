import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'dart:async';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:get_it/get_it.dart';
import '../../utils/design_tokens.dart';
import '../../widgets/enhanced_bottom_nav_bar.dart';
import '../../services/share_intent_service.dart';
import '../../services/navigation_service.dart';
import 'models/captured_content.dart';
import 'services/content_processing_service.dart';
import 'services/smart_capture_api_service.dart';
import 'services/smart_capture_error_handler.dart';
import 'widgets/content_card.dart';
import 'widgets/glassmorphic_input_field.dart';
import 'widgets/content_detail_modal.dart';

class SmartCaptureScreen extends StatefulWidget {
  const SmartCaptureScreen({super.key});

  @override
  State<SmartCaptureScreen> createState() => _SmartCaptureScreenState();
}

class _SmartCaptureScreenState extends State<SmartCaptureScreen>
    with TickerProviderStateMixin {
  
  final TextEditingController _inputController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _inputFocusNode = FocusNode();
  final FocusNode _searchFocusNode = FocusNode();
  
  late AnimationController _pageAnimationController;
  late AnimationController _cardAnimationController;
  late Animation<Offset> _pageSlideAnimation;
  late Animation<double> _cardFadeAnimation;
  
  // Services
  late final ContentProcessingService _contentService;
  late final SmartCaptureApiService _apiService;
  late final ShareIntentService _shareIntentService;

  // Stream subscriptions
  StreamSubscription<String>? _textShareSubscription;
  StreamSubscription<List<String>>? _imageShareSubscription;

  // State
  List<CapturedContent> _allContent = [];
  List<CapturedContent> _filteredContent = [];
  String _searchQuery = '';
  ContentFilter _currentFilter = ContentFilter.all;
  SortOption _currentSort = SortOption.chronological;
  bool _isSearchExpanded = false;
  bool _isBulkSelectionMode = false;
  Set<String> _selectedItems = {};
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeAnimations();
    _setupListeners();
    _setupShareIntentListeners();
    _loadExistingContent();
  }

  void _initializeServices() {
    try {
      // Initialize API service
      _apiService = SmartCaptureApiService(GetIt.instance.get());
      _contentService = _apiService as ContentProcessingService;

      // Initialize share intent service
      _shareIntentService = ShareIntentService();
      _shareIntentService.initialize();
    } catch (e) {
      print('Error initializing services: $e');
      // Show error to user and rethrow
      SmartCaptureErrorHandler.showErrorSnackBar(
        context,
        'Failed to initialize Smart Capture services. Please restart the app.',
      );
      rethrow;
    }
  }

  void _setupShareIntentListeners() {
    try {
      // Listen for shared text content
      _textShareSubscription = _shareIntentService.sharedTextStream.listen(
        (sharedText) {
          if (_shareIntentService.isValidSharedContent(sharedText)) {
            _handleSharedContent(sharedText);
          }
        },
        onError: (error) {
          print('Error receiving shared text: $error');
        },
      );

      // Listen for shared images
      _imageShareSubscription = _shareIntentService.sharedImageStream.listen(
        (imagePaths) {
          if (imagePaths.isNotEmpty) {
            _handleSharedImages(imagePaths);
          }
        },
        onError: (error) {
          print('Error receiving shared images: $error');
        },
      );
    } catch (e) {
      print('Error setting up share intent listeners: $e');
    }
  }

  /// Handle shared text content from external apps
  void _handleSharedContent(String sharedText) {
    final cleanText = _shareIntentService.cleanSharedText(sharedText);

    // Extract URLs if present
    final urls = _shareIntentService.extractUrls(cleanText);

    if (urls.isNotEmpty) {
      // Process the first URL found
      _processInput(urls.first);
    } else if (_shareIntentService.isUrl(cleanText)) {
      // Process as URL
      _processInput(cleanText);
    } else {
      // Handle as text content
      _inputController.text = cleanText;
      _inputFocusNode.requestFocus();

      // Show helpful message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Shared content added to input field'),
          backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  /// Handle shared images from external apps
  void _handleSharedImages(List<String> imagePaths) {
    // For now, show a message about image processing
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Received ${imagePaths.length} image(s). Image processing will be available soon.'),
        backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
        duration: const Duration(seconds: 3),
      ),
    );

    // Process shared images
    for (final imagePath in imagePaths) {
      _processImage(File(imagePath));
    }
  }

  void _initializeAnimations() {
    _pageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 350),
      vsync: this,
    );
    
    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _pageSlideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _pageAnimationController,
      curve: Curves.easeOut,
    ));
    
    _cardFadeAnimation = CurvedAnimation(
      parent: _cardAnimationController,
      curve: Curves.easeOut,
    );
    
    _pageAnimationController.forward();
  }

  void _setupListeners() {
    _searchController.addListener(_onSearchChanged);
    _inputController.addListener(_onInputChanged);
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterContent();
    });
  }

  void _onInputChanged() {
    // Detect paste or content input
    if (_inputController.text.isNotEmpty) {
      _processInput(_inputController.text);
    }
  }

  Future<void> _loadExistingContent() async {
    // Load existing content from backend
    final existingContent = await _contentService.loadExistingContent();
    setState(() {
      _allContent = existingContent;
      _filterContent();
    });

    // Animate cards in with stagger
    _animateCardsIn();
  }

  Future<void> _processImage(File image) async {
    // Create placeholder content immediately
    final newContent = CapturedContent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      url: image.path,
      title: 'Processing image...',
      summary: 'Analyzing image content with AI...',
      tag: 'Image',
      type: ContentType.image,
      status: ProcessingStatus.processing,
      timestamp: DateTime.now(),
      thumbnailUrl: image.path,
    );

    setState(() {
      _allContent.insert(0, newContent);
      _filterContent();
    });

    // Clear input
    _inputController.clear();

    // Process image in background
    try {
      final processedContent = await _contentService.processImage(image);

      // Update the content
      final index = _allContent.indexWhere((c) => c.id == newContent.id);
      if (index != -1) {
        setState(() {
          _allContent[index] = processedContent.copyWith(
            id: newContent.id,
            timestamp: newContent.timestamp,
          );
          _filterContent();
        });
      }
    } catch (e) {
      // Handle error
      final index = _allContent.indexWhere((c) => c.id == newContent.id);
      if (index != -1) {
        setState(() {
          _allContent[index] = newContent.copyWith(
            status: ProcessingStatus.error,
            title: 'Failed to process image',
            summary: 'Couldn\'t process this image',
          );
          _filterContent();
        });
      }
    }
  }

  void _animateCardsIn() {
    _cardAnimationController.reset();
    _cardAnimationController.forward();
  }

  void _filterContent() {
    List<CapturedContent> filtered = _allContent;
    
    // Apply content type filter
    if (_currentFilter != ContentFilter.all) {
      filtered = filtered.where((content) {
        switch (_currentFilter) {
          case ContentFilter.links:
            return content.type == ContentType.link;
          case ContentFilter.images:
            return content.type == ContentType.image;
          case ContentFilter.all:
            return true;
        }
      }).toList();
    }
    
    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((content) {
        return content.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               content.summary.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               content.tag.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }
    
    // Apply sorting
    if (_currentSort == SortOption.alphabetical) {
      filtered.sort((a, b) => a.title.compareTo(b.title));
    } else {
      filtered.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    }
    
    setState(() {
      _filteredContent = filtered;
    });
  }

  Future<void> _processInput(String input) async {
    if (input.trim().isEmpty) return;
    
    // Clear input with animation
    _inputController.clear();
    
    // Create new content item
    final newContent = CapturedContent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      url: input.trim(),
      title: 'Processing...',
      summary: '',
      tag: '',
      type: _detectContentType(input.trim()),
      status: ProcessingStatus.processing,
      timestamp: DateTime.now(),
    );
    
    setState(() {
      _allContent.insert(0, newContent);
      _filterContent();
    });
    
    // Process content in background
    try {
      final processedContent = await _contentService.processUrl(input.trim());
      
      // Update the content
      final index = _allContent.indexWhere((c) => c.id == newContent.id);
      if (index != -1) {
        setState(() {
          _allContent[index] = processedContent.copyWith(
            id: newContent.id,
            timestamp: newContent.timestamp,
          );
          _filterContent();
        });
      }
    } catch (e) {
      // Handle error
      final index = _allContent.indexWhere((c) => c.id == newContent.id);
      if (index != -1) {
        setState(() {
          _allContent[index] = newContent.copyWith(
            status: ProcessingStatus.error,
            title: 'Failed to process',
            summary: 'Couldn\'t process this content',
          );
          _filterContent();
        });
      }
    }
  }

  ContentType _detectContentType(String input) {
    if (input.startsWith('http') || input.startsWith('www')) {
      return ContentType.link;
    }
    return ContentType.image;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Dismiss keyboard and unfocus when tapping outside
        FocusScope.of(context).unfocus();
        setState(() {
          if (_isSearchExpanded) {
            _isSearchExpanded = false;
          }
        });
      },
      child: Scaffold(
        backgroundColor: DesignTokens.backgroundApp,
        body: SafeArea(
          child: SlideTransition(
            position: _pageSlideAnimation,
            child: RefreshIndicator(
              onRefresh: _refreshContent,
              backgroundColor: DesignTokens.backgroundCard,
              color: DesignTokens.primaryInteractiveBlue,
              child: CustomScrollView(
                physics: const ClampingScrollPhysics(),
                slivers: [
                  _buildHeader(),
                  _buildInputAndSearchSection(),
                  _buildFilterSection(),
                  _buildContentList(),
                ],
              ),
            ),
          ),
        ),
        // The bottom navigation bar is now provided by the TabScreenWrapper
      ),
    );
  }

  Widget _buildHeader() {
    return SliverToBoxAdapter(
      child: Container(
        height: 80, // Increased height for better visibility
        padding: const EdgeInsets.fromLTRB(
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingSm,
        ),
        child: Center(
          child: ShaderMask(
            shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
            child: Text(
              'Smart Capture',
              style: DesignTokens.therapySessionTitleStyle.copyWith(
                color: Colors.white,
                fontSize: 32,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInputAndSearchSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
        child: Row(
          children: [
            // Input field - takes majority width or compresses to icon
            Expanded(
              flex: _isSearchExpanded ? 1 : 5,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                child: _isSearchExpanded
                    ? _buildCompressedInputIcon()
                    : GlassmorphicInputField(
                        controller: _inputController,
                        focusNode: _inputFocusNode,
                        placeholder: 'Paste a link or add an image...',
                        onSubmitted: _processInput,
                        onIconTap: _pickImage,
                      ),
              ),
            ),

            const SizedBox(width: DesignTokens.spacingMd),

            // Search - compact icon or expanded field
            Expanded(
              flex: _isSearchExpanded ? 5 : 1,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                child: _isSearchExpanded
                    ? GlassmorphicInputField(
                        controller: _searchController,
                        focusNode: _searchFocusNode,
                        placeholder: 'Search your saved content',
                        isSearchField: true,
                        isExpanded: _isSearchExpanded,
                        onFocusChanged: (focused) {
                          setState(() {
                            _isSearchExpanded = focused;
                          });
                        },
                      )
                    : _buildSearchIcon(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompressedInputIcon() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isSearchExpanded = false;
        });
        _inputFocusNode.requestFocus();
      },
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: const Center(
              child: Icon(
                Icons.add_link,
                color: DesignTokens.iconSecondary,
                size: 20,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchIcon() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isSearchExpanded = true;
        });
        _searchFocusNode.requestFocus();
      },
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: const Center(
              child: Icon(
                Icons.search,
                color: DesignTokens.iconSecondary,
                size: 20,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: _buildContentTypeFilters(),
      ),
    );
  }

  Widget _buildContentTypeFilters() {
    return Row(
      children: [
        // Compact filter pills
        ...ContentFilter.values.map((filter) {
          final isActive = _currentFilter == filter;
          return GestureDetector(
            onTap: () {
              setState(() {
                _currentFilter = filter;
                _filterContent();
              });
            },
            child: Container(
              margin: const EdgeInsets.only(right: DesignTokens.spacingSm),
              padding: const EdgeInsets.symmetric(
                vertical: DesignTokens.spacingSm,
                horizontal: DesignTokens.spacingSm, // Reduced padding for compact pills
              ),
              decoration: BoxDecoration(
                color: isActive
                    ? DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.3)
                    : DesignTokens.backgroundCard.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                border: Border.all(
                  color: isActive
                      ? DesignTokens.primaryInteractiveBlue
                      : Colors.white.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Text(
                    filter.displayName,
                    style: DesignTokens.bodyStyle.copyWith(
                      color: isActive
                          ? DesignTokens.primaryInteractiveBlue
                          : DesignTokens.textSecondary,
                      fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
          );
        }).toList(),

        const Spacer(),

        // Sort dropdown
        _buildSortDropdown(),
      ],
    );
  }

  Widget _buildSortDropdown() {
    return GestureDetector(
      onTap: () {
        _showSortMenu();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: DesignTokens.spacingSm,
          horizontal: DesignTokens.spacingMd,
        ),
        decoration: BoxDecoration(
          color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Sort by',
                  style: DesignTokens.bodyStyle.copyWith(
                    color: DesignTokens.textSecondary,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(width: DesignTokens.spacingXs),
                const Icon(
                  Icons.keyboard_arrow_down,
                  color: DesignTokens.iconSecondary,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showSortMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(DesignTokens.spacingLg),
        decoration: const BoxDecoration(
          color: DesignTokens.backgroundApp,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(DesignTokens.borderRadiusXl),
            topRight: Radius.circular(DesignTokens.borderRadiusXl),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Sort Options',
              style: DesignTokens.cardTitleStyle,
            ),
            const SizedBox(height: DesignTokens.spacingLg),
            ...SortOption.values.map((option) {
              final isSelected = _currentSort == option;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _currentSort = option;
                    _filterContent();
                  });
                  Navigator.pop(context);
                },
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(DesignTokens.spacingMd),
                  margin: const EdgeInsets.only(bottom: DesignTokens.spacingSm),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? DesignTokens.primaryAccentBlue.withValues(alpha: 0.3)
                        : DesignTokens.backgroundCard.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                    border: Border.all(
                      color: isSelected
                          ? DesignTokens.primaryAccentBlue
                          : Colors.white.withValues(alpha: 0.1),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    option.displayName,
                    style: DesignTokens.bodyStyle.copyWith(
                      color: isSelected
                          ? DesignTokens.primaryAccentBlue
                          : DesignTokens.textPrimary,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }



  Widget _buildContentList() {
    if (_filteredContent.isEmpty) {
      return _buildEmptyState();
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final content = _filteredContent[index];
          return AnimatedBuilder(
            animation: _cardFadeAnimation,
            builder: (context, child) {
              final delay = index * 0.1;
              final animationValue = (_cardFadeAnimation.value - delay).clamp(0.0, 1.0);

              return Transform.translate(
                offset: Offset(0, 20 * (1 - animationValue)),
                child: Opacity(
                  opacity: animationValue,
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(
                      DesignTokens.spacingMd,
                      index == 0 ? DesignTokens.spacingSm : 0,
                      DesignTokens.spacingMd,
                      DesignTokens.spacingSm,
                    ),
                    child: ContentCard(
                      content: content,
                      isSelected: _selectedItems.contains(content.id),
                      isBulkSelectionMode: _isBulkSelectionMode,
                      onTap: () => _onCardTap(content),
                      onLongPress: () => _onCardLongPress(content),
                      onSelectionChanged: (selected) => _onCardSelectionChanged(content.id, selected),
                      onRetry: () => _retryProcessing(content),
                      onTagEdit: (newTag) => _updateContentTag(content.id, newTag),
                    ),
                  ),
                ),
              );
            },
          );
        },
        childCount: _filteredContent.length,
      ),
    );
  }

  Widget _buildEmptyState() {
    return SliverFillRemaining(
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(DesignTokens.spacingXl),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Your intelligent inbox awaits',
                style: DesignTokens.cardTitleStyle.copyWith(
                  fontSize: 20,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: DesignTokens.spacingLg),
              Text(
                'Share links and images from any app, or paste them below',
                style: DesignTokens.bodyStyle.copyWith(
                  color: DesignTokens.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onCardTap(CapturedContent content) {
    if (_isBulkSelectionMode) {
      _onCardSelectionChanged(content.id, !_selectedItems.contains(content.id));
    } else {
      _showContentDetail(content);
    }
  }

  void _onCardLongPress(CapturedContent content) {
    HapticFeedback.mediumImpact();
    setState(() {
      _isBulkSelectionMode = true;
      _selectedItems.add(content.id);
    });
  }

  void _onCardSelectionChanged(String contentId, bool selected) {
    setState(() {
      if (selected) {
        _selectedItems.add(contentId);
      } else {
        _selectedItems.remove(contentId);
        if (_selectedItems.isEmpty) {
          _isBulkSelectionMode = false;
        }
      }
    });
  }

  void _showContentDetail(CapturedContent content) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ContentDetailModal(
        content: content,
        onAddToNotes: () => _addToNotes(content),
        onChatWithDarvis: () => _chatWithDarvis(content),
        onDiscard: () => _discardContent(content),
      ),
    );
  }

  void _retryProcessing(CapturedContent content) {
    _processInput(content.url);
  }

  void _updateContentTag(String contentId, String newTag) {
    final index = _allContent.indexWhere((c) => c.id == contentId);
    if (index != -1) {
      setState(() {
        _allContent[index] = _allContent[index].copyWith(tag: newTag);
        _filterContent();
      });
    }
  }

  void _addToNotes(CapturedContent content) async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Import content to notes via API
      final success = await _apiService.importToNotes(
        contentId: content.id,
        noteTitle: content.title,
        noteContent: '${content.summary}\n\nSource: ${content.url}',
        tags: content.tag.isNotEmpty ? [content.tag] : null,
      );

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: DesignTokens.spacingSm),
                Expanded(
                  child: Text('Added "${content.title}" to Notes'),
                ),
              ],
            ),
            backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'View Notes',
              textColor: Colors.white,
              onPressed: () {
                // Navigate to notes screen
                GetIt.instance<NavigationService>().navigateToTab(1); // Notes tab
              },
            ),
          ),
        );
      } else {
        throw Exception('Failed to add to notes');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.white),
              const SizedBox(width: DesignTokens.spacingSm),
              const Expanded(
                child: Text('Failed to add to Notes. Please try again.'),
              ),
            ],
          ),
          backgroundColor: DesignTokens.priorityHigh,
          duration: const Duration(seconds: 3),
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
      Navigator.of(context).pop();
    }
  }

  void _chatWithDarvis(CapturedContent content) async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Add content to chat context via API
      final success = await _apiService.addToChatContext(
        contentId: content.id,
        contextMessage: 'I want to discuss this content: ${content.title}',
      );

      if (success && mounted) {
        // Navigate to chat screen
        GetIt.instance<NavigationService>().navigateToTab(0); // Chat tab

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.chat_bubble, color: Colors.white),
                const SizedBox(width: DesignTokens.spacingSm),
                Expanded(
                  child: Text('Opening chat about "${content.title}"'),
                ),
              ],
            ),
            backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
            duration: const Duration(seconds: 2),
          ),
        );
      } else if (mounted) {
        throw Exception('Failed to add to chat context');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.error_outline, color: Colors.white),
                SizedBox(width: DesignTokens.spacingSm),
                Expanded(
                  child: Text('Failed to open chat. Please try again.'),
                ),
              ],
            ),
            backgroundColor: DesignTokens.priorityHigh,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        Navigator.of(context).pop();
      }
    }
  }

  void _discardContent(CapturedContent content) {
    setState(() {
      _allContent.removeWhere((c) => c.id == content.id);
      _filterContent();
    });
    Navigator.of(context).pop();
  }

  Future<void> _pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        // Show processing message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: DesignTokens.spacingSm),
                Text('Processing image...'),
              ],
            ),
            backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
            duration: const Duration(seconds: 3),
          ),
        );

        // Process the image
        try {
          final processedContent = await _contentService.processImage(File(image.path));

          setState(() {
            _allContent.insert(0, processedContent);
            _filterContent();
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.white),
                    SizedBox(width: DesignTokens.spacingSm),
                    Text('Image processed successfully'),
                  ],
                ),
                backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
                duration: const Duration(seconds: 2),
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.white),
                    SizedBox(width: DesignTokens.spacingSm),
                    Text('Failed to process image'),
                  ],
                ),
                backgroundColor: DesignTokens.priorityHigh,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.error_outline, color: Colors.white),
                SizedBox(width: DesignTokens.spacingSm),
                Text('Failed to pick image'),
              ],
            ),
            backgroundColor: DesignTokens.priorityHigh,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _refreshContent() async {
    await Future.delayed(const Duration(milliseconds: 400));
    _loadExistingContent();
  }

  @override
  void dispose() {
    // Cancel stream subscriptions
    _textShareSubscription?.cancel();
    _imageShareSubscription?.cancel();

    // Dispose services
    try {
      _apiService.dispose();
      _shareIntentService.dispose();
    } catch (e) {
      print('Error disposing services: $e');
    }

    // Dispose controllers and focus nodes
    _pageAnimationController.dispose();
    _cardAnimationController.dispose();
    _inputController.dispose();
    _searchController.dispose();
    _inputFocusNode.dispose();
    _searchFocusNode.dispose();

    super.dispose();
  }
}


