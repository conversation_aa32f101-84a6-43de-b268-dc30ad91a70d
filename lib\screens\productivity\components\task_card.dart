import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:ui';
import '../../../utils/design_tokens.dart';
import '../models/task.dart';

class TaskCard extends StatefulWidget {
  final Task task;
  final VoidCallback onToggleComplete;
  final VoidCallback onTap;

  const TaskCard({
    super.key,
    required this.task,
    required this.onToggleComplete,
    required this.onTap,
  });

  @override
  State<TaskCard> createState() => _TaskCardState();
}

class _TaskCardState extends State<TaskCard>
    with TickerProviderStateMixin {
  late AnimationController _completionController;
  late Animation<double> _completionAnimation;
  late Animation<Color?> _completionColorAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _completionController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _completionAnimation = CurvedAnimation(
      parent: _completionController,
      curve: Curves.elasticOut,
    );

    _completionColorAnimation = ColorTween(
      begin: widget.task.category.color,
      end: Colors.green,
    ).animate(_completionController);

    if (widget.task.isCompleted) {
      _completionController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(TaskCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.task.isCompleted != oldWidget.task.isCompleted) {
      if (widget.task.isCompleted) {
        _completionController.forward();
      } else {
        _completionController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _completionAnimation,
      builder: (context, child) {
        return Container(
          margin: const EdgeInsets.only(bottom: DesignTokens.spacingMd),
          child: GestureDetector(
            onTap: widget.onTap,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Container(
                  padding: const EdgeInsets.all(DesignTokens.spacingMd),
                  decoration: BoxDecoration(
                    color: DesignTokens.backgroundCard.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      _buildCheckbox(),
                      const SizedBox(width: DesignTokens.spacingMd),
                      Expanded(
                        child: _buildTaskContent(),
                      ),
                      const SizedBox(width: DesignTokens.spacingMd),
                      _buildCategoryTag(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCheckbox() {
    return GestureDetector(
      onTap: () {
        widget.onToggleComplete();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: _completionColorAnimation.value ?? widget.task.category.color,
            width: 2,
          ),
          color: widget.task.isCompleted 
              ? (_completionColorAnimation.value ?? Colors.green)
              : Colors.transparent,
        ),
        child: widget.task.isCompleted
            ? Transform.scale(
                scale: _completionAnimation.value,
                child: const Icon(
                  Icons.check,
                  size: 16,
                  color: Colors.white,
                ),
              )
            : null,
      ),
    );
  }

  Widget _buildTaskContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.task.title,
          style: DesignTokens.bodyStyle.copyWith(
            color: widget.task.isCompleted 
                ? DesignTokens.textMuted 
                : DesignTokens.textPrimary,
            decoration: widget.task.isCompleted 
                ? TextDecoration.lineThrough 
                : null,
            fontWeight: FontWeight.w500,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        if (widget.task.dueDate != null) ...[
          const SizedBox(height: DesignTokens.spacingXs),
          Text(
            widget.task.dueDateLabel,
            style: DesignTokens.menuItemTextStyle.copyWith(
              color: widget.task.isOverdue && !widget.task.isCompleted
                  ? DesignTokens.priorityHigh
                  : DesignTokens.textSecondary,
            ),
          ),
        ],
        if (widget.task.description.isNotEmpty) ...[
          const SizedBox(height: DesignTokens.spacingXs),
          Text(
            widget.task.description,
            style: DesignTokens.menuItemTextStyle.copyWith(
              color: DesignTokens.textMuted,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildCategoryTag() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingMd,
        vertical: DesignTokens.spacingXs,
      ),
      decoration: BoxDecoration(
        color: widget.task.category.color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusXl),
        border: Border.all(
          color: widget.task.category.color.withOpacity(0.5),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            widget.task.category.name,
            style: DesignTokens.menuItemTextStyle.copyWith(
              color: widget.task.category.color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: DesignTokens.spacingXs),
          SvgPicture.asset(
            widget.task.category.iconPath,
            width: 12, // Reduced from 16 to 12
            height: 12, // Reduced from 16 to 12
            colorFilter: ColorFilter.mode(
              widget.task.category.color,
              BlendMode.srcIn,
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _completionController.dispose();
    super.dispose();
  }
}
