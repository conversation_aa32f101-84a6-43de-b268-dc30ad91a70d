import 'package:flutter_bloc/flutter_bloc.dart';
import '../../services/profile_service.dart';
import '../../models/profile_models.dart';
import 'profile_event.dart';
import 'profile_state.dart';

/// Profile BLoC for managing user profile and settings
class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  final ProfileService _profileService;

  ProfileBloc({
    required ProfileService profileService,
  }) : _profileService = profileService,
       super(const ProfileInitial()) {
    
    // Register event handlers
    on<LoadUserProfile>(_onLoadUserProfile);
    on<UpdateUserProfile>(_onUpdateUserProfile);
    on<UpdateDisplayName>(_onUpdateDisplayName);
    on<UpdateDarvisName>(_onUpdateDarvisName);
    on<ChangeProfilePicture>(_onChangeProfilePicture);
    on<DeleteProfilePicture>(_onDeleteProfilePicture);
    on<UpdateTimeZone>(_onUpdateTimeZone);
    on<UpdateLanguage>(_onUpdateLanguage);
    on<UpdateTheme>(_onUpdateTheme);
    on<UpdateBiometricAuth>(_onUpdateBiometricAuth);
    on<UpdateSessionTimeout>(_onUpdateSessionTimeout);
    on<UpdateAppLockRequired>(_onUpdateAppLockRequired);
    on<UpdateCloudSync>(_onUpdateCloudSync);
    on<UpdateAutoBackup>(_onUpdateAutoBackup);
    on<UpdateCurrentStreak>(_onUpdateCurrentStreak);
    on<UpdateSubscriptionPlan>(_onUpdateSubscriptionPlan);
    on<LoadProfilePictureHistory>(_onLoadProfilePictureHistory);
    on<SyncProfile>(_onSyncProfile);
    on<ResetProfile>(_onResetProfile);
    on<ExportProfileData>(_onExportProfileData);
    on<ImportProfileData>(_onImportProfileData);
    on<DeleteUserAccount>(_onDeleteUserAccount);
    on<BackupProfileData>(_onBackupProfileData);
    on<RestoreProfileData>(_onRestoreProfileData);
  }

  /// Load user profile
  Future<void> _onLoadUserProfile(
    LoadUserProfile event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      emit(const ProfileLoading());
      
      final profile = await _profileService.getUserProfile(event.userId);
      
      if (profile != null) {
        // Generate profile picture URLs
        final profilePictureUrl = _profileService.getProfilePictureUrl(
          publicId: profile.profilePicturePublicId,
        );
        
        final thumbnailUrl = _profileService.getThumbnailUrl(
          publicId: profile.profilePicturePublicId,
        );
        
        final responsiveUrls = _profileService.getResponsiveUrls(
          publicId: profile.profilePicturePublicId,
        );
        
        emit(ProfileLoaded(
          profile: profile,
          profilePictureUrl: profilePictureUrl,
          thumbnailUrl: thumbnailUrl,
          responsiveUrls: responsiveUrls,
        ));
      } else {
        // Create default profile
        final defaultProfile = UserProfile()
          ..userId = event.userId
          ..displayName = 'User'
          ..email = ''
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now()
          ..needsSync = true
          ..syncStatus = 'pending';
        
        final updatedProfile = await _profileService.updateUserProfile(defaultProfile);
        
        emit(ProfileLoaded(profile: updatedProfile));
      }
    } catch (e) {
      emit(ProfileError('Failed to load profile: $e'));
    }
  }

  /// Update user profile
  Future<void> _onUpdateUserProfile(
    UpdateUserProfile event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      emit(const ProfileUpdating('profile'));
      
      final updatedProfile = await _profileService.updateUserProfile(event.profile);
      
      emit(ProfileUpdated(
        profile: updatedProfile,
        message: 'Profile updated successfully',
      ));
      
      // Reload profile to get updated URLs
      add(LoadUserProfile(event.profile.userId));
    } catch (e) {
      emit(ProfileUpdateError(e.toString()));
    }
  }

  /// Update display name
  Future<void> _onUpdateDisplayName(
    UpdateDisplayName event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      emit(const ProfileUpdating('display_name'));
      
      final profile = await _profileService.getUserProfile(event.userId);
      if (profile != null) {
        profile.displayName = event.displayName;
        await _profileService.updateUserProfile(profile);
        
        emit(ProfileSettingUpdated(
          settingName: 'Display Name',
          newValue: event.displayName,
        ));
        
        add(LoadUserProfile(event.userId));
      }
    } catch (e) {
      emit(ProfileUpdateError(e.toString()));
    }
  }

  /// Update Darvis name
  Future<void> _onUpdateDarvisName(
    UpdateDarvisName event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      emit(const ProfileUpdating('darvis_name'));
      
      final profile = await _profileService.getUserProfile(event.userId);
      if (profile != null) {
        profile.darvisName = event.darvisName;
        await _profileService.updateUserProfile(profile);
        
        emit(ProfileSettingUpdated(
          settingName: 'Darvis Name',
          newValue: event.darvisName,
        ));
        
        add(LoadUserProfile(event.userId));
      }
    } catch (e) {
      emit(ProfileUpdateError(e.toString()));
    }
  }

  /// Change profile picture
  Future<void> _onChangeProfilePicture(
    ChangeProfilePicture event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      emit(const ProfilePictureChanging(
        uploadState: ProfilePictureUploadState.selecting,
      ));
      
      emit(const ProfilePictureChanging(
        uploadState: ProfilePictureUploadState.uploading,
        progress: 0.0,
      ));
      
      final newPictureUrl = await _profileService.changeProfilePicture(
        userId: event.userId,
        source: event.source,
      );
      
      if (newPictureUrl != null) {
        // Get updated profile to get public ID
        final profile = await _profileService.getUserProfile(event.userId);
        
        final thumbnailUrl = _profileService.getThumbnailUrl(
          publicId: profile?.profilePicturePublicId,
        );
        
        final responsiveUrls = _profileService.getResponsiveUrls(
          publicId: profile?.profilePicturePublicId,
        );
        
        emit(ProfilePictureChanged(
          newPictureUrl: newPictureUrl,
          thumbnailUrl: thumbnailUrl,
          responsiveUrls: responsiveUrls,
        ));
        
        emit(const ProfileOperationSuccess('Profile picture updated successfully'));
        
        // Reload profile
        add(LoadUserProfile(event.userId));
      } else {
        emit(const ProfilePictureUploadError('No image selected'));
      }
    } catch (e) {
      emit(ProfilePictureUploadError(e.toString()));
    }
  }

  /// Delete profile picture
  Future<void> _onDeleteProfilePicture(
    DeleteProfilePicture event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      emit(const ProfileUpdating('profile_picture'));
      
      final success = await _profileService.deleteProfilePicture(event.userId);
      
      if (success) {
        emit(const ProfilePictureDeleted());
        emit(const ProfileOperationSuccess('Profile picture deleted successfully'));
        
        // Reload profile
        add(LoadUserProfile(event.userId));
      } else {
        emit(const ProfilePictureDeleteError('Failed to delete profile picture'));
      }
    } catch (e) {
      emit(ProfilePictureDeleteError(e.toString()));
    }
  }

  /// Update time zone
  Future<void> _onUpdateTimeZone(
    UpdateTimeZone event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      await _updateProfileField(event.userId, (profile) {
        profile.timeZone = event.timeZone;
      });
      
      emit(ProfileSettingUpdated(
        settingName: 'Time Zone',
        newValue: event.timeZone,
      ));
      
      add(LoadUserProfile(event.userId));
    } catch (e) {
      emit(ProfileUpdateError(e.toString()));
    }
  }

  /// Update language
  Future<void> _onUpdateLanguage(
    UpdateLanguage event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      await _updateProfileField(event.userId, (profile) {
        profile.language = event.language;
      });
      
      emit(ProfileSettingUpdated(
        settingName: 'Language',
        newValue: event.language,
      ));
      
      add(LoadUserProfile(event.userId));
    } catch (e) {
      emit(ProfileUpdateError(e.toString()));
    }
  }

  /// Update theme
  Future<void> _onUpdateTheme(
    UpdateTheme event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      await _updateProfileField(event.userId, (profile) {
        profile.theme = event.theme;
      });
      
      emit(ProfileSettingUpdated(
        settingName: 'Theme',
        newValue: event.theme,
      ));
      
      add(LoadUserProfile(event.userId));
    } catch (e) {
      emit(ProfileUpdateError(e.toString()));
    }
  }

  /// Update biometric authentication
  Future<void> _onUpdateBiometricAuth(
    UpdateBiometricAuth event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      await _updateProfileField(event.userId, (profile) {
        profile.biometricAuthEnabled = event.enabled;
      });

      emit(ProfileSettingUpdated(
        settingName: 'Biometric Authentication',
        newValue: event.enabled,
      ));

      add(LoadUserProfile(event.userId));
    } catch (e) {
      emit(ProfileUpdateError(e.toString()));
    }
  }

  /// Helper method to update profile field
  Future<void> _updateProfileField(
    String userId,
    void Function(UserProfile) updateFunction,
  ) async {
    final profile = await _profileService.getUserProfile(userId);
    if (profile != null) {
      updateFunction(profile);
      await _profileService.updateUserProfile(profile);
    }
  }

  // Placeholder implementations for remaining handlers
  Future<void> _onUpdateSessionTimeout(UpdateSessionTimeout event, Emitter<ProfileState> emit) async {
    try {
      await _updateProfileField(event.userId, (profile) {
        profile.sessionTimeout = event.timeout;
      });
      emit(ProfileSettingUpdated(settingName: 'Session Timeout', newValue: event.timeout));
      add(LoadUserProfile(event.userId));
    } catch (e) {
      emit(ProfileUpdateError(e.toString()));
    }
  }

  Future<void> _onUpdateAppLockRequired(UpdateAppLockRequired event, Emitter<ProfileState> emit) async {
    try {
      await _updateProfileField(event.userId, (profile) {
        profile.appLockRequired = event.required;
      });
      emit(ProfileSettingUpdated(settingName: 'App Lock Required', newValue: event.required));
      add(LoadUserProfile(event.userId));
    } catch (e) {
      emit(ProfileUpdateError(e.toString()));
    }
  }

  Future<void> _onUpdateCloudSync(UpdateCloudSync event, Emitter<ProfileState> emit) async {
    try {
      await _updateProfileField(event.userId, (profile) {
        profile.cloudSyncEnabled = event.enabled;
      });
      emit(ProfileSettingUpdated(settingName: 'Cloud Sync', newValue: event.enabled));
      add(LoadUserProfile(event.userId));
    } catch (e) {
      emit(ProfileUpdateError(e.toString()));
    }
  }

  Future<void> _onUpdateAutoBackup(UpdateAutoBackup event, Emitter<ProfileState> emit) async {
    try {
      await _updateProfileField(event.userId, (profile) {
        profile.autoBackupEnabled = event.enabled;
      });
      emit(ProfileSettingUpdated(settingName: 'Auto Backup', newValue: event.enabled));
      add(LoadUserProfile(event.userId));
    } catch (e) {
      emit(ProfileUpdateError(e.toString()));
    }
  }

  Future<void> _onUpdateCurrentStreak(UpdateCurrentStreak event, Emitter<ProfileState> emit) async {
    try {
      final profile = await _profileService.getUserProfile(event.userId);
      if (profile != null) {
        final isNewRecord = event.streak > profile.currentStreak;
        profile.currentStreak = event.streak;
        profile.lastActivityDate = DateTime.now();
        await _profileService.updateUserProfile(profile);

        emit(StreakUpdated(newStreak: event.streak, isNewRecord: isNewRecord));
        add(LoadUserProfile(event.userId));
      }
    } catch (e) {
      emit(ProfileUpdateError(e.toString()));
    }
  }

  Future<void> _onUpdateSubscriptionPlan(UpdateSubscriptionPlan event, Emitter<ProfileState> emit) async {
    try {
      await _updateProfileField(event.userId, (profile) {
        profile.subscriptionPlan = event.plan;
        profile.subscriptionExpiryDate = event.expiryDate;
        profile.isSubscriptionActive = event.isActive;
      });

      emit(SubscriptionUpdated(
        plan: event.plan,
        expiryDate: event.expiryDate,
        isActive: event.isActive,
      ));
      add(LoadUserProfile(event.userId));
    } catch (e) {
      emit(ProfileUpdateError(e.toString()));
    }
  }

  Future<void> _onLoadProfilePictureHistory(LoadProfilePictureHistory event, Emitter<ProfileState> emit) async {
    try {
      final history = await _profileService.getProfilePictureHistory(event.userId);
      emit(ProfilePictureHistoryLoaded(history));
    } catch (e) {
      emit(ProfileError('Failed to load profile picture history: $e'));
    }
  }

  Future<void> _onSyncProfile(SyncProfile event, Emitter<ProfileState> emit) async {
    try {
      emit(const ProfileSyncing());
      // Sync logic would be handled by the profile service
      emit(ProfileSynced(DateTime.now()));
    } catch (e) {
      emit(ProfileSyncError(e.toString()));
    }
  }

  // Placeholder implementations for remaining complex handlers
  Future<void> _onResetProfile(ResetProfile event, Emitter<ProfileState> emit) async {
    emit(const ProfileError('Reset profile not implemented yet'));
  }

  Future<void> _onExportProfileData(ExportProfileData event, Emitter<ProfileState> emit) async {
    emit(const ProfileError('Export profile data not implemented yet'));
  }

  Future<void> _onImportProfileData(ImportProfileData event, Emitter<ProfileState> emit) async {
    emit(const ProfileError('Import profile data not implemented yet'));
  }

  Future<void> _onDeleteUserAccount(DeleteUserAccount event, Emitter<ProfileState> emit) async {
    emit(const ProfileError('Delete user account not implemented yet'));
  }

  Future<void> _onBackupProfileData(BackupProfileData event, Emitter<ProfileState> emit) async {
    emit(const ProfileError('Backup profile data not implemented yet'));
  }

  Future<void> _onRestoreProfileData(RestoreProfileData event, Emitter<ProfileState> emit) async {
    emit(const ProfileError('Restore profile data not implemented yet'));
  }
}
