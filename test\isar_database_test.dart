import 'package:flutter_test/flutter_test.dart';
import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

import 'package:darvis_app/models/isar_models.dart';

void main() {
  group('Isar Database Tests', () {
    late Isar isar;
    late Directory tempDir;

    setUp(() async {
      // Create a temporary directory for test database
      tempDir = await Directory.systemTemp.createTemp('isar_test_');
      
      // Open Isar database for testing
      isar = await Isar.open(
        [
          LocalTaskSchema,
          LocalNoteSchema,
          LocalUserSchema,
          LocalSyncOperationSchema,
          LocalDashboardCacheSchema,
        ],
        directory: tempDir.path,
      );
    });

    tearDown(() async {
      // Close database and clean up
      await isar.close();
      await tempDir.delete(recursive: true);
    });

    group('LocalTask Operations', () {
      test('should create and retrieve a task', () async {
        // Arrange
        final task = LocalTask()
          ..title = 'Test Task'
          ..description = 'Test Description'
          ..isCompleted = false
          ..priority = 'high'
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now()
          ..needsSync = true
          ..syncStatus = 'pending';

        // Act
        await isar.writeTxn(() async {
          await isar.localTasks.put(task);
        });

        final retrievedTask = await isar.localTasks.get(task.id);

        // Assert
        expect(retrievedTask, isNotNull);
        expect(retrievedTask!.title, 'Test Task');
        expect(retrievedTask.description, 'Test Description');
        expect(retrievedTask.isCompleted, false);
        expect(retrievedTask.priority, 'high');
        expect(retrievedTask.needsSync, true);
        expect(retrievedTask.syncStatus, 'pending');
      });

      test('should update a task', () async {
        // Arrange
        final task = LocalTask()
          ..title = 'Original Title'
          ..description = 'Original Description'
          ..isCompleted = false
          ..priority = 'low'
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now()
          ..needsSync = false
          ..syncStatus = 'synced';

        await isar.writeTxn(() async {
          await isar.localTasks.put(task);
        });

        // Act
        task.title = 'Updated Title';
        task.isCompleted = true;
        task.priority = 'high';
        task.needsSync = true;
        task.syncStatus = 'pending';
        task.updatedAt = DateTime.now();

        await isar.writeTxn(() async {
          await isar.localTasks.put(task);
        });

        final updatedTask = await isar.localTasks.get(task.id);

        // Assert
        expect(updatedTask!.title, 'Updated Title');
        expect(updatedTask.isCompleted, true);
        expect(updatedTask.priority, 'high');
        expect(updatedTask.needsSync, true);
        expect(updatedTask.syncStatus, 'pending');
      });

      test('should delete a task', () async {
        // Arrange
        final task = LocalTask()
          ..title = 'Task to Delete'
          ..description = 'This will be deleted'
          ..isCompleted = false
          ..priority = 'medium'
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now()
          ..needsSync = false
          ..syncStatus = 'synced';

        await isar.writeTxn(() async {
          await isar.localTasks.put(task);
        });

        // Act
        await isar.writeTxn(() async {
          await isar.localTasks.delete(task.id);
        });

        final deletedTask = await isar.localTasks.get(task.id);

        // Assert
        expect(deletedTask, isNull);
      });

      test('should query tasks by sync status', () async {
        // Arrange
        final tasks = [
          LocalTask()
            ..title = 'Synced Task'
            ..description = 'Already synced'
            ..isCompleted = false
            ..priority = 'low'
            ..createdAt = DateTime.now()
            ..updatedAt = DateTime.now()
            ..needsSync = false
            ..syncStatus = 'synced',
          LocalTask()
            ..title = 'Pending Task'
            ..description = 'Needs sync'
            ..isCompleted = false
            ..priority = 'high'
            ..createdAt = DateTime.now()
            ..updatedAt = DateTime.now()
            ..needsSync = true
            ..syncStatus = 'pending',
        ];

        await isar.writeTxn(() async {
          await isar.localTasks.putAll(tasks);
        });

        // Act
        final pendingTasks = await isar.localTasks
            .filter()
            .syncStatusEqualTo('pending')
            .findAll();

        final syncedTasks = await isar.localTasks
            .filter()
            .syncStatusEqualTo('synced')
            .findAll();

        // Assert
        expect(pendingTasks.length, 1);
        expect(pendingTasks.first.title, 'Pending Task');
        expect(syncedTasks.length, 1);
        expect(syncedTasks.first.title, 'Synced Task');
      });
    });

    group('LocalNote Operations', () {
      test('should create and retrieve a note', () async {
        // Arrange
        final note = LocalNote()
          ..title = 'Test Note'
          ..content = 'This is a test note content'
          ..isPinned = false
          ..tags = ['work', 'important']
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now()
          ..needsSync = true
          ..syncStatus = 'pending';

        // Act
        await isar.writeTxn(() async {
          await isar.localNotes.put(note);
        });

        final retrievedNote = await isar.localNotes.get(note.id);

        // Assert
        expect(retrievedNote, isNotNull);
        expect(retrievedNote!.title, 'Test Note');
        expect(retrievedNote.content, 'This is a test note content');
        expect(retrievedNote.isPinned, false);
        expect(retrievedNote.tags, ['work', 'important']);
        expect(retrievedNote.needsSync, true);
        expect(retrievedNote.syncStatus, 'pending');
      });

      test('should query notes by tags', () async {
        // Arrange
        final notes = [
          LocalNote()
            ..title = 'Work Note'
            ..content = 'Work related content'
            ..isPinned = false
            ..tags = ['work', 'project']
            ..createdAt = DateTime.now()
            ..updatedAt = DateTime.now()
            ..needsSync = false
            ..syncStatus = 'synced',
          LocalNote()
            ..title = 'Personal Note'
            ..content = 'Personal content'
            ..isPinned = true
            ..tags = ['personal', 'reminder']
            ..createdAt = DateTime.now()
            ..updatedAt = DateTime.now()
            ..needsSync = false
            ..syncStatus = 'synced',
        ];

        await isar.writeTxn(() async {
          await isar.localNotes.putAll(notes);
        });

        // Act
        final workNotes = await isar.localNotes
            .filter()
            .tagsElementContains('work')
            .findAll();

        final personalNotes = await isar.localNotes
            .filter()
            .tagsElementContains('personal')
            .findAll();

        // Assert
        expect(workNotes.length, 1);
        expect(workNotes.first.title, 'Work Note');
        expect(personalNotes.length, 1);
        expect(personalNotes.first.title, 'Personal Note');
      });
    });

    group('LocalSyncOperation Operations', () {
      test('should create and retrieve sync operations', () async {
        // Arrange
        final syncOp = LocalSyncOperation()
          ..operationType = 'create'
          ..entityType = 'task'
          ..localId = 'local_123'
          ..dataJson = '{"title": "Test Task", "completed": false}'
          ..timestamp = DateTime.now()
          ..status = 'pending';

        // Act
        await isar.writeTxn(() async {
          await isar.localSyncOperations.put(syncOp);
        });

        final retrievedOp = await isar.localSyncOperations.get(syncOp.id);

        // Assert
        expect(retrievedOp, isNotNull);
        expect(retrievedOp!.operationType, 'create');
        expect(retrievedOp.entityType, 'task');
        expect(retrievedOp.localId, 'local_123');
        expect(retrievedOp.status, 'pending');
      });

      test('should query pending sync operations', () async {
        // Arrange
        final operations = [
          LocalSyncOperation()
            ..operationType = 'create'
            ..entityType = 'task'
            ..dataJson = '{}'
            ..timestamp = DateTime.now()
            ..status = 'pending',
          LocalSyncOperation()
            ..operationType = 'update'
            ..entityType = 'note'
            ..dataJson = '{}'
            ..timestamp = DateTime.now()
            ..status = 'completed',
        ];

        await isar.writeTxn(() async {
          await isar.localSyncOperations.putAll(operations);
        });

        // Act
        final pendingOps = await isar.localSyncOperations
            .filter()
            .statusEqualTo('pending')
            .findAll();

        // Assert
        expect(pendingOps.length, 1);
        expect(pendingOps.first.operationType, 'create');
        expect(pendingOps.first.entityType, 'task');
      });
    });

    group('LocalDashboardCache Operations', () {
      test('should cache and retrieve dashboard data', () async {
        // Arrange
        final now = DateTime.now();
        final cache = LocalDashboardCache()
          ..dataJson = '{"tasks": [], "notes": [], "events": []}'
          ..cachedAt = now
          ..expiresAt = now.add(const Duration(hours: 1))
          ..cacheKey = 'dashboard_data';

        // Act
        await isar.writeTxn(() async {
          await isar.localDashboardCaches.put(cache);
        });

        final retrievedCache = await isar.localDashboardCaches
            .filter()
            .cacheKeyEqualTo('dashboard_data')
            .findFirst();

        // Assert
        expect(retrievedCache, isNotNull);
        expect(retrievedCache!.dataJson, '{"tasks": [], "notes": [], "events": []}');
        expect(retrievedCache.cacheKey, 'dashboard_data');
      });
    });
  });
}
