import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;
import '../../utils/design_tokens.dart';

class ProductivityAnalyticsScreen extends StatefulWidget {
  const ProductivityAnalyticsScreen({super.key});

  @override
  State<ProductivityAnalyticsScreen> createState() => _ProductivityAnalyticsScreenState();
}

class _ProductivityAnalyticsScreenState extends State<ProductivityAnalyticsScreen>
    with TickerProviderStateMixin {
  
  late AnimationController _chartAnimationController;
  late AnimationController _cardAnimationController;
  late Animation<double> _chartAnimation;
  late Animation<double> _cardAnimation;
  
  String _selectedTimePeriod = 'Week';
  final List<String> _timePeriods = ['Week', 'Month', 'Quarter'];
  
  // Current data that changes based on selected period
  List<double> _currentChartData = [];
  List<double> _currentCompletionData = [];
  List<String> _currentLabels = [];
  String _currentSummaryText = '';
  String _currentCompletionText = '';
  
  // Structured dummy data for each period
  final Map<String, Map<String, dynamic>> _analyticsData = {
    'Week': {
      'chartData': [65.0, 78.0, 85.0, 72.0, 90.0, 68.0, 75.0],
      'completionData': [85.0, 92.0, 78.0, 88.0, 95.0, 82.0, 89.0],
      'labels': ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      'summaryText': 'This week: 12 tasks completed, 5 notes created\nYour most productive day: Tuesday\nPeak productivity: 10-12am daily',
      'completionText': 'Task completion rate: 87% this week\n↑ 15% improvement from last week',
    },
    'Month': {
      'chartData': [45.0, 62.0, 78.0, 85.0, 72.0, 90.0, 68.0, 75.0, 88.0, 92.0, 95.0, 89.0, 82.0, 76.0, 83.0, 79.0, 86.0, 91.0, 88.0, 84.0, 77.0, 81.0, 85.0, 93.0, 87.0, 79.0, 82.0, 88.0, 90.0, 85.0],
      'completionData': [75.0, 82.0, 88.0, 95.0, 89.0, 92.0, 86.0, 83.0, 87.0, 90.0, 94.0, 91.0, 85.0, 79.0, 84.0, 88.0, 92.0, 89.0, 86.0, 83.0, 87.0, 91.0, 94.0, 88.0, 85.0, 82.0, 86.0, 89.0, 92.0, 87.0],
      'labels': ['1', '3', '5', '7', '9', '11', '13', '15', '17', '19', '21', '23', '25', '27', '29', '31'],
      'summaryText': 'This month: 45 tasks completed, 18 notes created\nYour most productive week: Week 3\nPeak productivity: Tuesday-Thursday',
      'completionText': 'Task completion rate: 89% this month\n↑ 12% improvement from last month',
    },
    'Quarter': {
      'chartData': [55.0, 68.0, 72.0, 78.0, 85.0, 89.0, 92.0, 87.0, 83.0, 79.0, 82.0, 88.0],
      'completionData': [70.0, 75.0, 82.0, 85.0, 89.0, 92.0, 95.0, 91.0, 88.0, 84.0, 86.0, 90.0],
      'labels': ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      'summaryText': 'This quarter: 156 tasks completed, 67 notes created\nYour most productive month: May\nPeak productivity: Mid-week patterns',
      'completionText': 'Task completion rate: 91% this quarter\n↑ 8% improvement from last quarter',
    },
  };
  
  // AI Insights dummy data
  final List<String> _aiInsights = [
    "Darvis noticed you're most productive on Tuesday mornings",
    "Your task completion rate increases 40% after creating notes",
    "You work best in 2-hour focused blocks",
    "Try scheduling important tasks between 10-12am",
    "Based on your patterns, take breaks every 90 minutes",
    "Your 'Work' category tasks are 60% more likely to be completed in the morning",
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadDataForPeriod(_selectedTimePeriod);
  }

  void _initializeAnimations() {
    _chartAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _chartAnimation = CurvedAnimation(
      parent: _chartAnimationController,
      curve: Curves.easeOut,
    );
    
    _cardAnimation = CurvedAnimation(
      parent: _cardAnimationController,
      curve: Curves.easeOut,
    );
    
    // Start animations
    _chartAnimationController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _cardAnimationController.forward();
    });
  }

  void _loadDataForPeriod(String period) {
    final data = _analyticsData[period]!;
    setState(() {
      _currentChartData = List<double>.from(data['chartData']);
      _currentCompletionData = List<double>.from(data['completionData']);
      _currentLabels = List<String>.from(data['labels']);
      _currentSummaryText = data['summaryText'];
      _currentCompletionText = data['completionText'];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _refreshData,
          backgroundColor: DesignTokens.backgroundCard,
          color: DesignTokens.primaryInteractiveBlue,
          child: CustomScrollView(
            slivers: [
              _buildHeader(),
              _buildTagline(),
              _buildWeeklySummariesSection(),
              _buildDarvisInsightsSection(),
              _buildCompletionStatsSection(),
              _buildGoalTrackingSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return SliverToBoxAdapter(
      child: Container(
        height: 90,
        padding: const EdgeInsets.symmetric(
          vertical: DesignTokens.spacingMd,
        ),
        child: Row(
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back, color: DesignTokens.iconPrimary),
              onPressed: () => Navigator.of(context).pop(),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingLg),
                child: ShaderMask(
                  shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
                  child: Text(
                    'Productivity',
                    style: DesignTokens.therapySessionTitleStyle.copyWith(
                      color: Colors.white,
                      fontSize: 32,
                      fontWeight: FontWeight.w700,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 48),
          ],
        ),
      ),
    );
  }

  Widget _buildTagline() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
        child: Text(
          'Your thoughts, tasks, and ideas, all in one place',
          style: DesignTokens.bodyStyle.copyWith(
            color: DesignTokens.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildWeeklySummariesSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: DesignTokens.spacingXl),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Weekly Summaries',
                  style: DesignTokens.cardTitleStyle,
                ),
                _buildTimePeriodSelector(),
              ],
            ),
            const SizedBox(height: DesignTokens.spacingSm),
            _buildWeeklyChart(),
            const SizedBox(height: DesignTokens.spacingMd),
            Text(
              _currentSummaryText,
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textSecondary,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimePeriodSelector() {
    return Container(
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: _timePeriods.map((period) {
              final isSelected = _selectedTimePeriod == period;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedTimePeriod = period;
                  });
                  _loadDataForPeriod(period);
                  // Restart animations for smooth transition
                  _chartAnimationController.reset();
                  _chartAnimationController.forward();
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: DesignTokens.spacingMd,
                    vertical: DesignTokens.spacingSm,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.3)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
                  ),
                  child: Text(
                    period,
                    style: DesignTokens.bodyStyle.copyWith(
                      color: isSelected 
                          ? DesignTokens.primaryInteractiveBlue
                          : DesignTokens.textSecondary,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildWeeklyChart() {
    return AnimatedBuilder(
      animation: _chartAnimation,
      child: Container(
        height: 180,
        decoration: BoxDecoration(
          color: DesignTokens.backgroundCard.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.08),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Padding(
              padding: const EdgeInsets.all(DesignTokens.spacingMd),
              child: CustomPaint(
                painter: WeeklyChartPainter(
                  data: _currentChartData,
                  labels: _currentLabels,
                  animationValue: _chartAnimation.value,
                ),
                size: const Size(double.infinity, 140),
              ),
            ),
          ),
        ),
      ),
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * _chartAnimation.value),
          child: Opacity(
            opacity: _chartAnimation.value,
            child: child,
          ),
        );
      },
    );
  }

  Future<void> _refreshData() async {
    await Future.delayed(const Duration(milliseconds: 300));
    _chartAnimationController.reset();
    _cardAnimationController.reset();
    _chartAnimationController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _cardAnimationController.forward();
    });
  }

  Widget _buildDarvisInsightsSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: DesignTokens.spacingXl),
            const Text(
              'Darvis Insights',
              style: DesignTokens.cardTitleStyle,
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            AnimatedBuilder(
              animation: _cardAnimation,
              builder: (context, child) {
                return Column(
                  children: List.generate(_aiInsights.length, (index) {
                    return Transform.translate(
                      offset: Offset(0, 20 * (1 - _cardAnimation.value)),
                      child: Opacity(
                        opacity: _cardAnimation.value,
                        child: Container(
                          margin: const EdgeInsets.only(bottom: DesignTokens.spacingSm),
                          child: _buildInsightCard(_aiInsights[index], index),
                        ),
                      ),
                    );
                  }),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInsightCard(String insight, int index) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Row(
            children: [
              Container(
                width: 4,
                height: 40,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      DesignTokens.primaryInteractiveBlue,
                      const Color(0xFF10B981),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: DesignTokens.spacingMd),
              Expanded(
                child: Text(
                  insight,
                  style: DesignTokens.bodyStyle.copyWith(
                    color: DesignTokens.textPrimary,
                    height: 1.4,
                  ),
                ),
              ),
              Icon(
                Icons.lightbulb_outline,
                color: DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.7),
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompletionStatsSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: DesignTokens.spacingXl),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Completion Stats',
                  style: DesignTokens.cardTitleStyle,
                ),
                _buildTimePeriodSelector(),
              ],
            ),
            const SizedBox(height: DesignTokens.spacingSm),
            _buildCompletionChart(),
            const SizedBox(height: DesignTokens.spacingMd),
            Text(
              _currentCompletionText,
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textSecondary,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompletionChart() {
    return AnimatedBuilder(
      animation: _chartAnimation,
      child: Container(
        height: 180,
        decoration: BoxDecoration(
          color: DesignTokens.backgroundCard.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.08),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Padding(
              padding: const EdgeInsets.all(DesignTokens.spacingMd),
              child: CustomPaint(
                painter: CompletionChartPainter(
                  data: _currentCompletionData,
                  labels: _currentLabels,
                  animationValue: _chartAnimation.value,
                ),
                size: const Size(double.infinity, 140),
              ),
            ),
          ),
        ),
      ),
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * _chartAnimation.value),
          child: Opacity(
            opacity: _chartAnimation.value,
            child: child,
          ),
        );
      },
    );
  }

  Widget _buildGoalTrackingSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: DesignTokens.spacingXl),
            const Text(
              'Weekly Goals',
              style: DesignTokens.cardTitleStyle,
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            AnimatedBuilder(
              animation: _cardAnimation,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, 20 * (1 - _cardAnimation.value)),
                  child: Opacity(
                    opacity: _cardAnimation.value,
                    child: _buildGoalCard(),
                  ),
                );
              },
            ),
            const SizedBox(height: DesignTokens.spacingXl),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(DesignTokens.spacingLg),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.12), // Neutral shadow to avoid green flash
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Row(
            children: [
              // Progress rings
              SizedBox(
                width: 80,
                height: 80,
                child: Stack(
                  children: [
                    // Tasks progress ring
                    CustomPaint(
                      painter: ProgressRingPainter(
                        progress: 0.8,
                        color: const Color(0xFF10B981),
                        strokeWidth: 6,
                      ),
                      size: const Size(80, 80),
                    ),
                    // Notes progress ring
                    Padding(
                      padding: const EdgeInsets.all(12),
                      child: CustomPaint(
                        painter: ProgressRingPainter(
                          progress: 0.6,
                          color: DesignTokens.primaryInteractiveBlue,
                          strokeWidth: 4,
                        ),
                        size: const Size(56, 56),
                      ),
                    ),
                    // Center text
                    Center(
                      child: Text(
                        '80%',
                        style: DesignTokens.cardTitleStyle.copyWith(
                          fontSize: 16,
                          color: DesignTokens.textPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: DesignTokens.spacingLg),
              // Goal text
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'This week: 8/10 tasks goal',
                      style: DesignTokens.cardTitleStyle.copyWith(
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: DesignTokens.spacingSm),
                    Text(
                      '5-day productivity streak!\nYou\'re on track for your weekly goal',
                      style: DesignTokens.bodyStyle.copyWith(
                        color: DesignTokens.textSecondary,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _chartAnimationController.dispose();
    _cardAnimationController.dispose();
    super.dispose();
  }
}

// Custom painter for weekly chart
class WeeklyChartPainter extends CustomPainter {
  final List<double> data;
  final List<String> labels;
  final double animationValue;

  WeeklyChartPainter({
    required this.data,
    required this.labels,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    final fillPaint = Paint()
      ..style = PaintingStyle.fill;

    // Create gradient
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.6),
        const Color(0xFF10B981).withValues(alpha: 0.1),
      ],
    );

    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    fillPaint.shader = gradient.createShader(rect);

    // Calculate points
    final maxValue = data.reduce(math.max);
    final minValue = data.reduce(math.min);
    final range = maxValue - minValue;

    final stepX = size.width / (data.length - 1);
    final chartHeight = size.height - 40; // Leave space for labels

    final points = <Offset>[];
    for (int i = 0; i < data.length; i++) {
      final x = i * stepX;
      final normalizedValue = (data[i] - minValue) / range;
      final y = chartHeight - (normalizedValue * chartHeight * 0.8) - 20;
      points.add(Offset(x, y * animationValue + (chartHeight * (1 - animationValue))));
    }

    // Draw gradient fill
    final path = Path();
    if (points.isNotEmpty) {
      path.moveTo(points.first.dx, size.height - 20);
      for (final point in points) {
        path.lineTo(point.dx, point.dy);
      }
      path.lineTo(points.last.dx, size.height - 20);
      path.close();
      canvas.drawPath(path, fillPaint);
    }

    // Draw line
    paint.color = DesignTokens.primaryInteractiveBlue;
    for (int i = 0; i < points.length - 1; i++) {
      canvas.drawLine(points[i], points[i + 1], paint);
    }

    // Draw points
    final pointPaint = Paint()
      ..color = DesignTokens.primaryInteractiveBlue
      ..style = PaintingStyle.fill;

    for (final point in points) {
      canvas.drawCircle(point, 4, pointPaint);
    }

    // Draw labels
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    for (int i = 0; i < labels.length; i++) {
      textPainter.text = TextSpan(
        text: labels[i],
        style: DesignTokens.bodyStyle.copyWith(
          color: DesignTokens.textMuted,
          fontSize: 12,
        ),
      );
      textPainter.layout();

      final x = i * stepX - textPainter.width / 2;
      final y = size.height - 15;
      textPainter.paint(canvas, Offset(x, y));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Custom painter for completion chart
class CompletionChartPainter extends CustomPainter {
  final List<double> data;
  final List<String> labels;
  final double animationValue;

  CompletionChartPainter({
    required this.data,
    required this.labels,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    final fillPaint = Paint()
      ..style = PaintingStyle.fill;

    // Create gradient
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xFF10B981).withValues(alpha: 0.6),
        DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.1),
      ],
    );

    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    fillPaint.shader = gradient.createShader(rect);

    // Calculate points
    final stepX = size.width / (data.length - 1);
    final chartHeight = size.height - 40;

    final points = <Offset>[];
    for (int i = 0; i < data.length; i++) {
      final x = i * stepX;
      final normalizedValue = data[i] / 100; // Percentage data
      final y = chartHeight - (normalizedValue * chartHeight * 0.8) - 20;
      points.add(Offset(x, y * animationValue + (chartHeight * (1 - animationValue))));
    }

    // Draw gradient fill
    final path = Path();
    if (points.isNotEmpty) {
      path.moveTo(points.first.dx, size.height - 20);
      for (final point in points) {
        path.lineTo(point.dx, point.dy);
      }
      path.lineTo(points.last.dx, size.height - 20);
      path.close();
      canvas.drawPath(path, fillPaint);
    }

    // Draw line
    paint.color = const Color(0xFF10B981);
    for (int i = 0; i < points.length - 1; i++) {
      canvas.drawLine(points[i], points[i + 1], paint);
    }

    // Draw points
    final pointPaint = Paint()
      ..color = const Color(0xFF10B981)
      ..style = PaintingStyle.fill;

    for (final point in points) {
      canvas.drawCircle(point, 4, pointPaint);
    }

    // Draw labels
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    for (int i = 0; i < labels.length; i++) {
      textPainter.text = TextSpan(
        text: labels[i],
        style: DesignTokens.bodyStyle.copyWith(
          color: DesignTokens.textMuted,
          fontSize: 12,
        ),
      );
      textPainter.layout();

      final x = i * stepX - textPainter.width / 2;
      final y = size.height - 15;
      textPainter.paint(canvas, Offset(x, y));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Custom painter for progress rings
class ProgressRingPainter extends CustomPainter {
  final double progress;
  final Color color;
  final double strokeWidth;

  ProgressRingPainter({
    required this.progress,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // Background circle
    final backgroundPaint = Paint()
      ..color = color.withValues(alpha: 0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Progress arc
    final progressPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    final sweepAngle = 2 * math.pi * progress;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2,
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
