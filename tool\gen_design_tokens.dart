#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';
import 'package:yaml/yaml.dart';

/// Generates lib/utils/design_tokens.dart from design_tokens.yaml
void main() async {
  print('🎨 Generating design tokens from YAML...');
  
  try {
    final designTokens = await generateDesignTokens();
    await File('lib/utils/design_tokens.dart').writeAsString(designTokens);
    print('✅ Design tokens generated successfully!');
  } catch (e) {
    print('❌ Error generating design tokens: $e');
    exit(1);
  }
}

Future<String> generateDesignTokens() async {
  final yamlFile = File('design_tokens.yaml');
  if (!yamlFile.existsSync()) {
    throw Exception('design_tokens.yaml not found');
  }

  final yamlContent = await yamlFile.readAsString();
  final yaml = loadYaml(yamlContent);
  
  final buffer = StringBuffer();
  
  // File header
  buffer.writeln("import 'package:flutter/material.dart';");
  buffer.writeln();
  buffer.writeln('/// Design tokens for the Darvis AI Assistant app');
  buffer.writeln('/// Auto-generated from design_tokens.yaml - DO NOT EDIT MANUALLY');
  buffer.writeln('class DesignTokens {');
  
  // Generate colors
  if (yaml['colors'] != null) {
    buffer.writeln('  // Colors');
    _generateColors(buffer, yaml['colors']);
  }
  
  // Generate spacing
  if (yaml['spacing'] != null) {
    buffer.writeln('  // Spacing');
    _generateSpacing(buffer, yaml['spacing']);
  }
  
  // Generate border radius
  if (yaml['border_radius'] != null) {
    buffer.writeln('  // Border Radius');
    _generateBorderRadius(buffer, yaml['border_radius']);
  }
  
  // Generate typography
  if (yaml['typography'] != null) {
    buffer.writeln('  // Typography');
    _generateTypography(buffer, yaml['typography']);
  }
  
  // Generate text styles
  if (yaml['typography']?['styles'] != null) {
    buffer.writeln('  // Text Styles');
    _generateTextStyles(buffer, yaml['typography']['styles'], yaml['colors']);
  }
  
  // Generate gradients
  if (yaml['gradients'] != null) {
    buffer.writeln('  // Gradients');
    _generateGradients(buffer, yaml['gradients']);
  }

  // Generate effects
  if (yaml['effects'] != null) {
    buffer.writeln('  // Effects');
    _generateEffects(buffer, yaml['effects']);
  }

  // Generate component colors
  if (yaml['components'] != null) {
    buffer.writeln('  // Component Colors');
    _generateComponentColors(buffer, yaml['components']);
  }

  // Generate theme
  buffer.writeln('  // Theme');
  _generateTheme(buffer);
  
  buffer.writeln('}');
  
  return buffer.toString();
}

void _generateColors(StringBuffer buffer, dynamic colors) {
  _generateColorSection(buffer, colors['primary'], 'primary');
  _generateColorSection(buffer, colors['background'], 'background');
  _generateColorSection(buffer, colors['text'], 'text');
  _generateColorSection(buffer, colors['icon'], 'icon');
  _generateColorSection(buffer, colors['border'], 'border');
  _generateColorSection(buffer, colors['navigation'], 'navigation');
  _generateColorSection(buffer, colors['priority'], 'priority');
  _generateColorSection(buffer, colors['category'], 'category');
}

void _generateColorSection(StringBuffer buffer, dynamic section, String prefix) {
  if (section == null) return;
  
  section.forEach((key, value) {
    final colorName = '${prefix}${_capitalize(key)}';
    final colorValue = _hexToColorCode(value);
    buffer.writeln('  static const Color $colorName = Color($colorValue);');
  });
  buffer.writeln();
}

void _generateSpacing(StringBuffer buffer, dynamic spacing) {
  spacing.forEach((key, value) {
    final spacingName = 'spacing${_capitalize(key)}';
    buffer.writeln('  static const double $spacingName = ${value.toDouble()};');
  });
  buffer.writeln();
}

void _generateBorderRadius(StringBuffer buffer, dynamic borderRadius) {
  borderRadius.forEach((key, value) {
    final radiusName = 'borderRadius${_capitalize(key)}';
    buffer.writeln('  static const double $radiusName = ${value.toDouble()};');
  });
  buffer.writeln();
}

void _generateTypography(StringBuffer buffer, dynamic typography) {
  if (typography['font_families'] != null) {
    typography['font_families'].forEach((key, value) {
      final fontName = 'fontFamily${_capitalize(key)}';
      buffer.writeln("  static const String $fontName = '$value';");
    });
    buffer.writeln();
  }
}

void _generateTextStyles(StringBuffer buffer, dynamic styles, dynamic colors) {
  styles.forEach((key, style) {
    // Convert snake_case to camelCase for style names
    final styleName = _convertToCamelCase(key) + 'Style';
    buffer.writeln('  static const TextStyle $styleName = TextStyle(');
    
    if (style['font_family'] != null) {
      final fontFamily = style['font_family'] == 'heading' ? 'fontFamilyHeading' : 'fontFamilyBody';
      buffer.writeln('    fontFamily: $fontFamily,');
    }
    
    if (style['font_size'] != null) {
      buffer.writeln('    fontSize: ${style['font_size']},');
    }
    
    if (style['font_weight'] != null) {
      final weight = _getFontWeight(style['font_weight']);
      buffer.writeln('    fontWeight: $weight,');
    }
    
    if (style['color_ref'] != null) {
      final colorRef = _resolveColorRef(style['color_ref']);
      buffer.writeln('    color: $colorRef,');
    }
    
    buffer.writeln('  );');
    buffer.writeln();
  });
}

void _generateComponentColors(StringBuffer buffer, dynamic components) {
  // Generate onboarding indicator colors (special case with color references)
  buffer.writeln('  // Onboarding Indicator');
  buffer.writeln('  static const Color onboardingIndicatorActive = textPrimary;');
  buffer.writeln('  static const Color onboardingIndicatorInactive = Color(0xFF333333);');
  buffer.writeln();

  // Generate dynamic component colors from flat key-value pairs
  buffer.writeln('  // Component Colors');
  components.forEach((key, value) {
    // Only process flat key-value pairs (not nested objects)
    if (value is String && value.startsWith('#')) {
      final colorName = _convertToCamelCase(key);
      final colorValue = _hexToColorCode(value);
      buffer.writeln('  static const Color $colorName = Color($colorValue);');
    }
  });
  buffer.writeln();
}

void _generateGradients(StringBuffer buffer, dynamic gradients) {
  gradients.forEach((key, gradient) {
    final gradientName = _convertToCamelCase(key) + 'Gradient';

    if (gradient['type'] == 'linear') {
      buffer.writeln('  static const LinearGradient $gradientName = LinearGradient(');
      buffer.writeln('    begin: Alignment.topCenter,');
      buffer.writeln('    end: Alignment.bottomCenter,');
      buffer.write('    colors: [');

      final stops = gradient['stops'] as List;
      for (int i = 0; i < stops.length; i++) {
        final stop = stops[i];
        final colorCode = _hexToColorCode(stop['color']);
        buffer.write('Color($colorCode)');
        if (i < stops.length - 1) buffer.write(', ');
      }
      buffer.writeln('],');
      buffer.writeln('  );');
    } else if (gradient['type'] == 'radial') {
      buffer.writeln('  static const RadialGradient $gradientName = RadialGradient(');
      buffer.writeln('    center: Alignment.center,');
      buffer.write('    colors: [');

      final stops = gradient['stops'] as List;
      for (int i = 0; i < stops.length; i++) {
        final stop = stops[i];
        final colorCode = _hexToColorCode(stop['color']);
        buffer.write('Color($colorCode)');
        if (i < stops.length - 1) buffer.write(', ');
      }
      buffer.writeln('],');

      buffer.write('    stops: [');
      for (int i = 0; i < stops.length; i++) {
        final stop = stops[i];
        final position = stop['position'].toString().replaceAll('%', '');
        final positionValue = double.parse(position) / 100;
        buffer.write('$positionValue');
        if (i < stops.length - 1) buffer.write(', ');
      }
      buffer.writeln('],');
      buffer.writeln('  );');
    }
    buffer.writeln();
  });
}

void _generateEffects(StringBuffer buffer, dynamic effects) {
  if (effects['glows'] != null) {
    effects['glows'].forEach((key, glow) {
      final effectName = _convertToCamelCase(key) + 'Glow';
      buffer.writeln('  static const BoxShadow $effectName = BoxShadow(');

      final colorHex = glow['color'].toString().replaceAll('#', '');
      final opacity = glow['opacity'] ?? 1.0;
      final r = int.parse(colorHex.substring(0, 2), radix: 16);
      final g = int.parse(colorHex.substring(2, 4), radix: 16);
      final b = int.parse(colorHex.substring(4, 6), radix: 16);

      buffer.writeln('    color: Color.fromRGBO($r, $g, $b, $opacity),');
      buffer.writeln('    blurRadius: ${glow['blur']},');
      buffer.writeln('    spreadRadius: ${glow['spread']},');
      buffer.writeln('  );');
      buffer.writeln();
    });
  }
}

void _generateTheme(StringBuffer buffer) {
  buffer.writeln('  static ThemeData get darkTheme {');
  buffer.writeln('    return ThemeData(');
  buffer.writeln('      brightness: Brightness.dark,');
  buffer.writeln('      primaryColor: primaryInteractiveBlue,');
  buffer.writeln('      scaffoldBackgroundColor: backgroundApp,');
  buffer.writeln('      fontFamily: fontFamilyBody,');
  buffer.writeln('      textTheme: const TextTheme(');
  buffer.writeln('        displayLarge: appNameStyle,');
  buffer.writeln('        headlineSmall: cardTitleStyle,');
  buffer.writeln('        bodyMedium: bodyStyle,');
  buffer.writeln('        bodyLarge: chatMessageStyle,');
  buffer.writeln('      ),');
  buffer.writeln('      colorScheme: const ColorScheme.dark(');
  buffer.writeln('        primary: primaryInteractiveBlue,');
  buffer.writeln('        secondary: primaryAccentBlue,');
  buffer.writeln('        surface: backgroundCard,');
  buffer.writeln('        onPrimary: textPrimary,');
  buffer.writeln('        onSecondary: textPrimary,');
  buffer.writeln('        onSurface: textPrimary,');
  buffer.writeln('      ),');
  buffer.writeln('      inputDecorationTheme: InputDecorationTheme(');
  buffer.writeln('        filled: true,');
  buffer.writeln('        fillColor: backgroundTextInput,');
  buffer.writeln('        border: OutlineInputBorder(');
  buffer.writeln('          borderRadius: BorderRadius.circular(borderRadiusXl),');
  buffer.writeln('          borderSide: BorderSide.none,');
  buffer.writeln('        ),');
  buffer.writeln('        contentPadding: const EdgeInsets.symmetric(');
  buffer.writeln('          horizontal: spacingMd,');
  buffer.writeln('          vertical: spacingMd,');
  buffer.writeln('        ),');
  buffer.writeln('      ),');
  buffer.writeln('      cardTheme: CardThemeData(');
  buffer.writeln('        color: backgroundCard,');
  buffer.writeln('        shape: RoundedRectangleBorder(');
  buffer.writeln('          borderRadius: BorderRadius.circular(borderRadiusLg),');
  buffer.writeln('        ),');
  buffer.writeln('      ),');
  buffer.writeln('    );');
  buffer.writeln('  }');
}

String _capitalize(String text) {
  if (text.isEmpty) return text;
  return text.split('_').map((word) => 
    word[0].toUpperCase() + word.substring(1).toLowerCase()
  ).join('');
}

String _hexToColorCode(String hex) {
  hex = hex.replaceAll('#', '');
  return '0xFF$hex';
}

String _getFontWeight(dynamic weight) {
  switch (weight.toString()) {
    case '400': return 'FontWeight.w400';
    case '500': return 'FontWeight.w500';
    case '600': return 'FontWeight.w600';
    case '700': return 'FontWeight.w700';
    default: return 'FontWeight.normal';
  }
}

String _resolveColorRef(String colorRef) {
  final parts = colorRef.split('.');
  if (parts.length == 2) {
    return '${parts[0]}${_capitalize(parts[1])}';
  }
  return 'Colors.white';
}

String _convertToCamelCase(String text) {
  if (text.isEmpty) return text;
  final parts = text.split('_');
  if (parts.length == 1) return text;

  return parts[0] + parts.skip(1).map((word) =>
    word[0].toUpperCase() + word.substring(1).toLowerCase()
  ).join('');
}
