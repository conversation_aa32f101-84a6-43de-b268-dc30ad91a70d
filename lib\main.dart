import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'utils/design_tokens.dart';
import 'utils/no_glow_scroll_behavior.dart';
import 'services/service_locator.dart';
import 'services/navigation_service.dart';
import 'blocs/auth/auth_bloc.dart';
import 'blocs/auth/auth_event.dart';
import 'blocs/auth/auth_state.dart';
import 'screens/auth/login_screen.dart';
import 'screens/home/<USER>';
import 'screens/onboarding/onboarding_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase for mobile
  await Firebase.initializeApp();

  // Setup service locator
  await setupServiceLocator();

  runApp(const DarvisApp());
}

class DarvisApp extends StatefulWidget {
  const DarvisApp({super.key});

  @override
  State<DarvisApp> createState() => _DarvisAppState();
}

class _DarvisAppState extends State<DarvisApp> {
  bool? _hasCompletedOnboarding;

  @override
  void initState() {
    super.initState();
    _checkOnboardingStatus();
  }

  Future<void> _checkOnboardingStatus() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _hasCompletedOnboarding = prefs.getBool('has_completed_onboarding') ?? false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<AuthBloc>(
      create: (context) => getIt<AuthBloc>()..add(AuthCheckRequested()),
      child: MaterialApp(
        title: 'Drix AI Assistant',
        theme: DesignTokens.darkTheme,
        navigatorKey: NavigationService.navigatorKey,
        scrollBehavior: NoGlowScrollBehavior(),
        home: _hasCompletedOnboarding == null
            ? const Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              )
            : BlocBuilder<AuthBloc, AuthState>(
                builder: (context, state) {
                  if (state is AuthAuthenticated) {
                    return _hasCompletedOnboarding! ? const HomeScreen() : const OnboardingScreen();
                  } else if (state is AuthUnauthenticated) {
                    return _hasCompletedOnboarding! ? const LoginScreen() : const OnboardingScreen();
                  } else {
                    return const Scaffold(
                      body: Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  }
                },
              ),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
