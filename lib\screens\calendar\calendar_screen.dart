import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'package:darvis_app/services/navigation_service.dart';
import 'package:darvis_app/blocs/calendar/calendar_bloc.dart';
import 'package:darvis_app/blocs/calendar/calendar_event.dart';
import 'package:darvis_app/blocs/calendar/calendar_state.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';

// Mock data model for an event. In a real app, this would be in /models.
class Event {
  final String title;
  final String time;
  Event({required this.title, required this.time});
}

/// CalendarScreen: A screen for viewing and managing events in a calendar.
class CalendarScreen extends StatefulWidget {
  const CalendarScreen({super.key});

  @override
  State<CalendarScreen> createState() => _CalendarScreenState();
}

class _CalendarScreenState extends State<CalendarScreen> with TickerProviderStateMixin {
  // State for the calendar
  CalendarFormat _calendarFormat = CalendarFormat.week; // Start in compact view
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;

  // BLoC
  late CalendarBloc _calendarBloc;

  List<Event> _getEventsForDay(DateTime day, List<CalendarEventModel> calendarEvents) {
    final dayEvents = calendarEvents.where((event) {
      return _isSameDay(event.startTime, day);
    }).map((calendarEvent) => Event(
      title: calendarEvent.title,
      time: DateFormat('h:mm a').format(calendarEvent.startTime),
    )).toList();

    return dayEvents;
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  @override
  void initState() {
    super.initState();
    _calendarBloc = GetIt.instance<CalendarBloc>();
    _selectedDay = _focusedDay;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Load calendar events
    _calendarBloc.add(LoadCalendarEvents(_focusedDay));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleCalendarView() {
    setState(() {
      _isExpanded = !_isExpanded;
      _calendarFormat = _isExpanded ? CalendarFormat.month : CalendarFormat.week;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final navigationService = GetIt.instance<NavigationService>();

    return BlocProvider.value(
      value: _calendarBloc,
      child: Scaffold(
        backgroundColor: DesignTokens.backgroundApp,
        body: BlocBuilder<CalendarBloc, CalendarState>(
          builder: (context, state) {
            List<CalendarEventModel> events = [];
            Map<DateTime, List<Event>> eventsByDate = {};

            if (state is CalendarLoaded) {
              events = state.events;
              // Convert CalendarEventModel to Event for UI compatibility
              eventsByDate = state.eventsByDate.map((date, calendarEvents) {
                final uiEvents = calendarEvents.map((calendarEvent) => Event(
                  title: calendarEvent.title,
                  time: DateFormat('h:mm a').format(calendarEvent.startTime),
                )).toList();
                return MapEntry(date, uiEvents);
              });
            }

            return Column(
              children: [
                _CalendarHeader(
                  onAddTapped: () {},
                  onAddEventTapped: () => navigationService.navigateToAddEvent(),
                  onAddContactTapped: () => navigationService.navigateToAddContact(),
                  onViewContactsTapped: () => navigationService.navigateToContactList(),
                ),
                const SizedBox(height: DesignTokens.spacingXl),
                _CalendarView(
                  focusedDay: _focusedDay,
                  selectedDay: _selectedDay,
                  calendarFormat: _calendarFormat,
                  events: eventsByDate,
                  rotationAnimation: _rotationAnimation,
                  onDaySelected: (selectedDay, focusedDay) {
                    setState(() {
                      _selectedDay = selectedDay;
                      _focusedDay = focusedDay;
                    });
                  },
                  onFormatChanged: (format) {
                    setState(() {
                      _calendarFormat = format;
                    });
                  },
                  onPageChanged: (focusedDay) {
                    setState(() {
                      _focusedDay = focusedDay;
                    });
                  },
                  onToggleView: _toggleCalendarView,
                ),
                const SizedBox(height: DesignTokens.spacingXl),
                _EventsList(
                  selectedDate: _selectedDay ?? DateTime.now(),
                  events: _getEventsForDay(_selectedDay ?? DateTime.now(), events),
                  isToday: _isSameDay(_selectedDay ?? DateTime.now(), DateTime.now()),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

// --- Header Widget ---

class _CalendarHeader extends StatelessWidget {
  final VoidCallback onAddTapped;
  final VoidCallback onAddEventTapped;
  final VoidCallback onAddContactTapped;
  final VoidCallback onViewContactsTapped;

  const _CalendarHeader({
    required this.onAddTapped, 
    required this.onAddEventTapped, 
    required this.onAddContactTapped,
    required this.onViewContactsTapped
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: false,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingSm,
        ),
        child: Column(
          children: [
            const SizedBox(height: 20), // Same spacing as chat screen
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const _GradientTitle(text: 'Calendar'),
                _AddButton(
                  onAddEventTapped: onAddEventTapped,
                  onAddContactTapped: onAddContactTapped,
                  onViewContactsTapped: onViewContactsTapped,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _GradientTitle extends StatelessWidget {
  final String text;
  const _GradientTitle({required this.text});

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      blendMode: BlendMode.srcIn,
      shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(
        Rect.fromLTWH(0, 0, bounds.width, bounds.height),
      ),
      child: Text(text, style: DesignTokens.appNameStyle),
    );
  }
}

// Custom dropdown button for the '+' action.
class _AddButton extends StatelessWidget {
  final VoidCallback onAddEventTapped;
  final VoidCallback onAddContactTapped;
  final VoidCallback onViewContactsTapped;

  const _AddButton({
    required this.onAddEventTapped,
    required this.onAddContactTapped,
    required this.onViewContactsTapped,
  });

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<int>(
      color: DesignTokens.calendarDropdownBackground,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
      ),
      offset: const Offset(0, 40), // Position dropdown below the button
      onSelected: (value) {
        if (value == 0) onAddEventTapped();
        if (value == 1) onAddContactTapped();
        if (value == 2) onViewContactsTapped();
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 0,
          child: Text(
            'Add an Event',
            style: DesignTokens.calendarDropdownItemStyle.copyWith(fontSize: 16) // Larger text
          )
        ),
        PopupMenuItem(
          value: 1,
          child: Text(
            'Add a Contact',
            style: DesignTokens.calendarDropdownItemStyle.copyWith(fontSize: 16) // Larger text
          )
        ),
        PopupMenuItem(
          value: 2,
          child: Text(
            'View Contacts',
            style: DesignTokens.calendarDropdownItemStyle.copyWith(fontSize: 16) // Larger text
          )
        ),
      ],
      child: SvgPicture.asset('assets/icons/add_event.svg'),
    );
  }
}

// --- Main Calendar Widget ---

class _CalendarView extends StatelessWidget {
  final DateTime focusedDay;
  final DateTime? selectedDay;
  final CalendarFormat calendarFormat;
  final Map<DateTime, List<Event>> events;
  final Animation<double> rotationAnimation;
  final Function(DateTime, DateTime) onDaySelected;
  final Function(CalendarFormat) onFormatChanged;
  final Function(DateTime) onPageChanged;
  final VoidCallback onToggleView;

  const _CalendarView({
    required this.focusedDay,
    this.selectedDay,
    required this.calendarFormat,
    required this.events,
    required this.rotationAnimation,
    required this.onDaySelected,
    required this.onFormatChanged,
    required this.onPageChanged,
    required this.onToggleView,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundChatBubbleUser,
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
      ),
      child: Column(
        children: [
          // Calendar header with toggle button
          Padding(
            padding: const EdgeInsets.all(DesignTokens.spacingSm),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Center(
                  child: Text(
                    DateFormat.yMMMM().format(focusedDay),
                    style: DesignTokens.calendarMonthNameStyle,
                  ),
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: GestureDetector(
                    onTap: onToggleView,
                    child: AnimatedBuilder(
                      animation: rotationAnimation,
                      builder: (context, child) {
                        return Transform.rotate(
                          angle: rotationAnimation.value * 3.14159, // π radians = 180 degrees
                          child: const Icon(
                            Icons.keyboard_arrow_down,
                            color: Colors.white,
                            size: 24,
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: DesignTokens.spacingMd), // Add spacing between header and calendar
          // Calendar widget
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingSm),
            child: TableCalendar(
        // Core properties
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2030, 12, 31),
        focusedDay: focusedDay,
        calendarFormat: calendarFormat,
        selectedDayPredicate: (day) => isSameDay(selectedDay, day),
        onDaySelected: onDaySelected,
        onFormatChanged: onFormatChanged,
        onPageChanged: onPageChanged,
        
        // Disable gestures to control view toggle manually
        availableGestures: AvailableGestures.horizontalSwipe,
        
        // Custom Styling
        headerStyle: HeaderStyle(
          titleCentered: true,
          titleTextStyle: DesignTokens.calendarMonthNameStyle,
          formatButtonVisible: false, // Hide the default format button
          leftChevronVisible: false, // Hide default navigation
          rightChevronVisible: false,
        ),
        daysOfWeekStyle: DaysOfWeekStyle(
          weekdayStyle: DesignTokens.calendarDayOfWeekStyle,
          weekendStyle: DesignTokens.calendarDayOfWeekStyle,
        ),
        calendarStyle: CalendarStyle(
          defaultTextStyle: DesignTokens.calendarDateNumberStyle,
          weekendTextStyle: DesignTokens.calendarDateNumberStyle,
          outsideTextStyle: DesignTokens.calendarDateNumberStyle.copyWith(
            color: DesignTokens.textMuted
          ),
          
          // Custom builder for today's date
          todayDecoration: const BoxDecoration(
            color: DesignTokens.calendarTodayMarkerBackground,
            shape: BoxShape.circle,
          ),
          todayTextStyle: DesignTokens.calendarDateNumberStyle.copyWith(
            color: DesignTokens.backgroundChatBubbleUser,
          ),

          // Custom builder for the selected date
          selectedDecoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: DesignTokens.calendarSelectedMarkerOutline,
              width: 2,
            ),
          ),
          selectedTextStyle: DesignTokens.calendarDateNumberStyle.copyWith(
            color: DesignTokens.calendarSelectedMarkerOutline
          ),
        ),
        // Hide the header since we have our own
        headerVisible: false,
      ),
            ),
          ],
        ),
    );
  }
}

// --- Events List Widget ---

class _EventsList extends StatelessWidget {
  final DateTime selectedDate;
  final List<Event> events;
  final bool isToday;

  const _EventsList({
    required this.selectedDate,
    required this.events,
    required this.isToday,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingLg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isToday ? "Today's Schedule" : "${DateFormat.MMMMd().format(selectedDate)} Schedule",
              style: DesignTokens.cardTitleStyle
            ),
            const SizedBox(height: DesignTokens.spacingSm),
            Expanded(
              child: events.isEmpty
                  ? const _EmptyState()
                  : ListView.builder(
                      itemCount: events.length,
                      itemBuilder: (context, index) {
                        final event = events[index];
                        return _EventListItem(event: event);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

class _EventListItem extends StatelessWidget {
  final Event event;
  const _EventListItem({required this.event});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingMd,
        vertical: DesignTokens.spacingMd,
      ),
      margin: const EdgeInsets.only(bottom: DesignTokens.spacingSm),
      decoration: BoxDecoration(
        color: DesignTokens.eventListItemBackground,
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(event.title, style: DesignTokens.eventListItemTitleStyle),
          Text(event.time, style: DesignTokens.eventListItemTimeStyle),
        ],
      ),
    );
  }
}

class _EmptyState extends StatelessWidget {
  const _EmptyState();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            'assets/icons/calendar_empty.svg',
            height: 60,
            colorFilter: const ColorFilter.mode(DesignTokens.textMuted, BlendMode.srcIn)
          ),
          const SizedBox(height: DesignTokens.spacingMd),
          Text('No events scheduled', style: DesignTokens.bodyStyle),
          const SizedBox(height: DesignTokens.spacingXs),
          Text(
            'Tap + to add an event', 
            style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textMuted)
          ),
        ],
      ),
    );
  }
}

