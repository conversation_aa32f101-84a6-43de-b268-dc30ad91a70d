import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'package:darvis_app/services/navigation_service.dart';
import 'package:darvis_app/services/data_service.dart';
import 'package:darvis_app/services/vcf_service.dart';
import 'package:darvis_app/services/device_contacts_service.dart';
import 'package:darvis_app/services/social_media_service.dart';
import 'package:darvis_app/services/contact_permission_service.dart';
import 'package:darvis_app/widgets/enhanced_bottom_nav_bar.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

class AddContactScreen extends StatefulWidget {
  const AddContactScreen({super.key});

  @override
  State<AddContactScreen> createState() => _AddContactScreenState();
}

class _AddContactScreenState extends State<AddContactScreen> with TickerProviderStateMixin {
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _locationController = TextEditingController();
  final _notesController = TextEditingController();
  final _socialUsernameController = TextEditingController();
  
  String? _selectedImage;
  String _selectedSocialPlatform = 'Twitter';
  bool _isLoading = false;
  bool _isLinkButtonLoading = false;
  bool _isLinkButtonSuccess = false;
  bool _isSearchLoading = false;
  bool _showSocialDropdown = false;
  
  final List<String> _socialPlatforms = ['Twitter', 'Instagram', 'LinkedIn', 'WhatsApp'];

  late AnimationController _loadingController;
  late AnimationController _successController;

  // Services
  final VcfService _vcfService = VcfService();
  final DeviceContactsService _deviceContactsService = DeviceContactsService();
  final SocialMediaService _socialMediaService = SocialMediaService();
  final ContactPermissionService _permissionService = ContactPermissionService();
  
  @override
  void initState() {
    super.initState();
    _loadingController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _successController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _locationController.dispose();
    _notesController.dispose();
    _socialUsernameController.dispose();
    _loadingController.dispose();
    _successController.dispose();
    super.dispose();
  }

  void _openCamera() async {
    final ImagePicker picker = ImagePicker();
    try {
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 800,
        maxHeight: 800,
      );

      if (image != null) {
        setState(() {
          _selectedImage = image.path;
        });
      }
    } catch (e) {
      // Handle camera permission or other errors
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to access camera. Please check permissions.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _clearForm() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DesignTokens.backgroundCard,
        title: const Text('Clear All Fields?', style: DesignTokens.cardTitleStyle),
        content: const Text(
          'This will remove all entered information.',
          style: DesignTokens.bodyStyle,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel', style: DesignTokens.linkTextStyle),
          ),
          TextButton(
            onPressed: () {
              _nameController.clear();
              _phoneController.clear();
              _locationController.clear();
              _notesController.clear();
              _socialUsernameController.clear();
              setState(() {
                _selectedImage = null;
                _selectedSocialPlatform = 'Twitter';
                _isLinkButtonSuccess = false;
              });
              Navigator.of(context).pop();
            },
            child: const Text('Clear All', style: DesignTokens.linkTextStyle),
          ),
        ],
      ),
    );
  }

  Future<void> _generateContactLink() async {
    if (_phoneController.text.isEmpty) return;

    setState(() {
      _isLinkButtonLoading = true;
    });

    _loadingController.repeat();

    try {
      // Prepare social media data for VCF
      final socialMedia = <String, String>{};
      if (_selectedSocialPlatform.isNotEmpty && _socialUsernameController.text.trim().isNotEmpty) {
        socialMedia[_selectedSocialPlatform.toLowerCase()] = _socialUsernameController.text.trim();
      }

      // Create temporary contact for VCF generation
      final tempContact = Contact(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim().isNotEmpty ? _nameController.text.trim() : 'Contact',
        phone: _phoneController.text.trim(),
        email: '', // No email field in current form
        location: _locationController.text.trim(),
        metAt: _locationController.text.trim(), // Use location as "met at"
        socialMedia: socialMedia,
        memoryPrompt: _notesController.text.trim(),
        imagePath: _selectedImage,
        createdAt: DateTime.now(),
      );

      // Generate and share VCF file
      await _vcfService.shareVcfFile(tempContact);

      _loadingController.stop();

      setState(() {
        _isLinkButtonLoading = false;
        _isLinkButtonSuccess = true;
      });

      _successController.forward();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Contact link generated and shared!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _loadingController.stop();

      setState(() {
        _isLinkButtonLoading = false;
        _isLinkButtonSuccess = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error generating contact link: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _searchSocialMedia() async {
    if (_socialUsernameController.text.isEmpty) return;

    setState(() {
      _isSearchLoading = true;
    });

    try {
      final username = _socialUsernameController.text.trim();

      // Validate username format
      if (!_socialMediaService.isValidUsername(_selectedSocialPlatform.toLowerCase(), username)) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Invalid ${_selectedSocialPlatform} username format'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // Try to open profile directly first
      final success = await _socialMediaService.openProfile(_selectedSocialPlatform.toLowerCase(), username);

      if (!success) {
        // Fallback to search
        await _socialMediaService.searchUsername(_selectedSocialPlatform.toLowerCase(), username);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Opened ${_selectedSocialPlatform} profile'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening ${_selectedSocialPlatform}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSearchLoading = false;
      });
    }
  }

  Future<void> _saveContact() async {
    final errors = <String>[];
    
    if (_nameController.text.trim().isEmpty) {
      errors.add('Contact name is required');
    }
    
    if (_phoneController.text.trim().isEmpty) {
      errors.add('Phone number is required');
    } else if (_phoneController.text.trim().length < 10) {
      errors.add('Phone number must be at least 10 digits');
    }

    if (errors.isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.white),
              const SizedBox(width: DesignTokens.spacingSm),
              Expanded(
                child: Text(
                  errors.length == 1 
                    ? errors.first
                    : 'Please complete all required fields:\n• ${errors.join('\n• ')}',
                  style: DesignTokens.bodyStyle.copyWith(color: Colors.white),
                ),
              ),
            ],
          ),
          backgroundColor: DesignTokens.priorityHigh,
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          ),
        ),
      );
      return;
    }
    
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DesignTokens.backgroundCard,
        title: const Text('Save Contact?', style: DesignTokens.cardTitleStyle),
        content: Text(
          'Save ${_nameController.text} to your contacts?',
          style: DesignTokens.bodyStyle,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel', style: DesignTokens.linkTextStyle),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Save', style: DesignTokens.linkTextStyle),
          ),
        ],
      ),
    );
    
    if (confirmed != true) return;
    
    setState(() {
      _isLoading = true;
    });
    
    _loadingController.repeat();
    
    try {
      // Prepare social media data
      final socialMedia = <String, String>{};
      if (_selectedSocialPlatform.isNotEmpty && _socialUsernameController.text.trim().isNotEmpty) {
        socialMedia[_selectedSocialPlatform.toLowerCase()] = _socialUsernameController.text.trim();
      }

      // Create new contact with updated model structure
      final contact = Contact(
        id: '', // Backend will generate ID
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        email: '', // No email field in current form
        location: _locationController.text.trim(),
        metAt: _locationController.text.trim(), // Use location as "met at"
        socialMedia: socialMedia,
        memoryPrompt: _notesController.text.trim(),
        imagePath: _selectedImage,
        deviceSyncStatus: 'disabled', // Will be updated if device save succeeds
        createdAt: DateTime.now(),
      );

      // Save to backend via data service
      final savedContact = await DataService().addContact(contact);

      if (savedContact == null) {
        throw Exception('Failed to save contact to backend');
      }

      // Try to save to device contacts (optional)
      bool deviceSaveSuccess = false;
      try {
        deviceSaveSuccess = await _deviceContactsService.saveContactToDevice(savedContact);

        // Update sync status if device save was successful
        if (deviceSaveSuccess) {
          await DataService().updateContactDeviceSync(
            contactId: savedContact.id,
            syncStatus: 'synced',
          );
        }
      } catch (e) {
        debugPrint('Device contact save failed: $e');
        // Update sync status to failed
        try {
          await DataService().updateContactDeviceSync(
            contactId: savedContact.id,
            syncStatus: 'failed',
            syncError: e.toString(),
          );
        } catch (syncError) {
          debugPrint('Failed to update sync status: $syncError');
        }
      }

      _loadingController.stop();

      if (mounted) {
        // Show success feedback with animation
        await _showSuccessAnimation();

        // Show additional feedback about device save
        if (deviceSaveSuccess) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Contact saved to app and device contacts!'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Contact saved to app. Device contacts permission needed for full integration.'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }

        // Navigate back to calendar
        GetIt.instance<NavigationService>().navigateToTab(1); // Calendar tab
      }
    } catch (e) {
      _loadingController.stop();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: DesignTokens.spacingSm),
                const Expanded(
                  child: Text(
                    'Failed to save contact. Please try again.',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
            backgroundColor: DesignTokens.priorityHigh,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _showSuccessAnimation() async {
    // Show success overlay with animation
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const _SuccessOverlay(),
    );
    
    // Wait for animation to complete
    await Future.delayed(const Duration(milliseconds: 2000));
    
    // Remove overlay
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      resizeToAvoidBottomInset: false, // Keep navbar in place
      body: SafeArea(
        child: Column(
          children: [
            _Header(onBackTap: () => GetIt.instance<NavigationService>().navigateBack()),
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.only(
                  left: DesignTokens.spacingMd,
                  right: DesignTokens.spacingMd,
                  top: DesignTokens.spacingSm,
                  bottom: DesignTokens.spacingSm + MediaQuery.of(context).viewInsets.bottom,
                ),
                child: Column(
                  children: [
                    const SizedBox(height: DesignTokens.spacingSm),

                    // Image Upload Section
                    _ImageUploadSection(
                      selectedImage: _selectedImage,
                      onTap: _openCamera,
                    ),

                    const SizedBox(height: DesignTokens.spacingMd),

                    // Form Fields
                    _FormField(
                      label: "What's your Name?",
                      controller: _nameController,
                      isRequired: true,
                    ),
                    const SizedBox(height: DesignTokens.spacingXs),

                    _PhoneField(
                      controller: _phoneController,
                      onLinkTap: _generateContactLink,
                      isLinkLoading: _isLinkButtonLoading,
                      isLinkSuccess: _isLinkButtonSuccess,
                      loadingController: _loadingController,
                    ),
                    const SizedBox(height: DesignTokens.spacingXs),

                    _FormField(
                      label: "Where did our paths cross today",
                      controller: _locationController,
                    ),
                    const SizedBox(height: DesignTokens.spacingXs),

                    _SocialMediaField(
                      controller: _socialUsernameController,
                      selectedPlatform: _selectedSocialPlatform,
                      platforms: _socialPlatforms,
                      showDropdown: _showSocialDropdown,
                      isSearchLoading: _isSearchLoading,
                      onPlatformChanged: (platform) {
                        setState(() {
                          _selectedSocialPlatform = platform;
                          _showSocialDropdown = false;
                        });
                      },
                      onDropdownToggle: () {
                        setState(() {
                          _showSocialDropdown = !_showSocialDropdown;
                        });
                      },
                      onSearchTap: _searchSocialMedia,
                    ),
                    const SizedBox(height: DesignTokens.spacingXs),

                    _ExpandableFormField(
                      label: "What should I remember you by?",
                      controller: _notesController,
                    ),

                    const SizedBox(height: DesignTokens.spacingLg),

                    // Action Buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _ActionButton(
                          text: 'Clear All',
                          onTap: _clearForm,
                          isSecondary: true,
                        ),
                        _ActionButton(
                          text: 'Save Contact',
                          onTap: _saveContact,
                          isLoading: _isLoading,
                          loadingController: _loadingController,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            // Keep the EnhancedBottomNavBar but add tap handlers for Calendar
            EnhancedBottomNavBar(
              selectedIndex: 1,
              onTabSelected: (index) {
                if (index == NavigationService.calendarTab) {
                  // Navigate back to main calendar screen
                  GetIt.instance<NavigationService>().navigateToTab(NavigationService.calendarTab);
                } else {
                  // Handle other tab navigation normally
                  GetIt.instance<NavigationService>().navigateToTab(index);
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}

// Header Component
class _Header extends StatelessWidget {
  final VoidCallback onBackTap;

  const _Header({required this.onBackTap});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: false,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingSm,
        ),
        child: Column(
          children: [
            const SizedBox(height: DesignTokens.spacingSm),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: onBackTap,
                  child: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const _GradientTitle(text: 'Add Contact'),
                const SizedBox(width: 24), // Balance the back button
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Gradient Title Component
class _GradientTitle extends StatelessWidget {
  final String text;

  const _GradientTitle({required this.text});

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
      child: Text(
        text,
        style: DesignTokens.appNameStyle.copyWith(
          color: Colors.white,
          fontSize: 35,
        ),
      ),
    );
  }
}

// Image Upload Section
class _ImageUploadSection extends StatelessWidget {
  final String? selectedImage;
  final VoidCallback onTap;

  const _ImageUploadSection({
    required this.selectedImage,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: selectedImage != null ? Colors.transparent : DesignTokens.formFieldBackground,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          border: selectedImage == null ? Border.all(
            color: DesignTokens.textMuted.withValues(alpha: 0.3),
            width: 1,
          ) : null,
        ),
        child: selectedImage != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              child: Image.file(
                File(selectedImage!),
                fit: BoxFit.cover,
              ),
            )
          : Center(
              child: SvgPicture.asset(
                'assets/icons/add_event.svg',
                width: 32,
                height: 32,
                colorFilter: const ColorFilter.mode(
                  DesignTokens.textMuted,
                  BlendMode.srcIn,
                ),
              ),
            ),
      ),
    );
  }
}

// Form Field Component
class _FormField extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final bool isRequired;

  const _FormField({
    required this.label,
    required this.controller,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingXs),
        TextField(
          controller: controller,
          style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
          decoration: InputDecoration(
            filled: true,
            fillColor: DesignTokens.formFieldBackground,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingSm,
            ),
          ),
        ),
      ],
    );
  }
}

// Expandable Form Field Component
class _ExpandableFormField extends StatelessWidget {
  final String label;
  final TextEditingController controller;

  const _ExpandableFormField({
    required this.label,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingXs),
        TextField(
          controller: controller,
          minLines: 1,
          maxLines: 4,
          style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
          decoration: InputDecoration(
            filled: true,
            fillColor: DesignTokens.formFieldBackground,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingSm,
            ),
          ),
        ),
      ],
    );
  }
}

// Phone Field with Contact Link Button
class _PhoneField extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onLinkTap;
  final bool isLinkLoading;
  final bool isLinkSuccess;
  final AnimationController loadingController;

  const _PhoneField({
    required this.controller,
    required this.onLinkTap,
    required this.isLinkLoading,
    required this.isLinkSuccess,
    required this.loadingController,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Digits Only', style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingXs),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: controller,
                keyboardType: TextInputType.phone,
                style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
                decoration: InputDecoration(
                  filled: true,
                  fillColor: DesignTokens.formFieldBackground,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: DesignTokens.spacingMd,
                    vertical: DesignTokens.spacingSm,
                  ),
                ),
              ),
            ),
            const SizedBox(width: DesignTokens.spacingSm),
            GestureDetector(
              onTap: isLinkLoading ? null : onLinkTap,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isLinkSuccess
                    ? DesignTokens.primaryInteractiveBlue
                    : DesignTokens.primaryAccentBlue,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: isLinkLoading
                    ? RotationTransition(
                        turns: loadingController,
                        child: const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(DesignTokens.iconPrimary),
                          ),
                        ),
                      )
                    : Icon(
                        isLinkSuccess ? Icons.check : Icons.link,
                        color: DesignTokens.iconPrimary,
                        size: 20,
                      ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

// Social Media Field with Dropdown and Search
class _SocialMediaField extends StatelessWidget {
  final TextEditingController controller;
  final String selectedPlatform;
  final List<String> platforms;
  final bool showDropdown;
  final bool isSearchLoading;
  final Function(String) onPlatformChanged;
  final VoidCallback onDropdownToggle;
  final VoidCallback onSearchTap;

  const _SocialMediaField({
    required this.controller,
    required this.selectedPlatform,
    required this.platforms,
    required this.showDropdown,
    required this.isSearchLoading,
    required this.onPlatformChanged,
    required this.onDropdownToggle,
    required this.onSearchTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Social Media', style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingSm),

        // Platform Selector
        GestureDetector(
          onTap: onDropdownToggle,
          child: Container(
            width: 150, // Fixed width instead of full width
            padding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingSm,
              vertical: DesignTokens.spacingSm,
            ),
            decoration: BoxDecoration(
              color: DesignTokens.formFieldBackground,
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  selectedPlatform,
                  style: DesignTokens.bodyStyle.copyWith(
                    color: DesignTokens.textPrimary,
                    fontSize: 14,
                  ),
                ),
                Icon(
                  showDropdown ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  color: DesignTokens.textMuted,
                  size: 18,
                ),
              ],
            ),
          ),
        ),

        // Dropdown Options
        if (showDropdown)
          Container(
            width: 150, // Match the selector width
            margin: const EdgeInsets.only(top: DesignTokens.spacingXs),
            decoration: BoxDecoration(
              color: DesignTokens.formFieldBackground,
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            ),
            child: Column(
              children: platforms.map((platform) {
                return GestureDetector(
                  onTap: () => onPlatformChanged(platform),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: DesignTokens.spacingSm,
                      vertical: DesignTokens.spacingXs,
                    ),
                    child: Text(
                      platform,
                      style: DesignTokens.bodyStyle.copyWith(
                        color: platform == selectedPlatform
                          ? DesignTokens.primaryInteractiveBlue
                          : DesignTokens.textPrimary,
                        fontSize: 14,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),

        const SizedBox(height: DesignTokens.spacingSm),

        // Username Input with Search Button
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: controller,
                style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
                decoration: InputDecoration(
                  filled: true,
                  fillColor: DesignTokens.formFieldBackground,
                  hintText: 'Username',
                  hintStyle: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textMuted),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: DesignTokens.spacingMd,
                    vertical: DesignTokens.spacingMd,
                  ),
                ),
              ),
            ),
            const SizedBox(width: DesignTokens.spacingSm),
            GestureDetector(
              onTap: isSearchLoading ? null : onSearchTap,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: DesignTokens.primaryAccentBlue,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: isSearchLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(DesignTokens.iconPrimary),
                        ),
                      )
                    : SvgPicture.asset(
                        'assets/icons/search_contact.svg',
                        width: 20,
                        height: 20,
                        colorFilter: const ColorFilter.mode(
                          DesignTokens.iconPrimary,
                          BlendMode.srcIn,
                        ),
                      ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

// Action Button Component
class _ActionButton extends StatelessWidget {
  final String text;
  final VoidCallback onTap;
  final bool isLoading;
  final bool isSecondary;
  final AnimationController? loadingController;

  const _ActionButton({
    required this.text,
    required this.onTap,
    this.isLoading = false,
    this.isSecondary = false,
    this.loadingController,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isLoading ? null : onTap,
      child: Container(
        width: 140,
        height: 45,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: isLoading
            ? Colors.transparent
            : isSecondary
              ? DesignTokens.formFieldBackground
              : DesignTokens.primaryButtonBackground,
          gradient: isLoading ? DesignTokens.appNameHeaderGradient : null,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        ),
        child: isLoading && loadingController != null
          ? RotationTransition(
              turns: loadingController!,
              child: const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          : Text(
              text,
              style: isSecondary
                ? DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary)
                : DesignTokens.primaryButtonTextStyle,
            ),
      ),
    );
  }
}

// Success Overlay Widget
class _SuccessOverlay extends StatefulWidget {
  const _SuccessOverlay();

  @override
  State<_SuccessOverlay> createState() => _SuccessOverlayState();
}

class _SuccessOverlayState extends State<_SuccessOverlay> 
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.4, curve: Curves.easeOut),
    ));
    
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black54,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Opacity(
            opacity: _opacityAnimation.value,
            child: Center(
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    color: DesignTokens.backgroundCard,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: DesignTokens.primaryInteractiveBlue.withOpacity(0.3),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: DesignTokens.primaryInteractiveBlue,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 50,
                        ),
                      ),
                      const SizedBox(height: DesignTokens.spacingMd),
                      const Text(
                        'Contact Saved!',
                        style: DesignTokens.cardTitleStyle,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: DesignTokens.spacingSm),
                      Text(
                        'Successfully added to your contacts',
                        style: DesignTokens.bodyStyle.copyWith(
                          color: DesignTokens.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}