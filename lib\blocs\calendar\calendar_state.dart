import 'package:equatable/equatable.dart';
import 'calendar_event.dart';

/// Base class for all calendar states
abstract class CalendarState extends Equatable {
  const CalendarState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class CalendarInitial extends CalendarState {
  const CalendarInitial();
}

/// Loading state
class CalendarLoading extends CalendarState {
  const CalendarLoading();
}

/// Calendar events loaded successfully
class CalendarLoaded extends CalendarState {
  final List<CalendarEventModel> events;
  final List<CalendarEventModel> filteredEvents;
  final DateTime currentDate;
  final DateTime selectedDate;
  final CalendarViewMode viewMode;
  final String? searchQuery;
  final String? selectedEventType;
  final Map<DateTime, List<CalendarEventModel>> eventsByDate;

  const CalendarLoaded({
    required this.events,
    required this.filteredEvents,
    required this.currentDate,
    required this.selectedDate,
    this.viewMode = CalendarViewMode.month,
    this.searchQuery,
    this.selectedEventType,
    required this.eventsByDate,
  });

  @override
  List<Object?> get props => [
        events,
        filteredEvents,
        currentDate,
        selectedDate,
        viewMode,
        searchQuery,
        selectedEventType,
        eventsByDate,
      ];

  CalendarLoaded copyWith({
    List<CalendarEventModel>? events,
    List<CalendarEventModel>? filteredEvents,
    DateTime? currentDate,
    DateTime? selectedDate,
    CalendarViewMode? viewMode,
    String? searchQuery,
    String? selectedEventType,
    Map<DateTime, List<CalendarEventModel>>? eventsByDate,
    bool clearSearchQuery = false,
    bool clearSelectedEventType = false,
  }) {
    return CalendarLoaded(
      events: events ?? this.events,
      filteredEvents: filteredEvents ?? this.filteredEvents,
      currentDate: currentDate ?? this.currentDate,
      selectedDate: selectedDate ?? this.selectedDate,
      viewMode: viewMode ?? this.viewMode,
      searchQuery: clearSearchQuery ? null : (searchQuery ?? this.searchQuery),
      selectedEventType: clearSelectedEventType ? null : (selectedEventType ?? this.selectedEventType),
      eventsByDate: eventsByDate ?? this.eventsByDate,
    );
  }

  /// Get events for a specific date
  List<CalendarEventModel> getEventsForDate(DateTime date) {
    final dateKey = DateTime(date.year, date.month, date.day);
    return eventsByDate[dateKey] ?? [];
  }

  /// Get events for the selected date
  List<CalendarEventModel> get selectedDateEvents => getEventsForDate(selectedDate);

  /// Get events for today
  List<CalendarEventModel> get todayEvents => getEventsForDate(DateTime.now());

  /// Get upcoming events (next 7 days)
  List<CalendarEventModel> get upcomingEvents {
    final now = DateTime.now();
    final nextWeek = now.add(const Duration(days: 7));
    
    return filteredEvents.where((event) {
      return event.startTime.isAfter(now) && event.startTime.isBefore(nextWeek);
    }).toList()..sort((a, b) => a.startTime.compareTo(b.startTime));
  }

  /// Get events for current week
  List<CalendarEventModel> get currentWeekEvents {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 7));
    
    return filteredEvents.where((event) {
      return event.startTime.isAfter(startOfWeek) && event.startTime.isBefore(endOfWeek);
    }).toList();
  }

  /// Get events for current month
  List<CalendarEventModel> get currentMonthEvents {
    final startOfMonth = DateTime(currentDate.year, currentDate.month, 1);
    final endOfMonth = DateTime(currentDate.year, currentDate.month + 1, 1);
    
    return filteredEvents.where((event) {
      return event.startTime.isAfter(startOfMonth) && event.startTime.isBefore(endOfMonth);
    }).toList();
  }

  /// Check if a date has events
  bool hasEventsOnDate(DateTime date) {
    return getEventsForDate(date).isNotEmpty;
  }

  /// Get total events count
  int get totalEventsCount => events.length;

  /// Get filtered events count
  int get filteredEventsCount => filteredEvents.length;
}

/// Calendar operation in progress
class CalendarOperationInProgress extends CalendarState {
  final String operation;
  final String? eventId;

  const CalendarOperationInProgress({
    required this.operation,
    this.eventId,
  });

  @override
  List<Object?> get props => [operation, eventId];
}

/// Calendar operation completed successfully
class CalendarOperationSuccess extends CalendarState {
  final String message;
  final String operation;
  final CalendarEventModel? event;

  const CalendarOperationSuccess({
    required this.message,
    required this.operation,
    this.event,
  });

  @override
  List<Object?> get props => [message, operation, event];
}

/// Calendar operation failed
class CalendarOperationError extends CalendarState {
  final String error;
  final String operation;
  final String? eventId;

  const CalendarOperationError({
    required this.error,
    required this.operation,
    this.eventId,
  });

  @override
  List<Object?> get props => [error, operation, eventId];
}

/// Calendar sync in progress
class CalendarSyncInProgress extends CalendarState {
  const CalendarSyncInProgress();
}

/// Calendar sync completed
class CalendarSyncSuccess extends CalendarState {
  final int syncedCount;
  final String message;

  const CalendarSyncSuccess({
    required this.syncedCount,
    required this.message,
  });

  @override
  List<Object?> get props => [syncedCount, message];
}

/// Calendar sync failed
class CalendarSyncError extends CalendarState {
  final String error;

  const CalendarSyncError(this.error);

  @override
  List<Object?> get props => [error];
}

/// Calendar export in progress
class CalendarExportInProgress extends CalendarState {
  const CalendarExportInProgress();
}

/// Calendar export completed
class CalendarExportSuccess extends CalendarState {
  final String filePath;
  final int exportedCount;
  final String format;

  const CalendarExportSuccess({
    required this.filePath,
    required this.exportedCount,
    required this.format,
  });

  @override
  List<Object?> get props => [filePath, exportedCount, format];
}

/// Calendar export failed
class CalendarExportError extends CalendarState {
  final String error;

  const CalendarExportError(this.error);

  @override
  List<Object?> get props => [error];
}

/// Calendar event model
class CalendarEventModel extends Equatable {
  final String id;
  final String title;
  final String description;
  final DateTime startTime;
  final DateTime endTime;
  final String type;
  final bool isAllDay;
  final String? location;
  final List<String> attendees;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CalendarEventModel({
    required this.id,
    required this.title,
    required this.description,
    required this.startTime,
    required this.endTime,
    required this.type,
    this.isAllDay = false,
    this.location,
    this.attendees = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        startTime,
        endTime,
        type,
        isAllDay,
        location,
        attendees,
        createdAt,
        updatedAt,
      ];

  CalendarEventModel copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? startTime,
    DateTime? endTime,
    String? type,
    bool? isAllDay,
    String? location,
    List<String>? attendees,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CalendarEventModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      type: type ?? this.type,
      isAllDay: isAllDay ?? this.isAllDay,
      location: location ?? this.location,
      attendees: attendees ?? this.attendees,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get duration of the event
  Duration get duration => endTime.difference(startTime);

  /// Check if event is happening now
  bool get isHappeningNow {
    final now = DateTime.now();
    return now.isAfter(startTime) && now.isBefore(endTime);
  }

  /// Check if event is in the past
  bool get isPast => DateTime.now().isAfter(endTime);

  /// Check if event is in the future
  bool get isFuture => DateTime.now().isBefore(startTime);

  /// Check if event is today
  bool get isToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final eventDate = DateTime(startTime.year, startTime.month, startTime.day);
    return eventDate == today;
  }
}
