import 'package:equatable/equatable.dart';
import '../../models/isar_models.dart';
import 'tasks_event.dart';

/// Base class for all tasks states
abstract class TasksState extends Equatable {
  const TasksState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class TasksInitial extends TasksState {
  const TasksInitial();
}

/// Loading state
class TasksLoading extends TasksState {
  const TasksLoading();
}

/// Tasks loaded successfully
class TasksLoaded extends TasksState {
  final List<LocalTask> tasks;
  final List<LocalTask> filteredTasks;
  final String? searchQuery;
  final TaskStatusFilter? statusFilter;
  final String? priorityFilter;
  final String? categoryFilter;
  final TaskDueDateFilter? dueDateFilter;
  final TasksSortType sortType;
  final Set<String> selectedTaskIds;
  final List<String> availableCategories;
  final List<String> availableTags;
  final bool isSelectionMode;

  const TasksLoaded({
    required this.tasks,
    required this.filteredTasks,
    this.searchQuery,
    this.statusFilter,
    this.priorityFilter,
    this.categoryFilter,
    this.dueDateFilter,
    this.sortType = TasksSortType.dueDateAsc,
    this.selectedTaskIds = const {},
    this.availableCategories = const [],
    this.availableTags = const [],
    this.isSelectionMode = false,
  });

  @override
  List<Object?> get props => [
        tasks,
        filteredTasks,
        searchQuery,
        statusFilter,
        priorityFilter,
        categoryFilter,
        dueDateFilter,
        sortType,
        selectedTaskIds,
        availableCategories,
        availableTags,
        isSelectionMode,
      ];

  TasksLoaded copyWith({
    List<LocalTask>? tasks,
    List<LocalTask>? filteredTasks,
    String? searchQuery,
    TaskStatusFilter? statusFilter,
    String? priorityFilter,
    String? categoryFilter,
    TaskDueDateFilter? dueDateFilter,
    TasksSortType? sortType,
    Set<String>? selectedTaskIds,
    List<String>? availableCategories,
    List<String>? availableTags,
    bool? isSelectionMode,
    bool clearSearchQuery = false,
    bool clearStatusFilter = false,
    bool clearPriorityFilter = false,
    bool clearCategoryFilter = false,
    bool clearDueDateFilter = false,
  }) {
    return TasksLoaded(
      tasks: tasks ?? this.tasks,
      filteredTasks: filteredTasks ?? this.filteredTasks,
      searchQuery: clearSearchQuery ? null : (searchQuery ?? this.searchQuery),
      statusFilter: clearStatusFilter ? null : (statusFilter ?? this.statusFilter),
      priorityFilter: clearPriorityFilter ? null : (priorityFilter ?? this.priorityFilter),
      categoryFilter: clearCategoryFilter ? null : (categoryFilter ?? this.categoryFilter),
      dueDateFilter: clearDueDateFilter ? null : (dueDateFilter ?? this.dueDateFilter),
      sortType: sortType ?? this.sortType,
      selectedTaskIds: selectedTaskIds ?? this.selectedTaskIds,
      availableCategories: availableCategories ?? this.availableCategories,
      availableTags: availableTags ?? this.availableTags,
      isSelectionMode: isSelectionMode ?? this.isSelectionMode,
    );
  }

  /// Get pending tasks
  List<LocalTask> get pendingTasks => filteredTasks.where((task) => !task.isCompleted).toList();

  /// Get completed tasks
  List<LocalTask> get completedTasks => filteredTasks.where((task) => task.isCompleted).toList();

  /// Get overdue tasks
  List<LocalTask> get overdueTasks {
    final now = DateTime.now();
    return filteredTasks.where((task) {
      return !task.isCompleted && 
             task.dueDate != null && 
             task.dueDate!.isBefore(now);
    }).toList();
  }

  /// Get tasks due today
  List<LocalTask> get tasksDueToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    
    return filteredTasks.where((task) {
      return task.dueDate != null &&
             task.dueDate!.isAfter(today) &&
             task.dueDate!.isBefore(tomorrow);
    }).toList();
  }

  /// Get tasks due this week
  List<LocalTask> get tasksDueThisWeek {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 7));
    
    return filteredTasks.where((task) {
      return task.dueDate != null &&
             task.dueDate!.isAfter(startOfWeek) &&
             task.dueDate!.isBefore(endOfWeek);
    }).toList();
  }

  /// Get high priority tasks
  List<LocalTask> get highPriorityTasks => 
      filteredTasks.where((task) => task.priority == 'high').toList();

  /// Get selected tasks
  List<LocalTask> get selectedTasks => 
      tasks.where((task) => selectedTaskIds.contains(task.serverId ?? task.id.toString())).toList();

  /// Check if any tasks are selected
  bool get hasSelectedTasks => selectedTaskIds.isNotEmpty;

  /// Get total tasks count
  int get totalTasksCount => tasks.length;

  /// Get filtered tasks count
  int get filteredTasksCount => filteredTasks.length;

  /// Get pending tasks count
  int get pendingTasksCount => pendingTasks.length;

  /// Get completed tasks count
  int get completedTasksCount => completedTasks.length;

  /// Get overdue tasks count
  int get overdueTasksCount => overdueTasks.length;

  /// Check if task is selected
  bool isTaskSelected(LocalTask task) {
    final taskId = task.serverId ?? task.id.toString();
    return selectedTaskIds.contains(taskId);
  }

  /// Get completion percentage
  double get completionPercentage {
    if (tasks.isEmpty) return 0.0;
    return completedTasks.length / tasks.length;
  }
}

/// Tasks operation in progress
class TasksOperationInProgress extends TasksState {
  final String operation;
  final String? taskId;

  const TasksOperationInProgress({
    required this.operation,
    this.taskId,
  });

  @override
  List<Object?> get props => [operation, taskId];
}

/// Tasks operation completed successfully
class TasksOperationSuccess extends TasksState {
  final String message;
  final String operation;
  final LocalTask? task;

  const TasksOperationSuccess({
    required this.message,
    required this.operation,
    this.task,
  });

  @override
  List<Object?> get props => [message, operation, task];
}

/// Tasks operation failed
class TasksOperationError extends TasksState {
  final String error;
  final String operation;
  final String? taskId;

  const TasksOperationError({
    required this.error,
    required this.operation,
    this.taskId,
  });

  @override
  List<Object?> get props => [error, operation, taskId];
}

/// Tasks sync in progress
class TasksSyncInProgress extends TasksState {
  const TasksSyncInProgress();
}

/// Tasks sync completed
class TasksSyncSuccess extends TasksState {
  final int syncedCount;
  final String message;

  const TasksSyncSuccess({
    required this.syncedCount,
    required this.message,
  });

  @override
  List<Object?> get props => [syncedCount, message];
}

/// Tasks sync failed
class TasksSyncError extends TasksState {
  final String error;

  const TasksSyncError(this.error);

  @override
  List<Object?> get props => [error];
}

/// Tasks export in progress
class TasksExportInProgress extends TasksState {
  const TasksExportInProgress();
}

/// Tasks export completed
class TasksExportSuccess extends TasksState {
  final String filePath;
  final int exportedCount;
  final String format;

  const TasksExportSuccess({
    required this.filePath,
    required this.exportedCount,
    required this.format,
  });

  @override
  List<Object?> get props => [filePath, exportedCount, format];
}

/// Tasks export failed
class TasksExportError extends TasksState {
  final String error;

  const TasksExportError(this.error);

  @override
  List<Object?> get props => [error];
}

/// Bulk operation in progress
class TasksBulkOperationInProgress extends TasksState {
  final String operation;
  final int totalCount;

  const TasksBulkOperationInProgress({
    required this.operation,
    required this.totalCount,
  });

  @override
  List<Object?> get props => [operation, totalCount];
}

/// Bulk operation completed
class TasksBulkOperationSuccess extends TasksState {
  final String operation;
  final int processedCount;
  final String message;

  const TasksBulkOperationSuccess({
    required this.operation,
    required this.processedCount,
    required this.message,
  });

  @override
  List<Object?> get props => [operation, processedCount, message];
}

/// Bulk operation failed
class TasksBulkOperationError extends TasksState {
  final String operation;
  final String error;

  const TasksBulkOperationError({
    required this.operation,
    required this.error,
  });

  @override
  List<Object?> get props => [operation, error];
}
