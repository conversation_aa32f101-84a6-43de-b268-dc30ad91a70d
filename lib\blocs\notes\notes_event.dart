import 'package:equatable/equatable.dart';

/// Base class for all notes events
abstract class Notes<PERSON>vent extends Equatable {
  const NotesEvent();

  @override
  List<Object?> get props => [];
}

/// Load all notes
class LoadNotes extends NotesEvent {
  const LoadNotes();
}

/// Refresh notes from server
class RefreshNotes extends NotesEvent {
  const RefreshNotes();
}

/// Add a new note
class AddNote extends NotesEvent {
  final String title;
  final String content;
  final List<String> tags;
  final bool isPinned;

  const AddNote({
    required this.title,
    required this.content,
    this.tags = const [],
    this.isPinned = false,
  });

  @override
  List<Object?> get props => [title, content, tags, isPinned];
}

/// Update an existing note
class UpdateNote extends NotesEvent {
  final String noteId;
  final String? title;
  final String? content;
  final List<String>? tags;
  final bool? isPinned;

  const UpdateNote({
    required this.noteId,
    this.title,
    this.content,
    this.tags,
    this.isPinned,
  });

  @override
  List<Object?> get props => [noteId, title, content, tags, isPinned];
}

/// Delete a note
class DeleteNote extends NotesEvent {
  final String noteId;

  const DeleteNote(this.noteId);

  @override
  List<Object?> get props => [noteId];
}

/// Toggle pin status of a note
class TogglePinNote extends NotesEvent {
  final String noteId;

  const TogglePinNote(this.noteId);

  @override
  List<Object?> get props => [noteId];
}

/// Search notes by query
class SearchNotes extends NotesEvent {
  final String query;

  const SearchNotes(this.query);

  @override
  List<Object?> get props => [query];
}

/// Filter notes by tag
class FilterNotesByTag extends NotesEvent {
  final String? tag;

  const FilterNotesByTag(this.tag);

  @override
  List<Object?> get props => [tag];
}

/// Sort notes
class SortNotes extends NotesEvent {
  final NotesSortType sortType;

  const SortNotes(this.sortType);

  @override
  List<Object?> get props => [sortType];
}

/// Clear search and filters
class ClearNotesFilters extends NotesEvent {
  const ClearNotesFilters();
}

/// Select/deselect note for bulk operations
class ToggleNoteSelection extends NotesEvent {
  final String noteId;

  const ToggleNoteSelection(this.noteId);

  @override
  List<Object?> get props => [noteId];
}

/// Clear all selections
class ClearNoteSelections extends NotesEvent {
  const ClearNoteSelections();
}

/// Delete selected notes
class DeleteSelectedNotes extends NotesEvent {
  const DeleteSelectedNotes();
}

/// Pin selected notes
class PinSelectedNotes extends NotesEvent {
  const PinSelectedNotes();
}

/// Unpin selected notes
class UnpinSelectedNotes extends NotesEvent {
  const UnpinSelectedNotes();
}

/// Add tag to selected notes
class AddTagToSelectedNotes extends NotesEvent {
  final String tag;

  const AddTagToSelectedNotes(this.tag);

  @override
  List<Object?> get props => [tag];
}

/// Export notes
class ExportNotes extends NotesEvent {
  final List<String>? noteIds; // null means export all
  final String format; // 'json', 'txt', 'markdown'

  const ExportNotes({
    this.noteIds,
    this.format = 'json',
  });

  @override
  List<Object?> get props => [noteIds, format];
}

/// Sync notes with server
class SyncNotes extends NotesEvent {
  const SyncNotes();
}

/// Notes sort types
enum NotesSortType {
  createdAtDesc,
  createdAtAsc,
  updatedAtDesc,
  updatedAtAsc,
  titleAsc,
  titleDesc,
  pinned,
}
