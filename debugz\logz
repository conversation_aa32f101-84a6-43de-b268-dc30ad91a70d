Launching lib\main.dart on 21081111RG in debug mode...
√ Built build\app\outputs\flutter-apk\app-debug.apk
I/flutter (20780): [IMPORTANT:flutter/shell/platform/android/android_context_vk_impeller.cc(61)] Using the Impeller rendering backend (Vulkan).
W/FlutterWebRTCPlugin(20780): audioFocusChangeListener [Earpiece(name=Earpiece)] Earpiece(name=Earpiece)
W/FlutterWebRTCPlugin(20780): audioFocusChangeListener [Speakerphone(name=Speakerphone), Earpiece(name=Earpiece)] Speakerphone(name=Speakerphone)
W/FlutterWebRTCPlugin(20780): audioFocusChangeListener [BluetoothHeadset(name=soundcore Liberty 4 NC), Speakerphone(name=Speakerphone), Earpiece(name=Earpiece)] BluetoothHeadset(name=soundcore Liberty 4 NC)
I/com.drix.app(20780): <PERSON>mpiler allocated 6491KB to compile void android.view.ViewRootImpl.performTraversals()
Connecting to VM Service at ws://127.0.0.1:14834/MUuWt6pKr-Y=/ws
Connected to the VM Service.
D/CompatibilityChangeReporter(20780): Compat change id reported: 3400644; UID 10202; state: ENABLED
W/libc    (20780): Access denied finding property "persist.vendor.debug.gpud.enable"
E/LB      (20780): fail to open file: No such file or directory
E/LB      (20780): fail to open node: No such file or directory
W/libc    (20780): Access denied finding property "ro.vendor.display.iris_x7.support"
D/BLASTBufferQueue(20780): [SurfaceView[com.drix.app/com.drix.app.MainActivity]#1](f:0,a:1) acquireNextBufferLocked size=1080x2270 mFrameNumber=1 applyTransaction=true mTimestamp=19685056287941(auto) mPendingTransactions.size=0 graphicBufferId=89249420410894 transform=0
I/Choreographer(20780): Skipped 149 frames!  The application may be doing too much work on its main thread.
W/Looper  (20780): PerfMonitor doFrame : time=1ms vsyncFrame=0 latency=1239ms procState=-1
I/Choreographer(20780): Skipped 89 frames!  The application may be doing too much work on its main thread.
D/VRI[MainActivity](20780): vri.Setup new sync=wmsSync-VRI[MainActivity]#2
D/OpenGLRenderer(20780): makeCurrent grContext:0xb400006f6b6f4a00 reset mTextureAvailable
D/com.drix.app(20780): MiuiProcessManagerServiceStub setSchedFifo
I/MiuiProcessManagerImpl(20780): setSchedFifo pid:20780, mode:3
W/libc    (20780): Access denied finding property "ro.vendor.display.iris_x7.support"
D/BLASTBufferQueue(20780): [VRI[MainActivity]#0](f:0,a:1) acquireNextBufferLocked size=1080x2400 mFrameNumber=1 applyTransaction=true mTimestamp=19686062339865(auto) mPendingTransactions.size=0 graphicBufferId=89249420410899 transform=0
D/VRI[MainActivity](20780): vri.reportDrawFinished
W/Looper  (20780): PerfMonitor doFrame : time=145ms vsyncFrame=0 latency=747ms procState=-1 historyMsgCount=5
I/OpenGLRenderer(20780): Davey! duration=891ms; Flags=1, FrameTimelineVsyncId=6858829, IntendedVsync=19685171501236, Vsync=19685913167873, InputEventId=0, HandleInputStart=19685918965788, AnimationStart=19685918969403, PerformTraversalsStart=19685918971018, DrawStart=19685926675326, FrameDeadline=19685181501236, FrameInterval=19685918598403, FrameStartTime=8333333, SyncQueued=19685930763249, SyncStart=19685930919634, IssueDrawCommandsStart=19685933276557, SwapBuffers=19686059457095, FrameCompleted=19686062855941, DequeueBufferDuration=3227693, QueueBufferDuration=379692, GpuCompleted=19686062851249, SwapBuffersCompleted=19686062855941, DisplayPresentTime=0, CommandSubmissionCompleted=19686059457095, 
I/HandWritingStubImpl(20780): refreshLastKeyboardType: 1
I/HandWritingStubImpl(20780): getCurrentKeyboardType: 1
D/ProfileInstaller(20780): Installing profile for com.drix.app
I/HandWritingStubImpl(20780): getCurrentKeyboardType: 1
W/FinalizerDaemon(20780): type=1400 audit(0.0:17619): avc:  denied  { getopt } for  path="/dev/socket/usap_pool_primary" scontext=u:r:untrusted_app:s0:c202,c256,c512,c768 tcontext=u:r:zygote:s0 tclass=unix_stream_socket permissive=0 app=com.drix.app
2
I/ScrollIdentify(20780): on fling
W/WindowOnBackDispatcher(20780): OnBackInvokedCallback is not enabled for the application.
W/WindowOnBackDispatcher(20780): Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
I/flutter (20780): 🔍 Starting Google Sign-In process...
D/SessionLifecycleClient(20780): Sending lifecycle 2 to service
D/SessionLifecycleService(20780): Activity backgrounding at 19702958
D/libMEOW (20780): meow new tls: 0xb400006f73413f40
D/libMEOW (20780): applied 1 plugins for [com.drix.app]:
D/libMEOW (20780):   plugin 1: [libMEOW_gift.so]: 0xb40000700697ed00
D/libMEOW (20780): rebuild call chain: 0xb400006f73705680
D/libMEOW (20780): meow delete tls: 0xb400006f73413f40
D/DecorView[](20780): getWindowModeFromSystem  windowmode is 1
D/SessionLifecycleClient(20780): Sending lifecycle 1 to service
D/SessionLifecycleService(20780): Activity foregrounding at 19703028.
W/libc    (20780): Access denied finding property "ro.vendor.display.iris_x7.support"
I/ForceDarkHelperStubImpl(20780): setViewRootImplForceDark: false for com.google.android.gms.auth.api.signin.internal.SignInHubActivity@7dacc2c, reason: AppDarkModeEnable
D/VRI[SignInHubActivity](20780): hardware acceleration = true, forceHwAccelerated = false
D/SessionLifecycleClient(20780): Sending lifecycle 2 to service
D/SessionLifecycleService(20780): Activity backgrounding at 19703046
D/BufferQueueConsumer(20780): [](id:512c00000002,api:0,p:-1,c:20780) connect: controlledByApp=false
E/OpenGLRenderer(20780): Unable to match the desired swap behavior.
D/VRI[SignInHubActivity](20780): vri.reportNextDraw android.view.ViewRootImpl.performTraversals:4376 android.view.ViewRootImpl.doTraversal:2992 android.view.ViewRootImpl$TraversalRunnable.run:10469 android.view.Choreographer$CallbackRecord.run:1671 android.view.Choreographer$CallbackRecord.run:1680 
D/VRI[SignInHubActivity](20780): vri.Setup new sync=wmsSync-VRI[SignInHubActivity]#4
D/OpenGLRenderer(20780): makeCurrent grContext:0xb400006f6b6f4a00 reset mTextureAvailable
D/BLASTBufferQueue(20780): [VRI[SignInHubActivity]#2](f:0,a:1) acquireNextBufferLocked size=1080x2400 mFrameNumber=1 applyTransaction=true mTimestamp=19703097169173(auto) mPendingTransactions.size=0 graphicBufferId=89249420410900 transform=0
D/VRI[SignInHubActivity](20780): vri.reportDrawFinished
I/TRuntime.CctTransportBackend(20780): Making request to: https://firebaselogging-pa.googleapis.com/v1/firelog/legacy/batchlog
D/TrafficStats(20780): tagSocket(198) with statsTag=0xffffffff, statsUid=-1
D/CompatibilityChangeReporter(20780): Compat change id reported: ********; UID 10202; state: ENABLED
D/SessionLifecycleClient(20780): Sending lifecycle 1 to service
D/SessionLifecycleService(20780): Activity foregrounding at ********.
D/SessionLifecycleClient(20780): Sending lifecycle 2 to service
D/SessionLifecycleService(20780): Activity backgrounding at ********
D/SessionLifecycleClient(20780): Sending lifecycle 1 to service
D/SessionLifecycleService(20780): Activity foregrounding at ********.
I/flutter (20780): ✅ Google account selected: <EMAIL>
W/libc    (20780): Access denied finding property "ro.vendor.display.iris_x7.support"
W/WindowOnBackDispatcher(20780): sendCancelIfRunning: isInProgress=falsecallback=android.view.ViewRootImpl$$ExternalSyntheticLambda22@d7496f4
W/MiuiStubRegistry(20780): Failed to collect stub providers in android.magicpointer.util.MiuiMagicPointerUtilsStubHeadManifest$$: /system_ext/framework/miui-framework-pointer-pad.jar not exist
D/BLASTBufferQueue(20780): [VRI[SignInHubActivity]#2](f:0,a:1) destructor()
D/BufferQueueConsumer(20780): [VRI[SignInHubActivity]#2(BLAST Consumer)2](id:512c00000002,api:0,p:-1,c:20780) disconnect
D/View    (20780): [Warning] assignParent to null: this = DecorView@ed19206[SignInHubActivity]
I/HandWritingStubImpl(20780): refreshLastKeyboardType: 1
I/HandWritingStubImpl(20780): getCurrentKeyboardType: 1
I/flutter (20780): ✅ Google authentication tokens obtained
I/flutter (20780):  Signing in with Firebase...
W/System  (20780): Ignoring header X-Firebase-Locale because its value was null.
D/TrafficStats(20780): tagSocket(210) with statsTag=0xffffffff, statsUid=-1
W/System  (20780): Ignoring header X-Firebase-Locale because its value was null.
I/TRuntime.CctTransportBackend(20780): Status Code: 200
D/FirebaseAuth(20780): Notifying id token listeners about user ( hWnMGnwebUQWrQSArw3Acf7zCDW2 ).
D/FirebaseAuth(20780): Notifying auth state listeners about user ( hWnMGnwebUQWrQSArw3Acf7zCDW2 ).
I/flutter (20780): ❌ Unexpected error during Google Sign-In: type 'List<Object?>' is not a subtype of type 'PigeonUserDetails?' in type cast
W/WindowOnBackDispatcher(20780): OnBackInvokedCallback is not enabled for the application.
W/WindowOnBackDispatcher(20780): Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
5
E/BLASTBufferQueue(20780): [SurfaceView[com.drix.app/com.drix.app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 