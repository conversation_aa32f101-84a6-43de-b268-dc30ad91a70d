import 'package:isar/isar.dart';

part 'notification_models.g.dart';

/// Notification types enum
enum NotificationType {
  therapyReminder,
  taskNotification,
  dailyCheckIn,
  weeklyReport,
  systemUpdate,
  contextualAI,
}

/// Notification priority levels
enum NotificationPriority {
  low,
  normal,
  high,
  urgent,
}

/// Local notification model for Isar database
@collection
class LocalNotification {
  Id id = Isar.autoIncrement;
  
  @Index()
  String? serverId; // Server-side ID after sync
  
  late String title;
  late String body;
  
  @Enumerated(EnumType.name)
  late NotificationType type;
  
  @Enumerated(EnumType.name)
  late NotificationPriority priority;
  
  late DateTime scheduledTime;
  late DateTime createdAt;
  
  bool isRead = false;
  bool isDelivered = false;
  bool isInteracted = false;
  
  // Navigation data for when notification is tapped
  String? navigationRoute;
  String? navigationData; // JSON string for additional data
  
  // Retry logic for failed notifications
  int deliveryRetryCount = 0;
  DateTime? lastDeliveryAttempt;
  
  @Index()
  late bool needsSync;
  
  @Index()
  late String syncStatus; // 'synced', 'pending', 'error'
}

/// Notification settings model
@collection
class NotificationSettings {
  Id id = Isar.autoIncrement;
  
  // Notification type toggles
  bool therapyReminders = true;
  bool taskNotifications = true;
  bool dailyCheckIns = false;
  bool weeklyReports = true;
  bool systemUpdates = false;
  bool contextualAI = true;
  
  // Quiet hours
  bool quietHoursEnabled = false;
  int quietHoursStartHour = 22;
  int quietHoursStartMinute = 0;
  int quietHoursEndHour = 8;
  int quietHoursEndMinute = 0;
  
  // Frequency control
  String notificationFrequency = 'Normal'; // 'Minimal', 'Normal', 'Frequent'
  
  // Advanced settings
  bool soundEnabled = true;
  bool vibrationEnabled = true;
  bool badgeCountEnabled = true;
  
  late DateTime updatedAt;
  
  @Index()
  late bool needsSync;
}

/// Notification analytics model
@collection
class NotificationAnalytics {
  Id id = Isar.autoIncrement;
  
  @Index()
  String? serverId;
  
  @Index()
  late String notificationId; // Reference to LocalNotification
  
  @Enumerated(EnumType.name)
  late NotificationType type;
  
  late DateTime deliveredAt;
  DateTime? readAt;
  DateTime? interactedAt;
  DateTime? dismissedAt;
  
  // Interaction details
  String? interactionType; // 'tap', 'swipe', 'button_click'
  String? interactionData; // Additional context
  
  late DateTime createdAt;
  
  @Index()
  late bool needsSync;
}

/// Data transfer objects for API communication
class NotificationDto {
  final String? id;
  final String title;
  final String body;
  final NotificationType type;
  final NotificationPriority priority;
  final DateTime scheduledTime;
  final String? navigationRoute;
  final Map<String, dynamic>? navigationData;

  NotificationDto({
    this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.priority,
    required this.scheduledTime,
    this.navigationRoute,
    this.navigationData,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'body': body,
    'type': type.name,
    'priority': priority.name,
    'scheduled_time': scheduledTime.toIso8601String(),
    'navigation_route': navigationRoute,
    'navigation_data': navigationData,
  };

  factory NotificationDto.fromJson(Map<String, dynamic> json) => NotificationDto(
    id: json['id'],
    title: json['title'],
    body: json['body'],
    type: NotificationType.values.firstWhere((e) => e.name == json['type']),
    priority: NotificationPriority.values.firstWhere((e) => e.name == json['priority']),
    scheduledTime: DateTime.parse(json['scheduled_time']),
    navigationRoute: json['navigation_route'],
    navigationData: json['navigation_data'],
  );
}

class NotificationSettingsDto {
  final bool therapyReminders;
  final bool taskNotifications;
  final bool dailyCheckIns;
  final bool weeklyReports;
  final bool systemUpdates;
  final bool contextualAI;
  final bool quietHoursEnabled;
  final int quietHoursStartHour;
  final int quietHoursStartMinute;
  final int quietHoursEndHour;
  final int quietHoursEndMinute;
  final String notificationFrequency;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final bool badgeCountEnabled;

  NotificationSettingsDto({
    required this.therapyReminders,
    required this.taskNotifications,
    required this.dailyCheckIns,
    required this.weeklyReports,
    required this.systemUpdates,
    required this.contextualAI,
    required this.quietHoursEnabled,
    required this.quietHoursStartHour,
    required this.quietHoursStartMinute,
    required this.quietHoursEndHour,
    required this.quietHoursEndMinute,
    required this.notificationFrequency,
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.badgeCountEnabled,
  });

  Map<String, dynamic> toJson() => {
    'therapy_reminders': therapyReminders,
    'task_notifications': taskNotifications,
    'daily_check_ins': dailyCheckIns,
    'weekly_reports': weeklyReports,
    'system_updates': systemUpdates,
    'contextual_ai': contextualAI,
    'quiet_hours_enabled': quietHoursEnabled,
    'quiet_hours_start_hour': quietHoursStartHour,
    'quiet_hours_start_minute': quietHoursStartMinute,
    'quiet_hours_end_hour': quietHoursEndHour,
    'quiet_hours_end_minute': quietHoursEndMinute,
    'notification_frequency': notificationFrequency,
    'sound_enabled': soundEnabled,
    'vibration_enabled': vibrationEnabled,
    'badge_count_enabled': badgeCountEnabled,
  };

  factory NotificationSettingsDto.fromJson(Map<String, dynamic> json) => NotificationSettingsDto(
    therapyReminders: json['therapy_reminders'] ?? true,
    taskNotifications: json['task_notifications'] ?? true,
    dailyCheckIns: json['daily_check_ins'] ?? false,
    weeklyReports: json['weekly_reports'] ?? true,
    systemUpdates: json['system_updates'] ?? false,
    contextualAI: json['contextual_ai'] ?? true,
    quietHoursEnabled: json['quiet_hours_enabled'] ?? false,
    quietHoursStartHour: json['quiet_hours_start_hour'] ?? 22,
    quietHoursStartMinute: json['quiet_hours_start_minute'] ?? 0,
    quietHoursEndHour: json['quiet_hours_end_hour'] ?? 8,
    quietHoursEndMinute: json['quiet_hours_end_minute'] ?? 0,
    notificationFrequency: json['notification_frequency'] ?? 'Normal',
    soundEnabled: json['sound_enabled'] ?? true,
    vibrationEnabled: json['vibration_enabled'] ?? true,
    badgeCountEnabled: json['badge_count_enabled'] ?? true,
  );
}
