import 'package:dio/dio.dart';
import 'auth_service_interface.dart';

/// Interceptor that automatically adds authentication headers to API requests
class AuthInterceptor extends Interceptor {
  final AuthServiceInterface _authService;

  AuthInterceptor(this._authService);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    try {
      // Skip auth for certain endpoints
      if (_shouldSkipAuth(options.path)) {
        return handler.next(options);
      }

      // Get access token
      final accessToken = await _authService.getAccessToken();
      
      if (accessToken != null) {
        options.headers['Authorization'] = 'Bearer $accessToken';
      }

      handler.next(options);
    } catch (e) {
      // If we can't get the token, continue without auth
      // The API will return 401 if auth is required
      handler.next(options);
    }
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Handle 401 Unauthorized - try to refresh token
    if (err.response?.statusCode == 401) {
      try {
        // Attempt to refresh the token
        await _authService.refreshAccessToken();
        
        // Retry the original request with new token
        final accessToken = await _authService.getAccessToken();
        if (accessToken != null) {
          err.requestOptions.headers['Authorization'] = 'Bearer $accessToken';
          
          // Create a new Dio instance to avoid interceptor loops
          final dio = Dio();

          final response = await dio.request(
            err.requestOptions.path,
            data: err.requestOptions.data,
            queryParameters: err.requestOptions.queryParameters,
            options: Options(
              method: err.requestOptions.method,
              headers: err.requestOptions.headers,
            ),
          );
          
          return handler.resolve(response);
        }
      } catch (refreshError) {
        // If refresh fails, the user needs to log in again
        // Let the error propagate to trigger re-authentication
      }
    }

    handler.next(err);
  }

  /// Determine if authentication should be skipped for this endpoint
  bool _shouldSkipAuth(String path) {
    final skipAuthPaths = [
      '/auth/login',
      '/auth/register',
      '/auth/refresh',
      '/auth/firebase-exchange',
      '/auth/forgot-password',
    ];

    return skipAuthPaths.any((skipPath) => path.contains(skipPath));
  }
}
