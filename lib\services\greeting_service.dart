import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'api_service.dart';

/// Service for handling dynamic greetings and session tracking
class GreetingService {
  final ApiService _apiService;
  final FlutterSecureStorage _secureStorage;
  
  static const String _lastSessionKey = 'last_session_date';
  static const String _sessionCountKey = 'daily_session_count';
  static const String _firstSessionTodayKey = 'first_session_today';

  GreetingService({
    required ApiService apiService,
    required FlutterSecureStorage secureStorage,
  }) : _apiService = apiService,
       _secureStorage = secureStorage;

  /// Generate dynamic greeting based on time and session count
  Future<String> generateGreeting(String userName) async {
    try {
      // Try to get session data from backend first
      final sessionData = await _apiService.getUserSessionData();
      final dailySessions = sessionData['daily_sessions'] as int? ?? 0;

      // Update session count on backend
      await _apiService.updateUserSession();

      if (dailySessions == 0) {
        // First session of the day - time-based greeting
        return _getTimeBasedGreeting(userName, DateTime.now());
      } else {
        // Subsequent sessions - casual greeting
        return _getCasualGreeting(userName);
      }
    } catch (e) {
      print('⚠️ Backend session tracking unavailable, using local fallback: $e');
      // Fallback to local session tracking when backend is unavailable
      return await _generateLocalGreeting(userName);
    }
  }

  /// Fallback method for local greeting generation
  Future<String> _generateLocalGreeting(String userName) async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      // Check if this is the first session today
      final isFirstSessionToday = await _isFirstSessionToday(today);

      if (isFirstSessionToday) {
        // First session - time-based greeting
        await _recordFirstSessionToday(today);
        return _getTimeBasedGreeting(userName, now);
      } else {
        // Subsequent sessions - casual greeting
        await _incrementSessionCount();
        return _getCasualGreeting(userName);
      }
    } catch (e) {
      // Final fallback to time-based greeting if session tracking fails
      return _getTimeBasedGreeting(userName, DateTime.now());
    }
  }

  /// Check if this is the first session today
  Future<bool> _isFirstSessionToday(DateTime today) async {
    try {
      final lastSessionStr = await _secureStorage.read(key: _lastSessionKey);
      if (lastSessionStr == null) return true;
      
      final lastSession = DateTime.parse(lastSessionStr);
      final lastSessionDate = DateTime(lastSession.year, lastSession.month, lastSession.day);
      
      return !lastSessionDate.isAtSameMomentAs(today);
    } catch (e) {
      return true; // Default to first session if parsing fails
    }
  }

  /// Record the first session of the day
  Future<void> _recordFirstSessionToday(DateTime today) async {
    try {
      await _secureStorage.write(key: _lastSessionKey, value: today.toIso8601String());
      await _secureStorage.write(key: _sessionCountKey, value: '1');
      await _secureStorage.write(key: _firstSessionTodayKey, value: 'true');
      
      // Update backend session tracking
      await _apiService.updateUserSession();
    } catch (e) {
      // Continue silently if backend update fails
    }
  }

  /// Increment session count for the day
  Future<void> _incrementSessionCount() async {
    try {
      final countStr = await _secureStorage.read(key: _sessionCountKey) ?? '1';
      final count = int.tryParse(countStr) ?? 1;
      await _secureStorage.write(key: _sessionCountKey, value: (count + 1).toString());
      await _secureStorage.write(key: _firstSessionTodayKey, value: 'false');
      
      // Update backend session tracking
      await _apiService.updateUserSession();
    } catch (e) {
      // Continue silently if update fails
    }
  }

  /// Generate time-based greeting for first session
  String _getTimeBasedGreeting(String userName, DateTime time) {
    final hour = time.hour;
    
    if (hour >= 5 && hour < 12) {
      return 'Good morning, $userName';
    } else if (hour >= 12 && hour < 17) {
      return 'Good afternoon, $userName';
    } else if (hour >= 17 && hour < 22) {
      return 'Good evening, $userName';
    } else {
      return 'Good night, $userName';
    }
  }

  /// Generate casual greeting for subsequent sessions
  String _getCasualGreeting(String userName) {
    final casualGreetings = [
      'What\'s up, $userName?',
      'Hey there, $userName!',
      'Welcome back, $userName!',
      'Good to see you again, $userName!',
      'How\'s it going, $userName?',
    ];
    
    // Use current time as seed for consistent but varied greetings
    final seed = DateTime.now().hour + DateTime.now().minute;
    return casualGreetings[seed % casualGreetings.length];
  }

  /// Get current session count for the day
  Future<int> getDailySessionCount() async {
    try {
      final countStr = await _secureStorage.read(key: _sessionCountKey) ?? '0';
      return int.tryParse(countStr) ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// Check if this is the first session today (for external use)
  Future<bool> isFirstSessionToday() async {
    try {
      final firstSessionStr = await _secureStorage.read(key: _firstSessionTodayKey);
      return firstSessionStr == 'true';
    } catch (e) {
      return true;
    }
  }
}
