/// Abstract interface for authentication services
/// This allows us to swap between Firebase Auth and Mock Auth implementations

abstract class AuthServiceInterface {
  /// Stream of authentication state changes
  Stream<dynamic> get authStateChanges;

  /// Get current user
  dynamic get currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated;

  /// Sign in with email and password
  Future<dynamic> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  /// Create account with email and password
  Future<dynamic> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  });

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email);

  /// Sign out
  Future<void> signOut();

  /// Get access token
  Future<String?> getAccessToken();

  /// Refresh access token
  Future<void> refreshAccessToken();

  /// Sign in with Google
  Future<dynamic> signInWithGoogle();

  /// Sign up with Google
  Future<dynamic> signUpWithGoogle();
}
