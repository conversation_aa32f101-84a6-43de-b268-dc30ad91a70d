import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:ui';
import '../../../utils/design_tokens.dart';
import '../models/category.dart';
import 'category_creation_flow.dart';

class CategoriesSidebar extends StatefulWidget {
  final TaskCategory? selectedCategory;
  final Function(TaskCategory?) onCategorySelected;
  final VoidCallback onClose;
  final Function(TaskCategory)? onCategoryCreated;
  final List<TaskCategory>? customCategories;

  const CategoriesSidebar({
    super.key,
    required this.selectedCategory,
    required this.onCategorySelected,
    required this.onClose,
    this.onCategoryCreated,
    this.customCategories,
  });

  @override
  State<CategoriesSidebar> createState() => _CategoriesSidebarState();
}

class _CategoriesSidebarState extends State<CategoriesSidebar> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.70,
      height: double.infinity,
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(DesignTokens.borderRadiusLg),
          bottomRight: Radius.circular(DesignTokens.borderRadiusLg),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: Container(
            decoration: BoxDecoration(
              color: DesignTokens.backgroundSideMenu.withOpacity(0.9),
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(DesignTokens.borderRadiusLg),
                bottomRight: Radius.circular(DesignTokens.borderRadiusLg),
              ),
              border: Border.all(
                color: Colors.white.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(),
                  _buildAllCategoriesOption(),
                  const Divider(
                    color: Colors.white12,
                    height: 1,
                  ),
                  Expanded(
                    child: _buildCategoriesList(),
                  ),
                  _buildAddCategoryButton(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(DesignTokens.spacingLg),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: DesignTokens.primaryInteractiveBlue.withOpacity(0.2),
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              border: Border.all(
                color: DesignTokens.primaryInteractiveBlue.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: const Icon(
              Icons.category,
              color: DesignTokens.primaryInteractiveBlue,
              size: 20,
            ),
          ),
          const SizedBox(width: DesignTokens.spacingMd),
          const Text(
            'Categories',
            style: DesignTokens.cardTitleStyle,
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.close, color: DesignTokens.iconPrimary),
            onPressed: widget.onClose,
          ),
        ],
      ),
    );
  }

  Widget _buildAllCategoriesOption() {
    final isSelected = widget.selectedCategory == null;

    return GestureDetector(
      onTap: () => widget.onCategorySelected(null),
      child: Container(
        margin: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingLg,
          vertical: DesignTokens.spacingXs,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingSm,
        ),
        decoration: BoxDecoration(
          color: isSelected 
              ? DesignTokens.primaryInteractiveBlue.withOpacity(0.2)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          border: isSelected
              ? Border.all(
                  color: DesignTokens.primaryInteractiveBlue.withOpacity(0.5),
                  width: 1,
                )
              : null,
        ),
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: DesignTokens.textSecondary.withOpacity(0.2),
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
              ),
              child: const Icon(
                Icons.all_inclusive,
                color: DesignTokens.textSecondary,
                size: 18,
              ),
            ),
            const SizedBox(width: DesignTokens.spacingMd),
            Text(
              'All Categories',
              style: DesignTokens.bodyStyle.copyWith(
                color: isSelected 
                    ? DesignTokens.textPrimary 
                    : DesignTokens.textSecondary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesList() {
    final allCategories = TaskCategories.getAllCategories(widget.customCategories);

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingLg),
      itemCount: allCategories.length,
      itemBuilder: (context, index) {
        final category = allCategories[index];
        return _buildCategoryItem(category);
      },
    );
  }

  Widget _buildCategoryItem(TaskCategory category) {
    final isSelected = widget.selectedCategory?.id == category.id;

    return GestureDetector(
      onTap: () => widget.onCategorySelected(category),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: DesignTokens.spacingXs),
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingSm,
        ),
        decoration: BoxDecoration(
          color: isSelected 
              ? category.color.withOpacity(0.2)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          border: isSelected
              ? Border.all(
                  color: category.color.withOpacity(0.5),
                  width: 1,
                )
              : null,
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                category.name,
                style: DesignTokens.bodyStyle.copyWith(
                  color: isSelected 
                      ? category.color 
                      : DesignTokens.textSecondary,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                ),
              ),
            ),
            const SizedBox(width: DesignTokens.spacingMd),
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: category.color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
                border: Border.all(
                  color: category.color.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(8), // Increased padding
                  child: SvgPicture.asset(
                    category.iconPath,
                    width: 18,
                    height: 18,
                    colorFilter: ColorFilter.mode(
                      category.color,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check,
                color: category.color,
                size: 18,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddCategoryButton() {
    return Container(
      margin: const EdgeInsets.all(DesignTokens.spacingLg),
      child: SizedBox(
        width: double.infinity,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Container(
              decoration: BoxDecoration(
                color: DesignTokens.primaryInteractiveBlue.withOpacity(0.2),
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                border: Border.all(
                  color: DesignTokens.primaryInteractiveBlue.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: _showCategoryCreationFlow,
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: DesignTokens.spacingMd,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.add,
                          color: Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: DesignTokens.spacingSm),
                        Text(
                          'Add Category',
                          style: DesignTokens.bodyStyle.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showCategoryCreationFlow() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CategoryCreationFlow(
        onCategoryCreated: (category) {
          if (widget.onCategoryCreated != null) {
            widget.onCategoryCreated!(category);
          }
        },
      ),
    );
  }
}
