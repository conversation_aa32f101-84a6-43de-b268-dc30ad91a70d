import 'package:equatable/equatable.dart';

/// Dashboard events following the AuthBloc pattern
abstract class DashboardEvent extends Equatable {
  const DashboardEvent();

  @override
  List<Object> get props => [];
}

/// Event to load dashboard data
class LoadDashboard extends DashboardEvent {
  const LoadDashboard();
}

/// Event to refresh dashboard data
class RefreshDashboard extends DashboardEvent {
  const RefreshDashboard();
}

/// Event to update user greeting
class UpdateGreeting extends DashboardEvent {
  final String greeting;

  const UpdateGreeting(this.greeting);

  @override
  List<Object> get props => [greeting];
}
