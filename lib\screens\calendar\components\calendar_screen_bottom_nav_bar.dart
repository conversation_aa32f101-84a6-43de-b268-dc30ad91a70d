import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:darvis_app/services/navigation_service.dart';
import 'package:darvis_app/widgets/enhanced_bottom_nav_bar.dart';

/// A wrapper for calendar sub-screens that maintains proper navigation back to the main calendar screen
class CalendarScreenBottomNavBar extends StatelessWidget {
  const CalendarScreenBottomNavBar({super.key});
  
  @override
  Widget build(BuildContext context) {
    return EnhancedBottomNavBar(
      selectedIndex: 1, // Calendar tab
      onTabSelected: (index) {
        if (index == NavigationService.calendarTab) {
          // Navigate back to main calendar screen
          GetIt.instance<NavigationService>().navigateToTab(NavigationService.calendarTab);
        } else {
          // Handle other tab navigation normally
          GetIt.instance<NavigationService>().navigateToTab(index);
        }
      },
    );
  }
}
