import 'package:equatable/equatable.dart';
import '../../models/profile_models.dart';

/// Base profile state
abstract class ProfileState extends Equatable {
  const ProfileState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class ProfileInitial extends ProfileState {
  const ProfileInitial();
}

/// Loading state
class ProfileLoading extends ProfileState {
  const ProfileLoading();
}

/// Profile loaded successfully
class ProfileLoaded extends ProfileState {
  final UserProfile profile;
  final String? profilePictureUrl;
  final String? thumbnailUrl;
  final Map<String, String>? responsiveUrls;

  const ProfileLoaded({
    required this.profile,
    this.profilePictureUrl,
    this.thumbnailUrl,
    this.responsiveUrls,
  });

  @override
  List<Object?> get props => [profile, profilePictureUrl, thumbnailUrl, responsiveUrls];

  ProfileLoaded copyWith({
    UserProfile? profile,
    String? profilePictureUrl,
    String? thumbnailUrl,
    Map<String, String>? responsiveUrls,
  }) {
    return ProfileLoaded(
      profile: profile ?? this.profile,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      responsiveUrls: responsiveUrls ?? this.responsiveUrls,
    );
  }
}

/// Profile updated successfully
class ProfileUpdated extends ProfileState {
  final UserProfile profile;
  final String message;

  const ProfileUpdated({
    required this.profile,
    required this.message,
  });

  @override
  List<Object> get props => [profile, message];
}

/// Profile picture changing state
class ProfilePictureChanging extends ProfileState {
  final ProfilePictureUploadState uploadState;
  final double? progress;

  const ProfilePictureChanging({
    required this.uploadState,
    this.progress,
  });

  @override
  List<Object?> get props => [uploadState, progress];
}

/// Profile picture changed successfully
class ProfilePictureChanged extends ProfileState {
  final String newPictureUrl;
  final String? thumbnailUrl;
  final Map<String, String>? responsiveUrls;

  const ProfilePictureChanged({
    required this.newPictureUrl,
    this.thumbnailUrl,
    this.responsiveUrls,
  });

  @override
  List<Object?> get props => [newPictureUrl, thumbnailUrl, responsiveUrls];
}

/// Profile picture deleted
class ProfilePictureDeleted extends ProfileState {
  const ProfilePictureDeleted();
}

/// Profile picture history loaded
class ProfilePictureHistoryLoaded extends ProfileState {
  final List<ProfilePictureHistory> history;

  const ProfilePictureHistoryLoaded(this.history);

  @override
  List<Object> get props => [history];
}

/// Profile synced with backend
class ProfileSynced extends ProfileState {
  final DateTime syncTime;

  const ProfileSynced(this.syncTime);

  @override
  List<Object> get props => [syncTime];
}

/// Profile data exported
class ProfileDataExported extends ProfileState {
  final String filePath;
  final String format;

  const ProfileDataExported({
    required this.filePath,
    required this.format,
  });

  @override
  List<Object> get props => [filePath, format];
}

/// Profile data imported
class ProfileDataImported extends ProfileState {
  final UserProfile profile;
  final int importedItemsCount;

  const ProfileDataImported({
    required this.profile,
    required this.importedItemsCount,
  });

  @override
  List<Object> get props => [profile, importedItemsCount];
}

/// Profile backup created
class ProfileBackupCreated extends ProfileState {
  final String backupId;
  final DateTime backupTime;

  const ProfileBackupCreated({
    required this.backupId,
    required this.backupTime,
  });

  @override
  List<Object> get props => [backupId, backupTime];
}

/// Profile restored from backup
class ProfileRestored extends ProfileState {
  final UserProfile profile;
  final String backupId;

  const ProfileRestored({
    required this.profile,
    required this.backupId,
  });

  @override
  List<Object> get props => [profile, backupId];
}

/// User account deleted
class UserAccountDeleted extends ProfileState {
  const UserAccountDeleted();
}

/// Error state
class ProfileError extends ProfileState {
  final String message;
  final String? errorCode;

  const ProfileError(this.message, {this.errorCode});

  @override
  List<Object?> get props => [message, errorCode];
}

/// Specific error states
class ProfilePictureUploadError extends ProfileError {
  const ProfilePictureUploadError(String message) 
      : super('Failed to upload profile picture: $message');
}

class ProfileSyncError extends ProfileError {
  const ProfileSyncError(String message) 
      : super('Failed to sync profile: $message');
}

class ProfileUpdateError extends ProfileError {
  const ProfileUpdateError(String message) 
      : super('Failed to update profile: $message');
}

class ProfilePictureDeleteError extends ProfileError {
  const ProfilePictureDeleteError(String message) 
      : super('Failed to delete profile picture: $message');
}

class ProfileExportError extends ProfileError {
  const ProfileExportError(String message) 
      : super('Failed to export profile data: $message');
}

class ProfileImportError extends ProfileError {
  const ProfileImportError(String message) 
      : super('Failed to import profile data: $message');
}

class ProfileBackupError extends ProfileError {
  const ProfileBackupError(String message) 
      : super('Failed to backup profile: $message');
}

class ProfileRestoreError extends ProfileError {
  const ProfileRestoreError(String message) 
      : super('Failed to restore profile: $message');
}

class AccountDeletionError extends ProfileError {
  const AccountDeletionError(String message) 
      : super('Failed to delete account: $message');
}

/// Loading states for specific operations
class ProfileUpdating extends ProfileState {
  final String field;

  const ProfileUpdating(this.field);

  @override
  List<Object> get props => [field];
}

class ProfileSyncing extends ProfileState {
  const ProfileSyncing();
}

class ProfileExporting extends ProfileState {
  final String format;

  const ProfileExporting(this.format);

  @override
  List<Object> get props => [format];
}

class ProfileImporting extends ProfileState {
  const ProfileImporting();
}

class ProfileBackingUp extends ProfileState {
  const ProfileBackingUp();
}

class ProfileRestoring extends ProfileState {
  final String backupId;

  const ProfileRestoring(this.backupId);

  @override
  List<Object> get props => [backupId];
}

class AccountDeleting extends ProfileState {
  const AccountDeleting();
}

/// Success states with messages
class ProfileOperationSuccess extends ProfileState {
  final String message;
  final String? actionType;

  const ProfileOperationSuccess(this.message, {this.actionType});

  @override
  List<Object?> get props => [message, actionType];
}

/// Validation states
class ProfileValidationError extends ProfileError {
  final Map<String, String> fieldErrors;

  const ProfileValidationError(String message, this.fieldErrors) 
      : super(message);

  @override
  List<Object> get props => [message, fieldErrors];
}

/// Settings update states
class ProfileSettingUpdated extends ProfileState {
  final String settingName;
  final dynamic newValue;

  const ProfileSettingUpdated({
    required this.settingName,
    required this.newValue,
  });

  @override
  List<Object> get props => [settingName, newValue];
}

/// Subscription states
class SubscriptionUpdated extends ProfileState {
  final String plan;
  final DateTime? expiryDate;
  final bool isActive;

  const SubscriptionUpdated({
    required this.plan,
    this.expiryDate,
    required this.isActive,
  });

  @override
  List<Object?> get props => [plan, expiryDate, isActive];
}

/// Streak update state
class StreakUpdated extends ProfileState {
  final int newStreak;
  final bool isNewRecord;

  const StreakUpdated({
    required this.newStreak,
    required this.isNewRecord,
  });

  @override
  List<Object> get props => [newStreak, isNewRecord];
}
