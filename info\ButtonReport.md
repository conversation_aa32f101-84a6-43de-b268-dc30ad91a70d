# Button Landscape Audit Report

## Executive Summary:
- **Total interactive elements found:** 108
- **Working elements:** 15 (14%)
- **Placeholder elements:** 85 (79%)
- **Broken elements:** 8 (7%)

The codebase is rich with UI components, but the vast majority of interactive elements are placeholders (`print` statements or empty callbacks). Core functionality, especially authentication and data manipulation (CRUD), is not yet connected. The immediate priority is to wire up the authentication flow, followed by implementing the BLoC events for the primary features like Notes, Tasks, and Smart Capture.

## By Feature Area:
### Authentication: 10 total buttons
- **Working:** 0
- **Placeholders:** 10
- **Broken:** 0

### Productivity (Notes/Tasks): 28 total buttons
- **Working:** 4
- **Placeholders:** 24
- **Broken:** 0

### Therapy Features: 11 total buttons
- **Working:** 3
- **Placeholders:** 8
- **Broken:** 0

### Smart Capture: 15 total buttons
- **Working:** 2
- **Placeholders:** 13
- **Broken:** 0

### Profile/Settings: 31 total buttons
- **Working:** 6 (Navigation Only)
- **Placeholders:** 17
- **Broken:** 8

### Common Widgets: 13 total buttons
- **Working:** 0
- **Placeholders:** 13
- **Broken:** 0

## Critical Issues Found:
- **Complete Auth Disconnect:** No auth buttons are functional. Users cannot sign up, log in, or log out. This is the most critical blocker.
- **No Data Persistence:** No "Save", "Create", or "Delete" buttons for Notes, Tasks, or Events are functional. All user-generated content is lost.
- **Broken Settings:** Multiple toggles and buttons in the Profile & Settings screens are marked as `onChanged: null`, making them visually disabled and non-functional.

## Recommended Implementation Order:
1.  **🔥 Critical: Authentication Flow**
    - Connect `SignUpScreen` and `LoginScreen` buttons to `AuthBloc`.
    - Implement `Logout` functionality in `ProfileScreen`.
2.  **⚡ High: Core Productivity Features (CRUD)**
    - Implement "Add Note" and "Add Task" flows from the `EnhancedBottomNavBar` and respective screens.
    - Connect "Save" and "Delete" actions in `NoteViewScreen` and `TaskManagementScreen`.
3.  **⚡ High: Smart Capture**
    - Wire up the "Add to Notes" and "Chat with Darvis" buttons on `ContentCard`.
    - Implement content filtering and sorting controls.
4.  **📱 Medium: Profile & Settings**
    - Fix all `onChanged: null` toggles.
    - Connect settings to a persistence layer (e.g., `shared_preferences` or a settings BLoC).
5.  **🎨 Low: Therapy & UI Enhancements**
    - Connect therapy mode buttons to a `TherapyBloc`.
    - Implement navigation for secondary features like "Forgot Password".

---

## Detailed Button Map

### Screen/Feature Name: Authentication
**File Location:** `lib/screens/auth/login_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| GestureDetector | "Gmail" | `print('Gmail Login Tapped')` | 101 | Needs `AuthBloc.add(SignInWithGoogleRequested())` | 🔥 Critical | 🔄 Placeholder |
| IconButton | Password visibility | `setState(...)` | 170 | Working as intended for local UI state | 🎨 Low | ✅ Working |
| GestureDetector | "Forgot Password?" | `print('Forgot Password Tapped')` | 178 | Needs navigation to a password reset screen | 📱 Medium | 🔄 Placeholder |
| GestureDetector | "Sign In" | `print('Sign In Tapped')` | 184 | Needs `AuthBloc.add(SignInRequested(email, pass))` | 🔥 Critical | 🔄 Placeholder |

**File Location:** `lib/screens/auth/signup_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| GestureDetector | "Google" | `print('Google Sign Up Tapped')` | 80 | Needs `AuthBloc.add(SignUpWithGoogleRequested())` | 🔥 Critical | 🔄 Placeholder |
| IconButton | Password visibility | `setState(...)` | 180 | Working as intended for local UI state | 🎨 Low | ✅ Working |
| IconButton | Confirm Pass visibility | `setState(...)` | 194 | Working as intended for local UI state | 🎨 Low | ✅ Working |
| GestureDetector | "Log In." link | `print('Log In link tapped')` | 205 | Needs `Navigator.push` to LoginScreen | 🔥 Critical | 🔄 Placeholder |
| GestureDetector | "Sign Up" | `print('Sign Up button tapped')` | 211 | Needs `AuthBloc.add(SignUpRequested(email, pass, ...))` | 🔥 Critical | 🔄 Placeholder |

**File Location:** `lib/screens/auth/welcome_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| Timer | Auto-navigation | `Navigator.of(context).pushReplacement(...)` | 60 | Navigates to HomeScreen after 10s | 🎨 Low | ✅ Working |

---

### Screen/Feature Name: Productivity
**File Location:** `lib/screens/productivity/notes_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| IconButton | Back arrow | `Navigator.of(context).pop()` | 250 | Navigation working | 📱 Medium | ✅ Working |
| TextField | Search input | `_onSearchChanged()` | 273 | Filters local mock data. Needs BLoC event. | ⚡ High | 🔄 Placeholder |
| GestureDetector | Tag filter item | `_onTagFilterChanged(tag)` | 335 | Filters local mock data. Needs BLoC event. | ⚡ High | 🔄 Placeholder |
| GestureDetector | Note item | `_onNoteTapped(note)` | 401 | Navigates to `NoteViewScreen` | ⚡ High | ✅ Working |
| GestureDetector | Note item (long press) | `_onNoteLongPress(note)` | 402 | Enters selection mode. Needs BLoC state update. | ⚡ High | 🔄 Placeholder |
| IconButton | Cancel selection | `_clearSelection()` | 431 | Clears local selection. Needs BLoC event. | ⚡ High | 🔄 Placeholder |
| IconButton | Pin selected | `_pinSelectedNotes()` | 442 | Prints to console. Needs `NotesBloc.add(PinNotes(...))` | ⚡ High | 🔄 Placeholder |
| IconButton | Delete selected | `_deleteSelectedNotes()` | 452 | Prints to console. Needs `NotesBloc.add(DeleteNotes(...))` | ⚡ High | 🔄 Placeholder |
| FloatingActionButton | Add Note | `_createNewNote()` | 471 | Navigates to `NoteViewScreen` with a new note object | ⚡ High | ✅ Working |

**File Location:** `lib/screens/productivity/note_view_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| IconButton | Back arrow | `_onBackPressed()` | 150 | Shows discard dialog. Needs `NotesBloc.add(UpdateNote(...))` on save. | ⚡ High | 🔄 Placeholder |
| IconButton | Pin | `setState(...)` | 164 | Toggles local state. Needs BLoC event. | ⚡ High | 🔄 Placeholder |
| IconButton | Delete | `_deleteNote()` | 174 | Shows delete dialog. Needs `NotesBloc.add(DeleteNote(...))`. | ⚡ High | 🔄 Placeholder |
| IconButton | More options | `_showMoreOptions()` | 184 | Shows menu. Actions are placeholders. | ⚡ High | 🔄 Placeholder |
| TextField | Title | `_onContentChanged()` | 220 | Updates local state. Needs to be debounced and sent to BLoC. | ⚡ High | 🔄 Placeholder |
| TextField | Content | `_onContentChanged()` | 233 | Updates local state. Needs to be debounced and sent to BLoC. | ⚡ High | 🔄 Placeholder |

**File Location:** `lib/screens/productivity/task_management_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| IconButton | Back arrow | `Navigator.of(context).pop()` | 139 | Navigation working | 📱 Medium | ✅ Working |
| GestureDetector | Category item | `_onCategorySelected(category)` | 202 | Filters local mock data. Needs `TasksBloc` event. | ⚡ High | 🔄 Placeholder |
| Checkbox | Task completion | `_onTaskCompleted(task, completed)` | 251 | Updates local mock data. Needs `TasksBloc.add(UpdateTask(...))`. | ⚡ High | 🔄 Placeholder |
| FloatingActionButton | Add Task | `_showAddTaskModal()` | 270 | Opens modal. Needs `TasksBloc.add(AddTask(...))` on save. | ⚡ High | 🔄 Placeholder |

**File Location:** `lib/screens/productivity/productivity_analytics_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| IconButton | Back arrow | `Navigator.of(context).pop()` | 153 | Navigation working | 📱 Medium | ✅ Working |
| DropdownButton | Time period | `_onTimePeriodChanged(value)` | 208 | Updates local mock data. Needs BLoC event to fetch new data. | 📱 Medium | 🔄 Placeholder |
| RefreshIndicator | Pull to refresh | `_refreshData()` | 144 | Simulates delay. Needs to trigger BLoC event to refetch data. | 📱 Medium | 🔄 Placeholder |

---

### Screen/Feature Name: Therapy Features
**File Location:** `lib/screens/therapy/therapy_voice_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| GestureDetector | Mic button | `_handleMicTap()` | 86 | Toggles local UI state. Needs to connect to a voice service/BLoC. | 📱 Medium | 🔄 Placeholder |
| GestureDetector | Screen tap | `_handleScreenTap()` | 98 | Toggles local UI state. | 🎨 Low | 🔄 Placeholder |
| IconButton | Close | `_handleCloseTap()` | 107 | Prints to console. Needs `Navigator.pop()`. | 📱 Medium | 🔄 Placeholder |
| IconButton | Chat | `_handleChatTap()` | 112 | Prints to console. Needs navigation to ChatScreen. | 📱 Medium | 🔄 Placeholder |

**File Location:** `lib/screens/therapy/therapy_progress_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| TimeRangeButton | Time range filter | `_onTimeRangeChanged(newRange)` | 210 | Updates local mock data. Needs BLoC event. | 📱 Medium | 🔄 Placeholder |
| GestureDetector | Emotion Bubble | `_onEmotionBubbleTap(emotion)` | 220 | Shows a SnackBar with mock data. | 🎨 Low | ✅ Working |
| TextButton | "Read Full Summary" | `_showExpandedSummary()` | 398 | Shows a modal with mock data. | 🎨 Low | ✅ Working |

**File Location:** `lib/screens/therapy/mind_garden_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| IconButton | Back arrow | `Navigator.of(context).pop()` | 218 | Navigation working | 📱 Medium | ✅ Working |
| GestureDetector | Lottie Animation | `_onAnimationTap()` | 238 | Restarts local animation. | 🎨 Low | 🔄 Placeholder |
| GestureDetector | Mood Emoji | `_selectMood(index)` | 273 | Updates local state and shows SnackBar. Needs to persist mood. | 📱 Medium | 🔄 Placeholder |
| IconButton | Refresh Quote | `_refreshQuote()` | 310 | Fetches new mock quote. Needs to call a service/BLoC. | 🎨 Low | 🔄 Placeholder |

---

### Screen/Feature Name: Smart Capture
**File Location:** `lib/screens/smart_capture/smart_capture_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| TextField | Search input | `_onSearchChanged()` | 100 | Filters local mock data. Needs BLoC event. | ⚡ High | 🔄 Placeholder |
| TextField | Paste/Input field | `_processInput(_inputController.text)` | 107 | Uses mock service. Needs `SmartCaptureBloc`. | ⚡ High | 🔄 Placeholder |
| PopupMenuButton | Filter options | `_onFilterChanged(value)` | 258 | Filters local mock data. Needs BLoC event. | ⚡ High | 🔄 Placeholder |
| PopupMenuButton | Sort options | `_onSortChanged(value)` | 281 | Sorts local mock data. Needs BLoC event. | ⚡ High | 🔄 Placeholder |
| IconButton | Toggle search | `setState(...)` | 304 | Toggles local UI state. | 🎨 Low | ✅ Working |
| IconButton | Bulk select | `_toggleBulkSelectionMode()` | 314 | Toggles local UI state. Needs BLoC state update. | ⚡ High | 🔄 Placeholder |
| ContentCard | Tap card | `_showContentDetail(content)` | 358 | Opens detail modal. | ⚡ High | ✅ Working |
| ContentCard | Long press card | `_onItemLongPress(content)` | 359 | Enters selection mode. Needs BLoC state update. | ⚡ High | 🔄 Placeholder |
| IconButton | Delete selected | `_deleteSelectedItems()` | 400 | Prints to console. Needs `SmartCaptureBloc.add(DeleteItems(...))`. | ⚡ High | 🔄 Placeholder |

**File Location:** `lib/screens/smart_capture/widgets/content_card.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| IconButton | "Add to Notes" | `onAddToNotes()` (passed in) | 250 | Callback is empty in `smart_capture_screen.dart`. Needs `NotesBloc` event. | ⚡ High | 🔄 Placeholder |
| IconButton | "Chat with Darvis" | `onChatWithDarvis()` (passed in) | 263 | Callback is empty. Needs navigation to ChatScreen with context. | ⚡ High | 🔄 Placeholder |

---

### Screen/Feature Name: Profile & Settings
**File Location:** `lib/screens/profile/profile_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| GestureDetector | Profile Photo | `_changeProfilePhoto()` | 200 | Prints to console. Needs ImagePicker and `UserBloc`. | 📱 Medium | 🔄 Placeholder |
| SettingsListItem | Account Settings | `Navigator.push(...)` | 260 | Navigation working | 📱 Medium | ✅ Working |
| SettingsListItem | Privacy & Security | `Navigator.push(...)` | 266 | Navigation working | 📱 Medium | ✅ Working |
| SettingsListItem | Notifications | `Navigator.push(...)` | 272 | Navigation working | 📱 Medium | ✅ Working |
| SettingsListItem | Data & Storage | `Navigator.push(...)` | 278 | Navigation working | 📱 Medium | ✅ Working |
| SettingsListItem | About Darvis | `Navigator.push(...)` | 288 | Navigation working | 📱 Medium | ✅ Working |
| SettingsListItem | Contact Us | `Navigator.push(...)` | 294 | Navigation working | 📱 Medium | ✅ Working |
| TextButton | Logout | `_logout()` | 305 | Prints to console. Needs `AuthBloc.add(LogoutRequested())`. | 🔥 Critical | 🔄 Placeholder |

**File Location:** `lib/screens/profile/account_settings_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| Switch | Face ID / Biometrics | `onChanged: null` | 180 | Broken. Needs implementation. | 📱 Medium | ❌ Broken |
| TextButton | Change Password | `_showComingSoon(...)` | 200 | Placeholder action. | 📱 Medium | 🔄 Placeholder |
| TextButton | Manage Subscription | `_showComingSoon(...)` | 220 | Placeholder action. | 📱 Medium | 🔄 Placeholder |
| TextButton | Deactivate Account | `_showComingSoon(...)` | 240 | Placeholder action. | 📱 Medium | 🔄 Placeholder |

**File Location:** `lib/screens/profile/privacy_security_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| Switch | Conversational Memory | `onChanged: null` | 140 | Broken. Needs implementation. | 📱 Medium | ❌ Broken |
| TextButton | Clear Conversation History | `_showComingSoon(...)` | 160 | Placeholder action. | 📱 Medium | 🔄 Placeholder |
| TextButton | Manage Blocked Contacts | `_showComingSoon(...)` | 180 | Placeholder action. | 📱 Medium | 🔄 Placeholder |

**File Location:** `lib/screens/profile/notifications_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| Switch | All Notifications | `onChanged: null` | 140 | Broken. Needs implementation. | 📱 Medium | ❌ Broken |
| Switch | Task Reminders | `onChanged: null` | 160 | Broken. Needs implementation. | 📱 Medium | ❌ Broken |
| Switch | Event Alerts | `onChanged: null` | 180 | Broken. Needs implementation. | 📱 Medium | ❌ Broken |
| Switch | Smart Capture Updates | `onChanged: null` | 200 | Broken. Needs implementation. | 📱 Medium | ❌ Broken |

**File Location:** `lib/screens/profile/data_storage_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| Switch | Master Sync | `onChanged: null` | 230 | Broken. Needs implementation. | 📱 Medium | ❌ Broken |
| DropdownButton | Sync Frequency | `onChanged: null` | 245 | Broken. Needs implementation. | 📱 Medium | ❌ Broken |
| TextButton | Manual Backup | `_showComingSoon(...)` | 340 | Placeholder action. | 📱 Medium | 🔄 Placeholder |
| TextButton | Export Data | `_showComingSoon(...)` | 350 | Placeholder action. | 📱 Medium | 🔄 Placeholder |

**File Location:** `lib/screens/profile/contact_us_screen.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| ElevatedButton | Send Message | `_sendMessage()` | 230 | Shows a SnackBar. Does not actually send data. | 📱 Medium | 🔄 Placeholder |
| InkWell | Email Support | `_showComingSoon(...)` | 300 | Placeholder action. | 📱 Medium | 🔄 Placeholder |
| InkWell | Live Chat | `_showComingSoon(...)` | 306 | Placeholder action. | 📱 Medium | 🔄 Placeholder |
| InkWell | Community Forum | `_showComingSoon(...)` | 312 | Placeholder action. | 📱 Medium | 🔄 Placeholder |
| InkWell | Documentation | `_showComingSoon(...)` | 318 | Placeholder action. | 📱 Medium | 🔄 Placeholder |

---

### Screen/Feature Name: Common Widgets
**File Location:** `lib/widgets/enhanced_bottom_nav_bar.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| InkWell | Nav Item (Home, etc.) | `onItemSelected(index)` | 160 | Callback is empty in most screens. Needs navigation logic. | 🔥 Critical | 🔄 Placeholder |
| GestureDetector | Central Darvis Button | `onItemSelected(index)` | 180 | Callback is empty. Needs navigation logic. | 🔥 Critical | 🔄 Placeholder |
| GestureDetector | Long Press Darvis Button | `_showQuickActionOverlay()` | 181 | Shows overlay. Actions on overlay are placeholders. | ⚡ High | 🔄 Placeholder |

**File Location:** `lib/widgets/quick_action_overlay.dart`

| Element Type | Description/Text | Current Action | Line # | Notes | Priority | Status |
|--------------|------------------|----------------|--------|-------|----------|--------|
| GestureDetector | "Add a Note" | `onAddNote()` (passed in) | 200 | Callback is a placeholder in `enhanced_bottom_nav_bar.dart`. | ⚡ High | 🔄 Placeholder |
| GestureDetector | "Add a Task" | `onAddTask()` (passed in) | 205 | Callback is a placeholder in `enhanced_bottom_nav_bar.dart`. | ⚡ High | 🔄 Placeholder |
| GestureDetector | Overlay Dismiss | `_dismissOverlay()` | 160 | Dismisses the overlay. | 🎨 Low | ✅ Working |
