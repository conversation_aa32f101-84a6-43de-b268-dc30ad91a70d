import 'package:equatable/equatable.dart';

/// Base class for all calendar events
abstract class Calendar<PERSON>vent extends Equatable {
  const CalendarEvent();

  @override
  List<Object?> get props => [];
}

/// Load calendar events for a specific month
class LoadCalendarEvents extends CalendarEvent {
  final DateTime month;

  const LoadCalendarEvents(this.month);

  @override
  List<Object?> get props => [month];
}

/// Refresh calendar events from server
class RefreshCalendarEvents extends CalendarEvent {
  const RefreshCalendarEvents();
}

/// Add a new calendar event
class AddCalendarEvent extends CalendarEvent {
  final String title;
  final String description;
  final DateTime startTime;
  final DateTime endTime;
  final String type;
  final bool isAllDay;
  final String? location;
  final List<String> attendees;

  const AddCalendarEvent({
    required this.title,
    required this.description,
    required this.startTime,
    required this.endTime,
    this.type = 'general',
    this.isAllDay = false,
    this.location,
    this.attendees = const [],
  });

  @override
  List<Object?> get props => [
        title,
        description,
        startTime,
        endTime,
        type,
        isAllDay,
        location,
        attendees,
      ];
}

/// Update an existing calendar event
class UpdateCalendarEvent extends CalendarEvent {
  final String eventId;
  final String? title;
  final String? description;
  final DateTime? startTime;
  final DateTime? endTime;
  final String? type;
  final bool? isAllDay;
  final String? location;
  final List<String>? attendees;

  const UpdateCalendarEvent({
    required this.eventId,
    this.title,
    this.description,
    this.startTime,
    this.endTime,
    this.type,
    this.isAllDay,
    this.location,
    this.attendees,
  });

  @override
  List<Object?> get props => [
        eventId,
        title,
        description,
        startTime,
        endTime,
        type,
        isAllDay,
        location,
        attendees,
      ];
}

/// Delete a calendar event
class DeleteCalendarEvent extends CalendarEvent {
  final String eventId;

  const DeleteCalendarEvent(this.eventId);

  @override
  List<Object?> get props => [eventId];
}

/// Select a specific date
class SelectCalendarDate extends CalendarEvent {
  final DateTime date;

  const SelectCalendarDate(this.date);

  @override
  List<Object?> get props => [date];
}

/// Change calendar view mode
class ChangeCalendarView extends CalendarEvent {
  final CalendarViewMode viewMode;

  const ChangeCalendarView(this.viewMode);

  @override
  List<Object?> get props => [viewMode];
}

/// Navigate to previous period
class NavigateToPreviousPeriod extends CalendarEvent {
  const NavigateToPreviousPeriod();
}

/// Navigate to next period
class NavigateToNextPeriod extends CalendarEvent {
  const NavigateToNextPeriod();
}

/// Navigate to today
class NavigateToToday extends CalendarEvent {
  const NavigateToToday();
}

/// Search calendar events
class SearchCalendarEvents extends CalendarEvent {
  final String query;

  const SearchCalendarEvents(this.query);

  @override
  List<Object?> get props => [query];
}

/// Filter events by type
class FilterEventsByType extends CalendarEvent {
  final String? eventType;

  const FilterEventsByType(this.eventType);

  @override
  List<Object?> get props => [eventType];
}

/// Clear calendar filters
class ClearCalendarFilters extends CalendarEvent {
  const ClearCalendarFilters();
}

/// Sync calendar events with server
class SyncCalendarEvents extends CalendarEvent {
  const SyncCalendarEvents();
}

/// Export calendar events
class ExportCalendarEvents extends CalendarEvent {
  final DateTime? startDate;
  final DateTime? endDate;
  final String format; // 'ics', 'json', 'csv'

  const ExportCalendarEvents({
    this.startDate,
    this.endDate,
    this.format = 'ics',
  });

  @override
  List<Object?> get props => [startDate, endDate, format];
}

/// Calendar view modes
enum CalendarViewMode {
  month,
  week,
  day,
  agenda,
}

/// Event types
enum EventType {
  general,
  meeting,
  appointment,
  reminder,
  birthday,
  holiday,
  task,
}
