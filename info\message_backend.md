# Backend Implementation Requirements for Drix App

This document outlines the backend functionality required to support the frontend features implemented in the Drix app.

## Phase 1 Implementation Summary

### 1. Home Screen Features ✅ IMPLEMENTED

#### Dynamic Greeting System
**Frontend Implementation:**
- Created `GreetingService` with intelligent greeting logic
- Time-based greetings for first session (Good morning/afternoon/evening)
- Casual greetings for subsequent sessions ("What's up?", "Welcome back!")
- Session tracking with secure local storage
- Integrated with `DashboardBloc` and `HomeScreen`

**Backend Requirements:**
```http
POST /user/session
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "timestamp": "2024-01-15T10:30:00Z",
  "session_type": "app_open"
}

Response:
{
  "success": true,
  "session_id": "session_12345",
  "daily_count": 3
}
```

```http
GET /user/session-data
Authorization: Bearer {access_token}

Response:
{
  "daily_sessions": 2,
  "last_session": "2024-01-15T07:30:00Z",
  "first_session_today": "2024-01-15T06:00:00Z",
  "total_sessions": 45
}
```

#### Expandable Quick Look Card
**Frontend Implementation:**
- Enhanced "Today's Plate" card with "Tap for More" functionality
- Modal bottom sheet with 80% screen height
- Scrollable content with organized sections (tasks, events, notes)
- "View Full Calendar" navigation button
- Glassmorphic design following app standards

**Backend Requirements:**
- Enhanced dashboard endpoint to include more detailed daily data
- Existing `/dashboard` endpoint should be expanded to include:
  - Detailed task breakdown
  - Upcoming events with time information
  - Recent notes summary

### 2. Notification Engine ✅ IMPLEMENTED

#### Comprehensive Notification System
**Frontend Implementation:**
- Created `NotificationService` with local and remote notification handling
- Implemented `NotificationBloc` for state management
- Support for multiple notification types:
  - Therapy reminders
  - Task notifications
  - Daily check-ins
  - Weekly reports
  - System updates
  - Contextual AI suggestions
- Quiet hours functionality
- Notification frequency controls
- Local storage with Isar database
- Analytics tracking for notification interactions

**Backend Requirements:**

##### Notification Management
```http
POST /notifications
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "title": "Therapy Session Reminder",
  "body": "Your therapy session is starting soon",
  "type": "therapy_reminder",
  "priority": "high",
  "scheduled_time": "2024-01-15T14:45:00Z",
  "navigation_route": "/therapy",
  "navigation_data": {
    "session_id": "session_123"
  }
}

Response:
{
  "success": true,
  "notification_id": "notif_456",
  "scheduled_time": "2024-01-15T14:45:00Z"
}
```

##### Notification Settings
```http
PUT /notifications/settings
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "therapy_reminders": true,
  "task_notifications": true,
  "daily_check_ins": false,
  "weekly_reports": true,
  "system_updates": false,
  "contextual_ai": true,
  "quiet_hours_enabled": true,
  "quiet_hours_start_hour": 22,
  "quiet_hours_start_minute": 0,
  "quiet_hours_end_hour": 8,
  "quiet_hours_end_minute": 0,
  "notification_frequency": "Normal",
  "sound_enabled": true,
  "vibration_enabled": true,
  "badge_count_enabled": true
}

Response:
{
  "success": true,
  "updated_at": "2024-01-15T10:30:00Z"
}
```

##### Notification Interaction Tracking
```http
POST /notifications/{notification_id}/interact
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "interaction_recorded": true
}
```

##### Get User Notifications
```http
GET /notifications?page=1&limit=20
Authorization: Bearer {access_token}

Response:
{
  "notifications": [
    {
      "id": "notif_456",
      "title": "Therapy Session Reminder",
      "body": "Your therapy session is starting soon",
      "type": "therapy_reminder",
      "priority": "high",
      "scheduled_time": "2024-01-15T14:45:00Z",
      "is_read": false,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 15,
  "page": 1,
  "limit": 20
}
```

### 3. Profile & Settings with Cloudinary Integration ✅ IMPLEMENTED

#### Profile Management System
**Frontend Implementation:**
- Created `ProfileService` with Cloudinary integration for profile pictures
- Implemented `CloudinaryService` for secure image upload/management
- Created `ProfileBloc` for comprehensive profile state management
- Profile picture sync between home and profile screens
- Comprehensive settings management (theme, language, security, etc.)
- Profile picture history tracking
- Responsive image URLs for different screen densities

**Backend Requirements:**

##### User Profile Management
```http
PUT /user/profile
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "user_id": "firebase_user_123",
  "display_name": "John Doe",
  "email": "<EMAIL>",
  "darvis_name": "John",
  "profile_picture_url": "https://res.cloudinary.com/drix/image/upload/v123/profile.jpg",
  "profile_picture_public_id": "drix/profiles/user_123_456789",
  "time_zone": "America/New_York",
  "language": "en",
  "theme": "dark",
  "biometric_auth_enabled": true,
  "session_timeout": "30 minutes",
  "app_lock_required": false,
  "cloud_sync_enabled": true,
  "auto_backup_enabled": true
}

Response:
{
  "success": true,
  "profile_id": "profile_789",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

##### Profile Picture Management
```http
POST /user/profile-picture
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "user_id": "firebase_user_123",
  "cloudinary_url": "https://res.cloudinary.com/drix/image/upload/v123/new_profile.jpg",
  "cloudinary_public_id": "drix/profiles/user_123_789012",
  "previous_public_id": "drix/profiles/user_123_456789"
}

Response:
{
  "success": true,
  "profile_picture_updated": true,
  "previous_image_deleted": true
}
```

##### Get User Profile
```http
GET /user/profile
Authorization: Bearer {access_token}

Response:
{
  "user_id": "firebase_user_123",
  "display_name": "John Doe",
  "email": "<EMAIL>",
  "darvis_name": "John",
  "profile_picture_url": "https://res.cloudinary.com/drix/image/upload/v123/profile.jpg",
  "profile_picture_public_id": "drix/profiles/user_123_456789",
  "member_since": "2024-01-01T00:00:00Z",
  "current_streak": 15,
  "last_activity_date": "2024-01-15T09:00:00Z",
  "subscription_plan": "Essential",
  "subscription_expiry_date": null,
  "is_subscription_active": false,
  "time_zone": "America/New_York",
  "language": "en",
  "theme": "dark",
  "biometric_auth_enabled": true,
  "session_timeout": "30 minutes",
  "app_lock_required": false,
  "cloud_sync_enabled": true,
  "auto_backup_enabled": true,
  "last_backup_date": "2024-01-14T12:00:00Z"
}
```

##### Delete Profile Picture
```http
DELETE /user/profile-picture
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "profile_picture_deleted": true,
  "cloudinary_image_deleted": true
}
```

## Database Schema Requirements

### User Profiles Table
```sql
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY,
  user_id VARCHAR(255) UNIQUE NOT NULL, -- Firebase UID
  display_name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  darvis_name VARCHAR(255),
  profile_picture_url TEXT,
  profile_picture_public_id VARCHAR(255),
  member_since TIMESTAMP,
  current_streak INTEGER DEFAULT 0,
  last_activity_date TIMESTAMP,
  subscription_plan VARCHAR(50) DEFAULT 'Essential',
  subscription_expiry_date TIMESTAMP,
  is_subscription_active BOOLEAN DEFAULT FALSE,
  time_zone VARCHAR(100) DEFAULT 'UTC',
  language VARCHAR(10) DEFAULT 'en',
  theme VARCHAR(20) DEFAULT 'system',
  biometric_auth_enabled BOOLEAN DEFAULT FALSE,
  session_timeout VARCHAR(50) DEFAULT '30 minutes',
  app_lock_required BOOLEAN DEFAULT FALSE,
  cloud_sync_enabled BOOLEAN DEFAULT TRUE,
  auto_backup_enabled BOOLEAN DEFAULT TRUE,
  last_backup_date TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### User Sessions Table
```sql
CREATE TABLE user_sessions (
  id UUID PRIMARY KEY,
  user_id VARCHAR(255) NOT NULL,
  session_type VARCHAR(50) NOT NULL,
  timestamp TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES user_profiles(user_id)
);
```

### Notifications Table
```sql
CREATE TABLE notifications (
  id UUID PRIMARY KEY,
  user_id VARCHAR(255) NOT NULL,
  title VARCHAR(255) NOT NULL,
  body TEXT NOT NULL,
  type VARCHAR(50) NOT NULL,
  priority VARCHAR(20) NOT NULL,
  scheduled_time TIMESTAMP NOT NULL,
  navigation_route VARCHAR(255),
  navigation_data JSONB,
  is_delivered BOOLEAN DEFAULT FALSE,
  is_read BOOLEAN DEFAULT FALSE,
  is_interacted BOOLEAN DEFAULT FALSE,
  delivered_at TIMESTAMP,
  read_at TIMESTAMP,
  interacted_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES user_profiles(user_id)
);
```

### Notification Settings Table
```sql
CREATE TABLE notification_settings (
  id UUID PRIMARY KEY,
  user_id VARCHAR(255) UNIQUE NOT NULL,
  therapy_reminders BOOLEAN DEFAULT TRUE,
  task_notifications BOOLEAN DEFAULT TRUE,
  daily_check_ins BOOLEAN DEFAULT FALSE,
  weekly_reports BOOLEAN DEFAULT TRUE,
  system_updates BOOLEAN DEFAULT FALSE,
  contextual_ai BOOLEAN DEFAULT TRUE,
  quiet_hours_enabled BOOLEAN DEFAULT FALSE,
  quiet_hours_start_hour INTEGER DEFAULT 22,
  quiet_hours_start_minute INTEGER DEFAULT 0,
  quiet_hours_end_hour INTEGER DEFAULT 8,
  quiet_hours_end_minute INTEGER DEFAULT 0,
  notification_frequency VARCHAR(20) DEFAULT 'Normal',
  sound_enabled BOOLEAN DEFAULT TRUE,
  vibration_enabled BOOLEAN DEFAULT TRUE,
  badge_count_enabled BOOLEAN DEFAULT TRUE,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES user_profiles(user_id)
);
```

## Cloudinary Integration

The frontend uses Cloudinary for profile picture management. You'll need to:

1. Set up a Cloudinary account
2. Configure upload presets for profile pictures
3. Implement server-side image deletion when users change/remove profile pictures
4. Handle image transformations for different screen sizes

## Security Considerations

1. **Profile Picture Uploads**: Validate image types and sizes on the backend
2. **Cloudinary Cleanup**: Implement cleanup of old profile pictures when new ones are uploaded
3. **Data Validation**: Validate all profile data before saving to database
4. **Rate Limiting**: Implement rate limiting for profile updates and notification creation
5. **Authentication**: Ensure all endpoints require valid Firebase authentication tokens

## Next Steps

After implementing these backend endpoints, the frontend will have:
- ✅ Dynamic greeting system with session tracking
- ✅ Comprehensive notification engine with settings
- ✅ Profile management with Cloudinary integration
- ✅ Settings synchronization across devices
- ✅ Profile picture sync between home and profile screens

This completes Phase 1 of the Drix app implementation as outlined in the functional plan.

---

## Phase 2 Implementation Summary: Content Management Core 📝

### Overview
Phase 2 implements the essential productivity features that form the core content management system of the Drix app. This phase focuses on **offline-first architecture** with comprehensive BLoC state management, local Isar database integration, and robust sync capabilities.

### 1. Notes Section ✅ IMPLEMENTED

#### Comprehensive Notes Management System
**Frontend Implementation:**
- Created complete `NotesBloc` with full CRUD operations and state management
- Implemented `LocalNote` Isar model with offline-first data persistence
- Advanced search functionality across title, content, and tags
- Tag-based organization with dynamic tag extraction
- Pin/unpin functionality for important notes
- Bulk operations (delete, pin/unpin, tag management)
- Export capabilities (JSON, CSV, TXT formats)
- Real-time sync with conflict resolution
- Comprehensive error handling and retry mechanisms

**Key Features Implemented:**
- **Offline-First**: All notes stored locally in Isar database, work without internet
- **Search & Filter**: Substring search across all note content and tags
- **Tag System**: Dynamic tag creation, filtering, and bulk tag operations
- **Bulk Operations**: Select multiple notes for batch operations
- **Export System**: Multiple export formats with date range filtering
- **Sync Engine**: Background synchronization with server when online
- **Error Tolerance**: Graceful handling of network failures and data conflicts

**Backend Requirements:**

##### Notes CRUD Operations
```http
POST /notes
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "title": "Meeting Notes - Q4 Planning",
  "content": "Discussed quarterly goals, budget allocation...",
  "tags": ["work", "meetings", "planning"],
  "is_pinned": true
}

Response:
{
  "success": true,
  "note_id": "note_12345",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

```http
GET /notes?page=1&limit=20&search=meeting&tag=work
Authorization: Bearer {access_token}

Response:
{
  "notes": [
    {
      "id": "note_12345",
      "title": "Meeting Notes - Q4 Planning",
      "content": "Discussed quarterly goals...",
      "tags": ["work", "meetings", "planning"],
      "is_pinned": true,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 15,
  "page": 1,
  "limit": 20,
  "available_tags": ["work", "personal", "meetings", "planning"]
}
```

```http
PUT /notes/{note_id}
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "title": "Updated Meeting Notes",
  "content": "Updated content...",
  "tags": ["work", "meetings", "planning", "updated"],
  "is_pinned": false
}

Response:
{
  "success": true,
  "updated_at": "2024-01-15T11:30:00Z"
}
```

```http
DELETE /notes/{note_id}
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "deleted_at": "2024-01-15T11:30:00Z"
}
```

##### Notes Search and Filtering
```http
GET /notes/search?query=quarterly&tag=work&page=1&limit=20
Authorization: Bearer {access_token}

Response:
{
  "results": [
    {
      "id": "note_12345",
      "title": "Meeting Notes - Q4 Planning",
      "content": "Discussed quarterly goals...",
      "tags": ["work", "meetings", "planning"],
      "is_pinned": true,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "relevance_score": 0.95
    }
  ],
  "total": 3,
  "page": 1,
  "limit": 20,
  "query": "quarterly",
  "filters": {
    "tag": "work"
  }
}
```

##### Bulk Operations
```http
POST /notes/bulk-operations
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "operation": "delete",
  "note_ids": ["note_123", "note_456", "note_789"]
}

Response:
{
  "success": true,
  "operation": "delete",
  "processed_count": 3,
  "failed_count": 0,
  "message": "3 notes deleted successfully"
}
```

```http
POST /notes/bulk-operations
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "operation": "add_tag",
  "note_ids": ["note_123", "note_456"],
  "tag": "important"
}

Response:
{
  "success": true,
  "operation": "add_tag",
  "processed_count": 2,
  "failed_count": 0,
  "message": "Tag 'important' added to 2 notes"
}
```

### 2. Tasks Section ✅ IMPLEMENTED

#### Advanced Task Management System
**Frontend Implementation:**
- Created comprehensive `TasksBloc` with full task lifecycle management
- Implemented `LocalTask` Isar model with categories, tags, and priority system
- Three-tab organization: Today, Upcoming, Past tasks
- Calendar integration for date-based task filtering
- Advanced filtering: status, priority, category, due date, tags
- Bulk operations: complete, delete, set priority, categorize
- Smart sorting: due date, priority, creation date, title, status
- Export functionality with multiple formats
- Offline-first with background sync

**Key Features Implemented:**
- **Task Categorization**: Broader grouping (work, personal, health, etc.)
- **Tag System**: Additional organization with multi-tag support
- **Priority Management**: High, Medium, Low priority with visual indicators
- **Due Date Filtering**: Today, Tomorrow, This Week, This Month, Overdue, No Date
- **Bulk Operations**: Efficient multi-task operations
- **Smart Search**: Search across title, description, category, and tags
- **Calendar Integration**: Date-based task filtering and scheduling
- **Offline Resilience**: Full functionality without internet connection

**Backend Requirements:**

##### Tasks CRUD Operations
```http
POST /tasks
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "title": "Complete project proposal",
  "description": "Finalize the Q4 project proposal document",
  "due_date": "2024-01-20T17:00:00Z",
  "priority": "high",
  "category": "work",
  "tags": ["project", "proposal", "deadline"],
  "is_completed": false
}

Response:
{
  "success": true,
  "task_id": "task_12345",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

```http
GET /tasks?status=pending&priority=high&category=work&due_date_filter=today&page=1&limit=20
Authorization: Bearer {access_token}

Response:
{
  "tasks": [
    {
      "id": "task_12345",
      "title": "Complete project proposal",
      "description": "Finalize the Q4 project proposal document",
      "due_date": "2024-01-20T17:00:00Z",
      "priority": "high",
      "category": "work",
      "tags": ["project", "proposal", "deadline"],
      "is_completed": false,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 8,
  "page": 1,
  "limit": 20,
  "available_categories": ["work", "personal", "health", "finance"],
  "available_tags": ["project", "proposal", "deadline", "meeting", "urgent"]
}
```

```http
PUT /tasks/{task_id}
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "title": "Updated task title",
  "description": "Updated description",
  "due_date": "2024-01-21T17:00:00Z",
  "priority": "medium",
  "category": "personal",
  "tags": ["updated", "personal"],
  "is_completed": true
}

Response:
{
  "success": true,
  "updated_at": "2024-01-15T11:30:00Z"
}
```

##### Task Search and Filtering
```http
GET /tasks/search?query=proposal&status=pending&priority=high&page=1&limit=20
Authorization: Bearer {access_token}

Response:
{
  "results": [
    {
      "id": "task_12345",
      "title": "Complete project proposal",
      "description": "Finalize the Q4 project proposal document",
      "due_date": "2024-01-20T17:00:00Z",
      "priority": "high",
      "category": "work",
      "tags": ["project", "proposal", "deadline"],
      "is_completed": false,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "relevance_score": 0.98
    }
  ],
  "total": 2,
  "page": 1,
  "limit": 20
}
```

##### Task Bulk Operations
```http
POST /tasks/bulk-operations
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "operation": "complete",
  "task_ids": ["task_123", "task_456", "task_789"]
}

Response:
{
  "success": true,
  "operation": "complete",
  "processed_count": 3,
  "failed_count": 0,
  "message": "3 tasks marked as completed"
}
```

```http
POST /tasks/bulk-operations
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "operation": "set_priority",
  "task_ids": ["task_123", "task_456"],
  "priority": "high"
}

Response:
{
  "success": true,
  "operation": "set_priority",
  "processed_count": 2,
  "failed_count": 0,
  "message": "Priority set to 'high' for 2 tasks"
}
```

### 3. Calendar Screen ✅ IMPLEMENTED

#### Comprehensive Calendar Management
**Frontend Implementation:**
- Created `CalendarBloc` with full event management capabilities
- Multiple calendar view modes: Month, Week, Day, Agenda
- Event creation, editing, and deletion with rich metadata
- Advanced search and filtering by event type, date range
- Calendar navigation with today/previous/next period controls
- Event categorization (meeting, appointment, reminder, etc.)
- All-day event support with location and attendee management
- Export functionality for calendar data
- Real-time event updates and conflict detection

**Key Features Implemented:**
- **Multi-View Support**: Month, Week, Day, and Agenda views
- **Event Types**: Meeting, Appointment, Reminder, Birthday, Holiday, Task
- **Rich Event Data**: Location, attendees, all-day events, descriptions
- **Smart Navigation**: Previous/Next period, Jump to Today
- **Event Search**: Search across titles, descriptions, locations
- **Date Filtering**: Filter events by date ranges and types
- **Conflict Detection**: Identify overlapping events
- **Export Options**: ICS, JSON, CSV formats

**Backend Requirements:**

##### Calendar Events CRUD
```http
POST /calendar/events
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "title": "Team Meeting",
  "description": "Weekly team sync meeting",
  "start_time": "2024-01-16T14:00:00Z",
  "end_time": "2024-01-16T15:00:00Z",
  "type": "meeting",
  "is_all_day": false,
  "location": "Conference Room A",
  "attendees": ["<EMAIL>", "<EMAIL>"]
}

Response:
{
  "success": true,
  "event_id": "event_12345",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

```http
GET /calendar/events?start_date=2024-01-15&end_date=2024-01-22&type=meeting
Authorization: Bearer {access_token}

Response:
{
  "events": [
    {
      "id": "event_12345",
      "title": "Team Meeting",
      "description": "Weekly team sync meeting",
      "start_time": "2024-01-16T14:00:00Z",
      "end_time": "2024-01-16T15:00:00Z",
      "type": "meeting",
      "is_all_day": false,
      "location": "Conference Room A",
      "attendees": ["<EMAIL>", "<EMAIL>"],
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 5,
  "date_range": {
    "start_date": "2024-01-15",
    "end_date": "2024-01-22"
  },
  "available_types": ["meeting", "appointment", "reminder", "birthday"]
}
```

##### Calendar Search and Export
```http
GET /calendar/events/search?query=team&type=meeting&start_date=2024-01-01&end_date=2024-01-31
Authorization: Bearer {access_token}

Response:
{
  "results": [
    {
      "id": "event_12345",
      "title": "Team Meeting",
      "description": "Weekly team sync meeting",
      "start_time": "2024-01-16T14:00:00Z",
      "end_time": "2024-01-16T15:00:00Z",
      "type": "meeting",
      "is_all_day": false,
      "location": "Conference Room A",
      "attendees": ["<EMAIL>", "<EMAIL>"],
      "relevance_score": 0.92
    }
  ],
  "total": 3,
  "query": "team",
  "filters": {
    "type": "meeting",
    "date_range": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-31"
    }
  }
}
```

### 4. Sync Engine Integration ✅ IMPLEMENTED

#### Robust Offline-First Synchronization
**Frontend Implementation:**
- Enhanced `SyncEngine` with comprehensive conflict resolution
- Queue-based sync operations with retry mechanisms
- Optimistic updates with rollback capabilities
- Background sync with network state awareness
- Conflict detection and resolution strategies
- Sync status tracking and user feedback
- Batch operations for efficient data transfer
- Error recovery and retry logic

**Key Features:**
- **Offline-First**: All operations work offline, sync when online
- **Conflict Resolution**: Smart merging of conflicting changes
- **Retry Logic**: Exponential backoff for failed sync operations
- **Batch Sync**: Efficient bulk data synchronization
- **Status Tracking**: Real-time sync status for all entities
- **Error Recovery**: Graceful handling of sync failures

**Backend Requirements:**

##### Sync Operations
```http
POST /sync/batch
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "operations": [
    {
      "type": "create",
      "entity": "note",
      "local_id": "local_note_123",
      "data": {
        "title": "New Note",
        "content": "Note content...",
        "tags": ["sync", "test"]
      },
      "timestamp": "2024-01-15T10:30:00Z"
    },
    {
      "type": "update",
      "entity": "task",
      "id": "task_456",
      "data": {
        "title": "Updated Task",
        "is_completed": true
      },
      "timestamp": "2024-01-15T10:35:00Z"
    }
  ]
}

Response:
{
  "success": true,
  "results": [
    {
      "local_id": "local_note_123",
      "server_id": "note_789",
      "status": "created",
      "timestamp": "2024-01-15T10:30:05Z"
    },
    {
      "id": "task_456",
      "status": "updated",
      "timestamp": "2024-01-15T10:35:02Z"
    }
  ],
  "conflicts": [],
  "processed_count": 2,
  "failed_count": 0
}
```

##### Conflict Resolution
```http
GET /sync/conflicts
Authorization: Bearer {access_token}

Response:
{
  "conflicts": [
    {
      "entity": "task",
      "id": "task_123",
      "conflict_type": "concurrent_modification",
      "local_version": {
        "title": "Local Task Title",
        "updated_at": "2024-01-15T10:30:00Z"
      },
      "server_version": {
        "title": "Server Task Title",
        "updated_at": "2024-01-15T10:32:00Z"
      },
      "suggested_resolution": "server_wins"
    }
  ],
  "total": 1
}
```

```http
POST /sync/resolve-conflicts
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "resolutions": [
    {
      "entity": "task",
      "id": "task_123",
      "resolution": "merge",
      "merged_data": {
        "title": "Merged Task Title",
        "description": "Combined description",
        "updated_at": "2024-01-15T10:35:00Z"
      }
    }
  ]
}

Response:
{
  "success": true,
  "resolved_count": 1,
  "message": "Conflicts resolved successfully"
}
```

### 5. Enhanced API Service ✅ IMPLEMENTED

#### Comprehensive API Integration
**Frontend Implementation:**
- Extended `ApiService` with all Phase 2 endpoints
- Implemented comprehensive error handling and retry logic
- Added request/response interceptors for authentication
- Network state awareness and offline queue management
- Request caching and response optimization
- Timeout handling and connection management

**Key Features:**
- **Complete CRUD Operations**: All entities support full lifecycle management
- **Advanced Search**: Unified search across all content types
- **Bulk Operations**: Efficient batch processing
- **Error Handling**: Comprehensive error recovery and user feedback
- **Caching Strategy**: Smart caching for improved performance
- **Authentication**: Seamless token management and refresh

### 6. Service Locator Integration ✅ IMPLEMENTED

#### Dependency Injection Architecture
**Frontend Implementation:**
- Registered all new BLoCs in service locator
- Proper dependency injection with factory patterns
- Lifecycle management for all services
- Clean separation of concerns
- Testable architecture with mock implementations

**Key Components:**
- **NotesBloc**: Factory registration with Isar and API dependencies
- **TasksBloc**: Complete dependency injection setup
- **CalendarBloc**: Service integration with API service
- **Mock Services**: Comprehensive test implementations

### 7. Offline-First Architecture Optimizations 🚀

#### How Phase 2 Optimizes for Offline-First Experience:

##### **Effective (Does What Users Need)**
- **Complete Functionality Offline**: All CRUD operations work without internet
- **Smart Sync**: Automatic background synchronization when online
- **Conflict Resolution**: Intelligent merging of conflicting changes
- **Data Integrity**: Consistent data state across offline/online transitions

##### **Efficient (Fast, Minimal Effort)**
- **Local Database**: Isar provides lightning-fast local operations
- **Optimistic Updates**: Immediate UI feedback, sync in background
- **Batch Operations**: Efficient bulk processing reduces API calls
- **Smart Caching**: Reduced network requests through intelligent caching
- **Queue Management**: Background sync queue prevents blocking UI

##### **Engaging (Pleasant to Use)**
- **Instant Responsiveness**: No waiting for network operations
- **Visual Feedback**: Clear sync status indicators and progress
- **Seamless Transitions**: Smooth offline/online experience
- **Rich Interactions**: Full feature set available offline
- **Smart Notifications**: Informative sync status and conflict alerts

##### **Error-Tolerant (Handles Mistakes Gracefully)**
- **Retry Mechanisms**: Exponential backoff for failed operations
- **Conflict Detection**: Automatic identification of data conflicts
- **Rollback Capabilities**: Undo operations if sync fails
- **Graceful Degradation**: Reduced functionality instead of complete failure
- **User Control**: Manual sync triggers and conflict resolution options
- **Data Recovery**: Robust error recovery and data restoration

### 8. Technical Implementation Details

#### BLoC Architecture Benefits:
- **Predictable State Management**: Clear state transitions and error handling
- **Testability**: Easy unit testing with mock dependencies
- **Separation of Concerns**: Clean architecture with distinct layers
- **Reactive Programming**: Stream-based state updates
- **Memory Efficiency**: Proper lifecycle management and disposal

#### Isar Database Advantages:
- **High Performance**: Native Dart database with excellent speed
- **Type Safety**: Compile-time type checking for data models
- **Query Optimization**: Efficient indexing and query execution
- **Schema Evolution**: Easy database migrations and updates
- **Cross-Platform**: Consistent behavior across all platforms

#### Sync Engine Features:
- **Queue-Based Operations**: Reliable operation ordering and execution
- **Network Awareness**: Automatic sync when connection is available
- **Conflict Resolution**: Multiple strategies for handling conflicts
- **Error Recovery**: Robust error handling and retry mechanisms
- **Status Tracking**: Real-time sync status for all entities

### 9. Backend Integration Requirements Summary

#### Critical Endpoints Needed:
1. **Notes**: Full CRUD + Search + Bulk Operations
2. **Tasks**: Full CRUD + Search + Bulk Operations + Categories/Tags
3. **Calendar**: Full CRUD + Search + Event Management
4. **Sync**: Batch operations + Conflict resolution
5. **Search**: Unified search across all content types

#### Authentication Requirements:
- Bearer token authentication for all endpoints
- Token refresh mechanism
- User-scoped data access
- Rate limiting and security measures

#### Data Consistency Requirements:
- Timestamp-based conflict detection
- Optimistic locking for concurrent modifications
- Atomic batch operations
- Transaction support for data integrity

#### Performance Requirements:
- Pagination support for large datasets
- Efficient search indexing
- Bulk operation optimization
- Response time under 200ms for CRUD operations

### 10. Next Steps for Backend Implementation

#### Priority 1 (Critical):
1. Implement Notes CRUD endpoints with search
2. Implement Tasks CRUD endpoints with categories/tags
3. Set up basic sync infrastructure
4. Implement authentication and user management

#### Priority 2 (Important):
1. Calendar events management
2. Bulk operations for all entities
3. Advanced search across all content types
4. Conflict resolution mechanisms

#### Priority 3 (Enhancement):
1. Export functionality
2. Advanced filtering and sorting
3. Analytics and reporting endpoints
4. Performance optimizations

This Phase 2 implementation provides a **robust, scalable foundation** for the Drix app's core productivity features with comprehensive offline-first capabilities, ensuring users have a seamless experience regardless of network connectivity.

---

## Phase 3: Contact Management - Backend Requirements

### Overview
The frontend has implemented comprehensive contact management functionality including device integration, VCF generation, social media deep linking, and advanced contact operations. The backend needs to support these features with robust APIs and data management.

### Frontend Implementation Status
✅ **Completed Frontend Features:**
- Contact creation, editing, and deletion with full UI
- Real VCF file generation and sharing functionality
- Device contacts integration (save to device contacts app)
- Social media deep linking with app detection
- Contact permission management for device access
- Advanced contact actions (call, SMS, share, edit, delete)
- Contact search and filtering capabilities
- Image upload support for contact photos
- Edit contact screen with full CRUD operations
- Contact action buttons in contact details modal

### Required Backend API Endpoints

#### 1. Contact Management APIs

**POST /api/contacts**
- Create new contact
- Request Body:
```json
{
  "name": "string (required)",
  "phone": "string (required)",
  "email": "string (optional)",
  "location": "string (optional)",
  "socialPlatform": "string (optional)",
  "socialUsername": "string (optional)",
  "notes": "string (optional)",
  "imagePath": "string (optional)",
  "deviceSyncEnabled": "boolean (default: false)"
}
```
- Response: Contact object with generated ID and timestamps
- Validation: Phone number format, unique phone per user
- Status Codes: 201 Created, 400 Bad Request, 409 Conflict

**GET /api/contacts**
- Retrieve all contacts for authenticated user
- Query Parameters:
  - `search`: string (optional) - search by name, phone, or location
  - `platform`: string (optional) - filter by social platform
  - `limit`: integer (optional, default: 50, max: 100)
  - `offset`: integer (optional, default: 0)
  - `sortBy`: string (optional) - "name", "createdAt", "updatedAt"
  - `sortOrder`: string (optional) - "asc", "desc"
- Response: Paginated array of contact objects
- Status Codes: 200 OK, 400 Bad Request

**GET /api/contacts/{contactId}**
- Retrieve specific contact by ID
- Response: Contact object or 404 if not found
- Security: Verify contact belongs to authenticated user
- Status Codes: 200 OK, 404 Not Found, 403 Forbidden

**PUT /api/contacts/{contactId}**
- Update existing contact
- Request Body: Same as POST with all fields optional except ID
- Response: Updated contact object
- Validation: Same as POST, check for conflicts
- Status Codes: 200 OK, 400 Bad Request, 404 Not Found, 409 Conflict

**DELETE /api/contacts/{contactId}**
- Delete contact (soft delete recommended)
- Response: 204 No Content
- Security: Verify contact belongs to authenticated user
- Status Codes: 204 No Content, 404 Not Found, 403 Forbidden

#### 2. Contact Export and Sharing APIs

**GET /api/contacts/{contactId}/vcf**
- Generate VCF file for specific contact
- Response: VCF file content with proper MIME type
- Headers:
  - `Content-Type: text/vcard`
  - `Content-Disposition: attachment; filename="{contact_name}.vcf"`
- VCF Format: vCard 3.0 standard with UTF-8 encoding
- Status Codes: 200 OK, 404 Not Found

**POST /api/contacts/bulk-export**
- Export multiple contacts as VCF or other formats
- Request Body:
```json
{
  "contactIds": ["string array"],
  "format": "vcf" | "csv" | "json",
  "includeImages": "boolean (default: false)"
}
```
- Response: File download or presigned URL for large exports
- Limits: Maximum 100 contacts per export
- Status Codes: 200 OK, 400 Bad Request, 413 Payload Too Large

**GET /api/contacts/export-all**
- Export all user contacts
- Query Parameters:
  - `format`: "vcf" | "csv" | "json"
  - `includeImages`: boolean
- Response: File download or presigned URL
- Status Codes: 200 OK, 400 Bad Request

#### 3. Device Integration APIs

**POST /api/contacts/{contactId}/device-sync**
- Mark contact as synced with device
- Request Body:
```json
{
  "deviceContactId": "string (optional)",
  "syncStatus": "synced" | "failed" | "pending",
  "syncError": "string (optional)",
  "deviceInfo": {
    "platform": "android" | "ios",
    "version": "string"
  }
}
```
- Response: Updated sync status
- Status Codes: 200 OK, 404 Not Found

**GET /api/contacts/sync-status**
- Get device sync status for all contacts
- Response:
```json
{
  "contacts": [
    {
      "contactId": "string",
      "syncStatus": "string",
      "lastSyncAttempt": "ISO 8601 timestamp",
      "syncError": "string"
    }
  ],
  "summary": {
    "total": "integer",
    "synced": "integer",
    "pending": "integer",
    "failed": "integer"
  }
}
```
- Status Codes: 200 OK

**POST /api/contacts/bulk-device-sync**
- Sync multiple contacts to device
- Request Body:
```json
{
  "contactIds": ["string array"],
  "deviceInfo": {
    "platform": "android" | "ios",
    "version": "string"
  }
}
```
- Response: Bulk sync status
- Status Codes: 200 OK, 400 Bad Request

#### 4. Social Media Integration APIs

**GET /api/social-platforms**
- Get supported social media platforms with validation rules
- Response:
```json
{
  "platforms": [
    {
      "name": "twitter",
      "displayName": "Twitter",
      "urlPattern": "https://twitter.com/{username}",
      "appScheme": "twitter://user?screen_name={username}",
      "usernameValidation": "^[a-zA-Z0-9_]{1,15}$",
      "usernamePlaceholder": "@username",
      "color": "#1DA1F2"
    },
    {
      "name": "instagram",
      "displayName": "Instagram",
      "urlPattern": "https://instagram.com/{username}",
      "appScheme": "instagram://user?username={username}",
      "usernameValidation": "^[a-zA-Z0-9_.]{1,30}$",
      "usernamePlaceholder": "@username",
      "color": "#E4405F"
    }
  ]
}
```
- Status Codes: 200 OK

**POST /api/contacts/{contactId}/social-verify**
- Verify social media profile exists (optional feature)
- Request Body:
```json
{
  "platform": "string",
  "username": "string"
}
```
- Response:
```json
{
  "verified": "boolean",
  "profileExists": "boolean",
  "profileUrl": "string",
  "profileInfo": {
    "displayName": "string",
    "verified": "boolean",
    "followerCount": "integer"
  }
}
```
- Status Codes: 200 OK, 400 Bad Request

#### 5. Contact Search and Analytics APIs

**GET /api/contacts/search**
- Advanced contact search with filters
- Query Parameters:
  - `q`: string (required) - search query
  - `fields`: string (optional) - "name,phone,email,location,notes"
  - `platform`: string (optional) - filter by social platform
  - `dateFrom`: ISO date (optional) - created after date
  - `dateTo`: ISO date (optional) - created before date
  - `limit`: integer (optional, default: 20)
  - `offset`: integer (optional, default: 0)
- Response: Paginated search results with highlighting
- Status Codes: 200 OK, 400 Bad Request

**GET /api/contacts/analytics**
- Get contact statistics and insights
- Response:
```json
{
  "totalContacts": "integer",
  "contactsThisMonth": "integer",
  "topSocialPlatforms": [
    {"platform": "twitter", "count": 15},
    {"platform": "instagram", "count": 12}
  ],
  "topLocations": [
    {"location": "Coffee Shop Downtown", "count": 8},
    {"location": "Networking Event", "count": 5}
  ],
  "deviceSyncStats": {
    "synced": "integer",
    "pending": "integer",
    "failed": "integer"
  }
}
```
- Status Codes: 200 OK

### Required Data Models

#### Contact Model
```json
{
  "id": "string (UUID)",
  "userId": "string (UUID)",
  "name": "string (1-100 chars)",
  "phone": "string (E.164 format)",
  "email": "string (valid email or empty)",
  "location": "string (0-200 chars)",
  "socialPlatform": "string (platform name or empty)",
  "socialUsername": "string (platform-specific validation)",
  "notes": "string (0-1000 chars)",
  "imagePath": "string (CDN URL or empty)",
  "imagePublicId": "string (Cloudinary public ID)",
  "deviceSyncStatus": "disabled|pending|synced|failed",
  "deviceContactId": "string (device-specific ID)",
  "createdAt": "ISO 8601 timestamp",
  "updatedAt": "ISO 8601 timestamp",
  "syncedAt": "ISO 8601 timestamp (last device sync)",
  "deletedAt": "ISO 8601 timestamp (soft delete)"
}
```

#### Contact Sync Status Model
```json
{
  "contactId": "string (UUID)",
  "deviceSyncEnabled": "boolean",
  "lastSyncAttempt": "ISO 8601 timestamp",
  "syncStatus": "pending|synced|failed",
  "syncError": "string (error message if failed)",
  "deviceInfo": {
    "platform": "android|ios",
    "version": "string",
    "contactsAppVersion": "string"
  }
}
```

#### Social Platform Model
```json
{
  "name": "string (internal identifier)",
  "displayName": "string (user-facing name)",
  "urlPattern": "string (URL template with {username})",
  "appScheme": "string (deep link scheme)",
  "usernameValidation": "string (regex pattern)",
  "usernamePlaceholder": "string (UI placeholder)",
  "color": "string (hex color code)",
  "iconUrl": "string (platform icon URL)",
  "isActive": "boolean (platform enabled)"
}
```

### Authentication Requirements

**All contact endpoints require:**
- Valid JWT token in Authorization header: `Bearer {access_token}`
- User ID extracted from token for data isolation
- Rate limiting: 100 requests per minute per user for CRUD operations
- Rate limiting: 10 requests per minute per user for export operations
- CORS headers for web client support

**Permission Levels:**
- `contacts:read` - View own contacts
- `contacts:write` - Create/update own contacts
- `contacts:delete` - Delete own contacts
- `contacts:export` - Export own contacts

### Validation Requirements

#### Contact Field Validation
- **Name**: Required, 1-100 characters, Unicode letters/spaces/hyphens/apostrophes only
- **Phone**: Required, valid E.164 international format (+1234567890)
- **Email**: Optional, valid RFC 5322 email format if provided
- **Location**: Optional, 1-200 characters, basic sanitization
- **Social Username**: Platform-specific regex validation (see social platforms API)
- **Notes**: Optional, max 1000 characters, basic HTML sanitization

#### Business Rules
- Phone numbers must be unique per user (409 Conflict if duplicate)
- Social platform + username combinations must be unique per user
- Contact names should be unique per user (warning in response, not error)
- Maximum 1000 contacts per user (429 Too Many Requests if exceeded)
- Soft delete contacts (retain for 30 days before permanent deletion)

#### Input Sanitization
- Strip leading/trailing whitespace from all text fields
- Normalize phone numbers to E.164 format
- Convert email addresses to lowercase
- Escape HTML entities in notes field
- Validate image URLs are from approved CDN domains

### File Upload Requirements

#### Contact Images
- **Endpoint**: `POST /api/contacts/{contactId}/image`
- **Supported formats**: JPEG, PNG, WebP
- **Max file size**: 5MB per image
- **Image processing**:
  - Resize to 300x300px (square crop)
  - Optimize for web (80% quality)
  - Generate thumbnail (100x100px)
- **Storage**: Cloudinary or AWS S3 with CDN
- **Response**:
```json
{
  "imageUrl": "string (full size image URL)",
  "thumbnailUrl": "string (thumbnail URL)",
  "publicId": "string (storage identifier)"
}
```
- **Security**: Virus scanning, content type validation
- **Status Codes**: 200 OK, 400 Bad Request, 413 Payload Too Large

#### VCF File Generation Standards
- **Format**: vCard 3.0 standard (RFC 2426)
- **Required fields**:
  - `BEGIN:VCARD` / `END:VCARD`
  - `VERSION:3.0`
  - `FN` (formatted name)
  - `N` (structured name)
  - `TEL` (phone number)
- **Optional fields**: `EMAIL`, `ORG`, `URL`, `NOTE`, `REV`, `PHOTO`
- **Encoding**: UTF-8 with proper escaping
- **Line endings**: CRLF (\r\n) as per vCard standard
- **Line folding**: 75 character limit with continuation

**Example VCF Output:**
```
BEGIN:VCARD
VERSION:3.0
N:Doe;John;;;
FN:John Doe
TEL;TYPE=CELL:+1234567890
EMAIL;TYPE=HOME:<EMAIL>
ORG:Met at: Coffee Shop Downtown
URL:https://twitter.com/johndoe
NOTE:Met at networking event
REV:2024-01-15T10:30:00Z
END:VCARD
```

### Security Requirements

#### Data Protection
- All contact data encrypted at rest using AES-256
- Phone numbers hashed with salt for duplicate detection
- Social media usernames stored in plain text (needed for deep linking)
- Image URLs use signed URLs with 24-hour expiration
- Database connections use TLS 1.3
- API endpoints use HTTPS only

#### Privacy Controls
- Users can only access their own contacts (strict user isolation)
- Soft delete for contact removal (retain for 30 days for recovery)
- Export functionality respects user data ownership
- Device sync is opt-in per contact
- Contact sharing requires explicit user action
- No cross-user contact visibility or search

#### Audit Logging
- Log all contact CRUD operations with user ID and timestamp
- Log all export operations with file size and format
- Log failed authentication attempts
- Log device sync operations and results
- Retain audit logs for 90 days

### Performance Requirements

#### Response Time SLAs
- Contact CRUD operations: < 200ms (95th percentile)
- Contact search: < 500ms (95th percentile)
- VCF generation: < 1s for single contact, < 5s for bulk
- Image upload: < 3s for 5MB file
- Bulk export: < 10s for up to 100 contacts

#### Scalability Targets
- Support 1000 contacts per user
- Handle 10,000 concurrent users
- Process 1M API requests per hour
- Support 100GB total contact images
- 99.9% uptime SLA

#### Database Optimization
- Primary indexes on user_id, phone, email
- Composite indexes on (user_id, name), (user_id, social_platform)
- Full-text search index on name, location, notes
- Partitioning by user_id for large datasets
- Connection pooling with 100 max connections

#### Caching Strategy
- Redis cache for frequently accessed contacts (TTL: 1 hour)
- CDN caching for contact images (TTL: 24 hours)
- Application-level caching for social platform configs
- Database query result caching for search operations

### Integration Requirements

#### External Services
- **Cloudinary**: Image upload, optimization, and CDN delivery
  - API key management and secure upload signatures
  - Automatic image optimization and format conversion
  - Webhook notifications for upload completion
- **Twilio Lookup API**: Phone number validation and formatting (optional)
  - Real-time phone number verification
  - International format standardization
  - Carrier and line type information
- **Social Media APIs**: Profile verification (optional future feature)
  - Twitter API v2 for profile existence checks
  - Instagram Basic Display API for public profiles
  - LinkedIn API for professional profiles

#### Webhook Support (Future Enhancement)
- Contact sync status updates to mobile apps
- Bulk operation completion notifications
- Social media profile change detection
- Image processing completion events

**Webhook Payload Example:**
```json
{
  "event": "contact.sync.completed",
  "timestamp": "2024-01-15T10:30:00Z",
  "userId": "user_12345",
  "data": {
    "contactId": "contact_67890",
    "syncStatus": "synced",
    "deviceContactId": "device_abc123"
  }
}
```

### Error Handling

#### Standard Error Response Format
```json
{
  "error": {
    "code": "CONTACT_NOT_FOUND",
    "message": "Contact with ID {contactId} not found",
    "details": {
      "contactId": "contact_12345",
      "userId": "user_67890"
    },
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "req_abc123"
  }
}
```

#### Error Codes and HTTP Status Mapping
- `CONTACT_NOT_FOUND`: 404 Not Found
- `DUPLICATE_PHONE`: 409 Conflict
- `INVALID_PHONE_FORMAT`: 400 Bad Request
- `INVALID_EMAIL_FORMAT`: 400 Bad Request
- `CONTACT_LIMIT_EXCEEDED`: 429 Too Many Requests
- `DEVICE_SYNC_FAILED`: 500 Internal Server Error
- `VCF_GENERATION_FAILED`: 500 Internal Server Error
- `IMAGE_UPLOAD_FAILED`: 500 Internal Server Error
- `SOCIAL_VERIFICATION_FAILED`: 502 Bad Gateway
- `RATE_LIMIT_EXCEEDED`: 429 Too Many Requests
- `INSUFFICIENT_PERMISSIONS`: 403 Forbidden
- `INVALID_SOCIAL_USERNAME`: 400 Bad Request
- `EXPORT_SIZE_EXCEEDED`: 413 Payload Too Large

#### Error Handling Best Practices
- Include correlation IDs for request tracing
- Provide user-friendly error messages
- Log detailed error information server-side
- Implement exponential backoff for retries
- Graceful degradation for non-critical features

### Database Schema Requirements

#### Contacts Table
```sql
CREATE TABLE contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    location VARCHAR(200),
    social_platform VARCHAR(50),
    social_username VARCHAR(100),
    notes TEXT,
    image_path VARCHAR(500),
    image_public_id VARCHAR(100),
    device_sync_status VARCHAR(20) DEFAULT 'disabled',
    device_contact_id VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    synced_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,

    CONSTRAINT unique_user_phone UNIQUE(user_id, phone) DEFERRABLE,
    CONSTRAINT unique_user_social UNIQUE(user_id, social_platform, social_username) DEFERRABLE,
    CONSTRAINT valid_sync_status CHECK (device_sync_status IN ('disabled', 'pending', 'synced', 'failed')),
    CONSTRAINT valid_email CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Indexes for performance
CREATE INDEX idx_contacts_user_id ON contacts(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_contacts_phone ON contacts(phone) WHERE deleted_at IS NULL;
CREATE INDEX idx_contacts_name_gin ON contacts USING gin(to_tsvector('english', name)) WHERE deleted_at IS NULL;
CREATE INDEX idx_contacts_social ON contacts(social_platform, social_username) WHERE deleted_at IS NULL;
CREATE INDEX idx_contacts_created_at ON contacts(created_at DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_contacts_sync_status ON contacts(device_sync_status) WHERE deleted_at IS NULL;

-- Trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_contacts_updated_at BEFORE UPDATE ON contacts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### Contact Sync Logs Table
```sql
CREATE TABLE contact_sync_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contact_id UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
    sync_type VARCHAR(20) NOT NULL, -- 'device_save', 'vcf_export', 'bulk_export'
    sync_status VARCHAR(20) NOT NULL, -- 'success', 'failed', 'pending'
    sync_error TEXT,
    device_info JSONB,
    attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,

    CONSTRAINT valid_sync_type CHECK (sync_type IN ('device_save', 'vcf_export', 'bulk_export')),
    CONSTRAINT valid_sync_status CHECK (sync_status IN ('success', 'failed', 'pending'))
);

CREATE INDEX idx_sync_logs_contact_id ON contact_sync_logs(contact_id);
CREATE INDEX idx_sync_logs_attempted_at ON contact_sync_logs(attempted_at DESC);
CREATE INDEX idx_sync_logs_status ON contact_sync_logs(sync_status);
```

#### Social Platforms Configuration Table
```sql
CREATE TABLE social_platforms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    url_pattern VARCHAR(200) NOT NULL,
    app_scheme VARCHAR(200),
    username_validation VARCHAR(500),
    username_placeholder VARCHAR(50),
    color VARCHAR(7), -- hex color
    icon_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default platforms
INSERT INTO social_platforms (name, display_name, url_pattern, app_scheme, username_validation, username_placeholder, color) VALUES
('twitter', 'Twitter', 'https://twitter.com/{username}', 'twitter://user?screen_name={username}', '^[a-zA-Z0-9_]{1,15}$', '@username', '#1DA1F2'),
('instagram', 'Instagram', 'https://instagram.com/{username}', 'instagram://user?username={username}', '^[a-zA-Z0-9_.]{1,30}$', '@username', '#E4405F'),
('linkedin', 'LinkedIn', 'https://linkedin.com/in/{username}', 'linkedin://profile/{username}', '^[a-zA-Z0-9-]+$', 'profile-name', '#0077B5'),
('whatsapp', 'WhatsApp', 'https://wa.me/{username}', 'whatsapp://send?phone={username}', '^\+?[1-9]\d{1,14}$', '+1234567890', '#25D366');
```

### Testing Requirements

#### Unit Tests Required
- Contact CRUD operations with validation
- VCF generation accuracy and format compliance
- Phone number validation and normalization
- Social media username validation per platform
- Permission checks and user isolation
- Error handling and edge cases

#### Integration Tests Required
- End-to-end contact creation and retrieval flow
- Device sync simulation with mock responses
- Bulk export functionality with large datasets
- Image upload and processing pipeline
- Search and filtering accuracy with various queries
- Rate limiting enforcement

#### Load Testing Scenarios
- 1000 concurrent contact creations
- Bulk export of 100 contacts by 100 users simultaneously
- Search queries under high load
- Image upload stress testing
- Database connection pool exhaustion

### Monitoring and Analytics

#### Key Metrics to Track
- **API Performance**:
  - Request latency (p50, p95, p99)
  - Request rate (requests per second)
  - Error rate by endpoint
  - Database query performance
- **Business Metrics**:
  - Contact creation/update/delete rates
  - VCF generation success rates
  - Device sync success rates
  - Search query frequency and performance
  - Image upload success rates
  - User engagement with contact features

#### Alerts Required
- High error rates (>5% for any endpoint)
- Slow response times (>1s for CRUD operations)
- Failed device sync attempts (>10% failure rate)
- Storage quota approaching limits (>80% usage)
- Database connection pool exhaustion
- Rate limiting threshold breaches

#### Dashboards Needed
- Real-time API performance dashboard
- Contact management usage analytics
- Error tracking and resolution dashboard
- Infrastructure health monitoring
- User behavior analytics for contact features

### Deployment and Infrastructure

#### Environment Requirements
- **Production**: High availability with load balancing
- **Staging**: Production-like environment for testing
- **Development**: Lightweight setup for rapid iteration

#### Infrastructure Components
- **API Gateway**: Rate limiting, authentication, request routing
- **Application Servers**: Auto-scaling based on CPU/memory usage
- **Database**: PostgreSQL with read replicas for scaling
- **Cache**: Redis cluster for session and data caching
- **CDN**: CloudFront or similar for image delivery
- **Monitoring**: DataDog, New Relic, or similar APM solution

#### Security Considerations
- WAF (Web Application Firewall) for API protection
- DDoS protection and rate limiting
- Regular security audits and penetration testing
- Encrypted data transmission and storage
- Secure API key management
- Regular dependency updates and vulnerability scanning

---

**Phase 3 Contact Management Implementation Summary:**

The frontend has delivered a complete contact management system with:
- ✅ Full CRUD operations with intuitive UI
- ✅ Real VCF generation and device integration
- ✅ Social media deep linking with app detection
- ✅ Advanced search and filtering capabilities
- ✅ Comprehensive permission handling
- ✅ Production-ready error handling and validation
- ✅ Comprehensive test coverage

The backend implementation should prioritize:
1. **Security and Privacy**: Robust user data protection
2. **Performance**: Sub-200ms response times for core operations
3. **Scalability**: Support for 10K+ concurrent users
4. **Reliability**: 99.9% uptime with comprehensive monitoring
5. **Integration**: Seamless device and social media connectivity

This Phase 3 implementation provides a **enterprise-grade contact management system** that seamlessly integrates with device capabilities while maintaining the highest standards of security, performance, and user experience.

---

## Phase 4 Frontend Status Report

### What Frontend Has Implemented:

#### Smart Capture UI Components (90% Complete)
- ✅ **Smart Capture Screen**: Full-featured interface with sophisticated animations and state management
- ✅ **Content Display**: Grid/list view toggle with smooth transitions
- ✅ **Search & Filter**: Real-time search with advanced filtering options
- ✅ **Bulk Selection**: Multi-select mode with batch operations
- ✅ **Sort Options**: Multiple sorting criteria (chronological, alphabetical, type-based)
- ✅ **Content Actions**: Add to Notes, Chat with Drix, retry processing
- ✅ **Error Handling**: Comprehensive error states with user-friendly feedback
- ✅ **Loading States**: Skeleton loaders and progress indicators
- ✅ **Empty States**: Engaging empty state with onboarding guidance

#### Android Share Intent Integration (100% Complete)
- ✅ **AndroidManifest.xml**: Intent filters for text/plain, image/*, and multiple images
- ✅ **MainActivity.kt**: Complete rewrite with MethodChannel communication
- ✅ **ShareIntentService**: Stream-based architecture for handling shared content
- ✅ **Content Processing**: URL validation, content type detection, and error handling
- ✅ **Real-time Integration**: Immediate processing of shared content in Smart Capture screen

#### Content Management Interfaces (95% Complete)
- ✅ **Content CRUD**: Create, read, update, delete operations with optimistic updates
- ✅ **Tag Management**: Dynamic tag creation, filtering, and organization
- ✅ **Content Search**: Real-time search with highlighting and context
- ✅ **Processing Status**: Real-time status updates with polling mechanism
- ✅ **Content Integration**: Seamless "Add to Notes" and "Chat with Drix" workflows
- ✅ **Bulk Operations**: Multi-select delete and batch processing

#### Error Handling & User Feedback (100% Complete)
- ✅ **SmartCaptureErrorHandler**: Comprehensive error categorization and user-friendly messages
- ✅ **Network Error Handling**: Graceful degradation for offline scenarios
- ✅ **Processing Error Recovery**: Retry mechanisms with exponential backoff
- ✅ **User Feedback**: Toast notifications, snackbars, and inline error states
- ✅ **Loading Feedback**: Progress indicators and skeleton screens

### Backend Requirements for Full Feature:

#### Enhanced API Endpoints
- **Content Processing Pipeline**:
  - Image OCR and text extraction endpoints
  - PDF content parsing and metadata extraction
  - Video/audio transcription services
  - Rich media thumbnail generation
- **Advanced Content Analysis**:
  - AI-powered content categorization
  - Automatic tag suggestion based on content
  - Sentiment analysis for text content
  - Content similarity detection and clustering

#### Search Functionality
- **Full-Text Search**: Elasticsearch or similar for content indexing
- **Semantic Search**: Vector-based search for content similarity
- **Advanced Filters**: Date ranges, content types, processing status, custom metadata
- **Search Analytics**: Query performance tracking and optimization
- **Auto-complete**: Intelligent search suggestions based on user history

#### Integration APIs
- **Notes System Integration**:
  - Bi-directional sync between captured content and notes
  - Content embedding within notes with live updates
  - Note templates based on captured content types
- **Chat System Integration**:
  - Context injection for AI conversations
  - Content-based conversation starters
  - Chat history integration with captured content
- **Calendar Integration**:
  - Event creation from captured content (articles, emails, etc.)
  - Deadline extraction and reminder setup
  - Meeting content capture and organization

#### Real-time Updates
- **WebSocket Implementation**:
  - Live processing status updates
  - Real-time content synchronization across devices
  - Collaborative content sharing and editing
- **Push Notifications**:
  - Processing completion notifications
  - Content sharing alerts
  - Daily/weekly content summaries
- **Background Processing**:
  - Queue-based content processing
  - Batch processing for multiple items
  - Priority-based processing queues

#### Metadata Enhancement
- **Content Enrichment**:
  - Automatic metadata extraction (author, publish date, source)
  - Content quality scoring and relevance ranking
  - Related content suggestions
  - Content freshness tracking and updates
- **Smart Organization**:
  - AI-powered folder/category suggestions
  - Duplicate content detection and merging
  - Content lifecycle management (archive, delete)
  - Usage analytics and insights

#### Performance Optimizations
- **Caching Strategy**:
  - Redis-based content caching
  - CDN integration for media files
  - Intelligent cache invalidation
  - Progressive loading for large content lists
- **Response Time Improvements**:
  - Database query optimization
  - Lazy loading for content details
  - Pagination with infinite scroll
  - Background prefetching of likely-needed content
- **Scalability Enhancements**:
  - Horizontal scaling for processing workers
  - Load balancing for API endpoints
  - Database sharding for large content volumes
  - Microservices architecture for different content types

#### Additional Full-Featured Requirements
- **Content Versioning**: Track changes and maintain content history
- **Collaboration Features**: Share content with team members, comments, and annotations
- **Export/Import**: Bulk export to various formats (PDF, EPUB, etc.)
- **Analytics Dashboard**: Content capture patterns, processing metrics, usage insights
- **API Rate Limiting**: Intelligent throttling based on user tiers and usage patterns
- **Content Security**: Encryption at rest, secure sharing, access controls
- **Backup & Recovery**: Automated backups, point-in-time recovery, data redundancy
- **Compliance Features**: GDPR compliance, data retention policies, audit trails

---

## Phase 4 Frontend Status Report

### What Frontend Has Implemented:

#### Complete Smart Capture UI Components Now Functional:
- **Smart Capture Screen**: Fully functional main interface with glassmorphic design matching app aesthetics
- **Content Cards**: Interactive content display with thumbnails, metadata, and action buttons
- **Search & Filter Interface**: Real-time search with tag-based filtering and sort options
- **Content Detail Modal**: Comprehensive content viewing with full metadata and action options
- **Bulk Selection Mode**: Multi-select interface for batch operations
- **Processing Status Indicators**: Real-time visual feedback for content processing states
- **Error Handling UI**: User-friendly error messages and retry mechanisms

#### Android Share Intent Integration Status and Capabilities:
- **Native Android Integration**: Complete MainActivity.kt implementation with MethodChannel communication
- **Intent Filter Configuration**: AndroidManifest.xml configured for text/plain and image/* content types
- **Multi-format Support**: Handles text, single images, and multiple image sharing
- **Real-time Processing**: Immediate capture and processing of shared content
- **Background Handling**: Robust handling of app launch via share intents
- **Error Recovery**: Graceful handling of share intent failures

#### Content Management Interfaces and User Workflows:
- **Content Capture Flow**: Seamless URL and image capture with immediate feedback
- **Content Organization**: Tag-based organization with visual categorization
- **Search & Discovery**: Advanced search with real-time filtering and sorting
- **Content Actions**: Add to Notes, Chat with Drix, and content management actions
- **Bulk Operations**: Efficient multi-select and batch processing
- **Content Editing**: In-place editing of titles, summaries, and tags

#### Error Handling and User Feedback Systems Implemented:
- **Comprehensive Error Handler**: SmartCaptureErrorHandler with categorized error types
- **User-friendly Messages**: Clear, actionable error messages with retry options
- **Visual Feedback**: Loading states, success indicators, and error states
- **Graceful Degradation**: Fallback behaviors for network and processing failures
- **Real-time Status Updates**: Live processing status with progress indicators

### Backend Requirements for Full Feature:

#### Enhanced API Endpoints:
- **Batch Processing**: Endpoints for processing multiple items simultaneously
- **Content Versioning**: Version control for content updates and history
- **Advanced Analytics**: User behavior tracking and content performance metrics
- **Content Recommendations**: AI-powered content suggestions based on user patterns
- **Export Functionality**: Bulk export capabilities for content backup

#### Search Functionality:
- **Full-text Search**: Advanced search across content body, not just metadata
- **Semantic Search**: AI-powered semantic search for related content discovery
- **Search Suggestions**: Auto-complete and search term suggestions
- **Saved Searches**: User-defined search filters and saved queries
- **Search Analytics**: Search performance and result relevance tracking

#### Integration APIs:
- **Deep Notes Integration**: Rich text import with formatting preservation
- **Advanced Chat Context**: Conversation threading with content references
- **Calendar Smart Events**: Intelligent event creation from content analysis
- **Third-party Integrations**: APIs for external service connections (Notion, Evernote, etc.)
- **Workflow Automation**: Trigger-based automation for content processing

#### Real-time Updates:
- **WebSocket Implementation**: Live updates for processing status and content changes
- **Push Notifications**: Mobile notifications for completed processing and updates
- **Collaborative Features**: Real-time sharing and collaboration on captured content
- **Sync Optimization**: Efficient delta sync for large content libraries

#### Metadata Enhancement:
- **Rich Content Extraction**: Enhanced metadata from various content types
- **Content Relationships**: Automatic linking of related content items
- **Smart Categorization**: AI-powered content categorization and clustering
- **Content Quality Scoring**: Relevance and quality assessment for captured content
- **Duplicate Detection**: Intelligent duplicate content identification and merging

#### Performance Optimizations:
- **Content Caching**: Intelligent caching strategies for frequently accessed content
- **Image Optimization**: Automatic image compression and format optimization
- **Lazy Loading**: Progressive content loading for large libraries
- **Background Processing**: Optimized background processing for better user experience
- **Response Time Improvements**: Sub-second response times for all operations

#### Additional Features for Full-Fledged Experience:
- **Content Sharing**: Social sharing capabilities with privacy controls
- **Content Templates**: Predefined templates for common content types
- **Advanced Tagging**: Hierarchical tags and tag relationships
- **Content Archiving**: Long-term storage and archival capabilities
- **Usage Analytics**: Detailed analytics on content usage and engagement
- **Content Backup**: Automated backup and restore functionality
- **API Rate Limiting**: Intelligent rate limiting based on user behavior
- **Content Security**: Advanced security features for sensitive content

---

*Last updated: 2025-01-04*
*Next review: 2025-01-18*
