import 'package:flutter_bloc/flutter_bloc.dart';
import '../../services/notification_service.dart';
import '../../models/notification_models.dart';
import 'notification_event.dart';
import 'notification_state.dart';

/// Notification BLoC for managing notifications and settings
class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final NotificationService _notificationService;

  NotificationBloc({
    required NotificationService notificationService,
  }) : _notificationService = notificationService,
       super(const NotificationInitial()) {
    
    // Register event handlers
    on<InitializeNotificationService>(_onInitializeNotificationService);
    on<LoadNotifications>(_onLoadNotifications);
    on<ScheduleNotification>(_onScheduleNotification);
    on<MarkNotificationAsRead>(_onMarkNotificationAsRead);
    on<CancelNotification>(_onCancelNotification);
    on<LoadNotificationSettings>(_onLoadNotificationSettings);
    on<UpdateNotificationSettings>(_onUpdateNotificationSettings);
    on<UpdateNotificationTypeSetting>(_onUpdateNotificationTypeSetting);
    on<UpdateQuietHours>(_onUpdateQuietHours);
    on<UpdateNotificationFrequency>(_onUpdateNotificationFrequency);
    on<ClearAllNotifications>(_onClearAllNotifications);
    on<SyncNotifications>(_onSyncNotifications);
    on<HandleNotificationTap>(_onHandleNotificationTap);
    on<RequestNotificationPermissions>(_onRequestNotificationPermissions);
    on<ScheduleContextualNotification>(_onScheduleContextualNotification);
    on<ScheduleTherapyReminder>(_onScheduleTherapyReminder);
    on<ScheduleTaskReminder>(_onScheduleTaskReminder);
    on<ScheduleDailyCheckIn>(_onScheduleDailyCheckIn);
    on<ScheduleWeeklyReport>(_onScheduleWeeklyReport);
  }

  /// Initialize notification service
  Future<void> _onInitializeNotificationService(
    InitializeNotificationService event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(const NotificationLoading());
      
      await _notificationService.initialize();
      
      emit(const NotificationServiceInitialized());
      
      // Load initial data
      add(const LoadNotifications());
      add(const LoadNotificationSettings());
    } catch (e) {
      emit(NotificationError('Failed to initialize notification service: $e'));
    }
  }

  /// Load notifications
  Future<void> _onLoadNotifications(
    LoadNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(const NotificationLoading());
      
      final notifications = await _notificationService.getAllNotifications();
      final settings = await _notificationService.getNotificationSettings();
      final unreadCount = notifications.where((n) => !n.isRead).length;
      
      emit(NotificationsLoaded(
        notifications: notifications,
        settings: settings,
        unreadCount: unreadCount,
      ));
    } catch (e) {
      emit(NotificationError('Failed to load notifications: $e'));
    }
  }

  /// Schedule notification
  Future<void> _onScheduleNotification(
    ScheduleNotification event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(NotificationScheduling(event.title));
      
      await _notificationService.scheduleNotification(
        title: event.title,
        body: event.body,
        type: event.type,
        scheduledTime: event.scheduledTime,
        priority: event.priority,
        navigationRoute: event.navigationRoute,
        navigationData: event.navigationData,
      );
      
      emit(const NotificationOperationSuccess('Notification scheduled successfully'));
      
      // Reload notifications
      add(const LoadNotifications());
    } catch (e) {
      emit(NotificationSchedulingError(e.toString()));
    }
  }

  /// Mark notification as read
  Future<void> _onMarkNotificationAsRead(
    MarkNotificationAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _notificationService.markAsRead(event.notificationId);
      
      emit(NotificationMarkedAsRead(event.notificationId));
      
      // Reload notifications to update unread count
      add(const LoadNotifications());
    } catch (e) {
      emit(NotificationError('Failed to mark notification as read: $e'));
    }
  }

  /// Cancel notification
  Future<void> _onCancelNotification(
    CancelNotification event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _notificationService.cancelNotification(event.notificationId);
      
      emit(NotificationCancelled(event.notificationId));
      
      // Reload notifications
      add(const LoadNotifications());
    } catch (e) {
      emit(NotificationError('Failed to cancel notification: $e'));
    }
  }

  /// Load notification settings
  Future<void> _onLoadNotificationSettings(
    LoadNotificationSettings event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final settings = await _notificationService.getNotificationSettings();
      emit(NotificationSettingsLoaded(settings));
    } catch (e) {
      emit(NotificationError('Failed to load notification settings: $e'));
    }
  }

  /// Update notification settings
  Future<void> _onUpdateNotificationSettings(
    UpdateNotificationSettings event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(const NotificationSettingsUpdating());
      
      await _notificationService.updateNotificationSettings(event.settings);
      
      emit(NotificationSettingsUpdated(event.settings));
      emit(const NotificationOperationSuccess('Settings updated successfully'));
    } catch (e) {
      emit(NotificationSettingsError(e.toString()));
    }
  }

  /// Update notification type setting
  Future<void> _onUpdateNotificationTypeSetting(
    UpdateNotificationTypeSetting event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final currentSettings = await _notificationService.getNotificationSettings();
      
      // Update specific type setting
      switch (event.type) {
        case NotificationType.therapyReminder:
          currentSettings.therapyReminders = event.enabled;
          break;
        case NotificationType.taskNotification:
          currentSettings.taskNotifications = event.enabled;
          break;
        case NotificationType.dailyCheckIn:
          currentSettings.dailyCheckIns = event.enabled;
          break;
        case NotificationType.weeklyReport:
          currentSettings.weeklyReports = event.enabled;
          break;
        case NotificationType.systemUpdate:
          currentSettings.systemUpdates = event.enabled;
          break;
        case NotificationType.contextualAI:
          currentSettings.contextualAI = event.enabled;
          break;
      }
      
      await _notificationService.updateNotificationSettings(currentSettings);
      
      emit(NotificationSettingsUpdated(currentSettings));
    } catch (e) {
      emit(NotificationSettingsError(e.toString()));
    }
  }

  /// Update quiet hours
  Future<void> _onUpdateQuietHours(
    UpdateQuietHours event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final currentSettings = await _notificationService.getNotificationSettings();
      
      currentSettings.quietHoursEnabled = event.enabled;
      currentSettings.quietHoursStartHour = event.startHour;
      currentSettings.quietHoursStartMinute = event.startMinute;
      currentSettings.quietHoursEndHour = event.endHour;
      currentSettings.quietHoursEndMinute = event.endMinute;
      
      await _notificationService.updateNotificationSettings(currentSettings);
      
      emit(NotificationSettingsUpdated(currentSettings));
    } catch (e) {
      emit(NotificationSettingsError(e.toString()));
    }
  }

  /// Update notification frequency
  Future<void> _onUpdateNotificationFrequency(
    UpdateNotificationFrequency event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final currentSettings = await _notificationService.getNotificationSettings();
      
      currentSettings.notificationFrequency = event.frequency;
      
      await _notificationService.updateNotificationSettings(currentSettings);
      
      emit(NotificationSettingsUpdated(currentSettings));
    } catch (e) {
      emit(NotificationSettingsError(e.toString()));
    }
  }

  /// Clear all notifications
  Future<void> _onClearAllNotifications(
    ClearAllNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final notifications = await _notificationService.getAllNotifications();
      
      for (final notification in notifications) {
        await _notificationService.cancelNotification(notification.id);
      }
      
      emit(const NotificationsCleared());
      emit(const NotificationOperationSuccess('All notifications cleared'));
      
      // Reload notifications
      add(const LoadNotifications());
    } catch (e) {
      emit(NotificationError('Failed to clear notifications: $e'));
    }
  }

  /// Sync notifications with backend
  Future<void> _onSyncNotifications(
    SyncNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(const NotificationsSyncing());
      
      // This would sync pending notifications with backend
      // Implementation depends on backend sync strategy
      
      emit(const NotificationsSynced(0));
      
      // Reload notifications
      add(const LoadNotifications());
    } catch (e) {
      emit(NotificationSyncError(e.toString()));
    }
  }

  /// Handle notification tap
  Future<void> _onHandleNotificationTap(
    HandleNotificationTap event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      // Mark as read
      await _notificationService.markAsRead(event.notificationId);
      
      // Emit navigation state
      emit(NotificationTapped(
        notificationId: event.notificationId,
        navigationRoute: event.navigationRoute,
        navigationData: event.navigationData,
      ));
      
      // Reload notifications
      add(const LoadNotifications());
    } catch (e) {
      emit(NotificationError('Failed to handle notification tap: $e'));
    }
  }

  /// Request notification permissions
  Future<void> _onRequestNotificationPermissions(
    RequestNotificationPermissions event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      // This would be handled by the notification service initialization
      emit(const NotificationPermissionsGranted(true));
    } catch (e) {
      emit(const NotificationPermissionDenied());
    }
  }

  /// Schedule contextual notification
  Future<void> _onScheduleContextualNotification(
    ScheduleContextualNotification event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _notificationService.scheduleNotification(
        title: 'Drix has a suggestion',
        body: event.suggestion,
        type: NotificationType.contextualAI,
        scheduledTime: event.scheduledTime,
        priority: NotificationPriority.normal,
        navigationData: {'context': event.context},
      );

      emit(const NotificationOperationSuccess('Contextual notification scheduled'));
    } catch (e) {
      emit(NotificationSchedulingError(e.toString()));
    }
  }

  /// Schedule therapy reminder
  Future<void> _onScheduleTherapyReminder(
    ScheduleTherapyReminder event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final title = 'Therapy Session Reminder';
      final body = event.customMessage ??
          'Your ${event.sessionType ?? 'therapy'} session is starting soon';

      await _notificationService.scheduleNotification(
        title: title,
        body: body,
        type: NotificationType.therapyReminder,
        scheduledTime: event.sessionTime.subtract(const Duration(minutes: 15)),
        priority: NotificationPriority.high,
        navigationRoute: '/therapy',
        navigationData: {
          'session_time': event.sessionTime.toIso8601String(),
          'session_type': event.sessionType,
        },
      );

      emit(const NotificationOperationSuccess('Therapy reminder scheduled'));
    } catch (e) {
      emit(NotificationSchedulingError(e.toString()));
    }
  }

  /// Schedule task reminder
  Future<void> _onScheduleTaskReminder(
    ScheduleTaskReminder event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final title = 'Task Reminder';
      final body = 'Don\'t forget: ${event.taskTitle}';

      await _notificationService.scheduleNotification(
        title: title,
        body: body,
        type: NotificationType.taskNotification,
        scheduledTime: event.dueTime.subtract(const Duration(hours: 1)),
        priority: event.priority,
        navigationRoute: '/tasks',
        navigationData: {
          'task_id': event.taskId,
          'task_title': event.taskTitle,
        },
      );

      emit(const NotificationOperationSuccess('Task reminder scheduled'));
    } catch (e) {
      emit(NotificationSchedulingError(e.toString()));
    }
  }

  /// Schedule daily check-in
  Future<void> _onScheduleDailyCheckIn(
    ScheduleDailyCheckIn event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final title = 'Daily Check-in';
      final body = event.customMessage ?? 'How are you feeling today?';

      await _notificationService.scheduleNotification(
        title: title,
        body: body,
        type: NotificationType.dailyCheckIn,
        scheduledTime: event.scheduledTime,
        priority: NotificationPriority.normal,
        navigationRoute: '/mood-tracker',
      );

      emit(const NotificationOperationSuccess('Daily check-in scheduled'));
    } catch (e) {
      emit(NotificationSchedulingError(e.toString()));
    }
  }

  /// Schedule weekly report
  Future<void> _onScheduleWeeklyReport(
    ScheduleWeeklyReport event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final title = 'Weekly Progress Report';
      final body = 'Your weekly insights are ready to view';

      await _notificationService.scheduleNotification(
        title: title,
        body: body,
        type: NotificationType.weeklyReport,
        scheduledTime: event.scheduledTime,
        priority: NotificationPriority.normal,
        navigationRoute: '/progress',
        navigationData: event.reportData,
      );

      emit(const NotificationOperationSuccess('Weekly report notification scheduled'));
    } catch (e) {
      emit(NotificationSchedulingError(e.toString()));
    }
  }
}
