import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:ui';
import '../utils/design_tokens.dart';

class QuickActionOverlay extends StatefulWidget {
  final VoidCallback onAddNote;
  final VoidCallback onAddTask;
  final VoidCallback onDismiss;

  const QuickActionOverlay({
    super.key,
    required this.onAddNote,
    required this.onAddTask,
    required this.onDismiss,
  });

  @override
  State<QuickActionOverlay> createState() => _QuickActionOverlayState();
}

class _QuickActionOverlayState extends State<QuickActionOverlay>
    with TickerProviderStateMixin {
  
  late AnimationController _overlayController;
  late AnimationController _leftButtonController;
  late AnimationController _rightButtonController;
  
  late Animation<double> _overlayAnimation;
  late Animation<Offset> _leftButtonAnimation;
  late Animation<Offset> _rightButtonAnimation;
  late Animation<double> _leftButtonScaleAnimation;
  late Animation<double> _rightButtonScaleAnimation;
  late Animation<double> _textFadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
  }

  void _initializeAnimations() {
    // Overlay fade animation
    _overlayController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    // Button animations
    _leftButtonController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _rightButtonController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    // Overlay fade
    _overlayAnimation = Tween<double>(
      begin: 0.0,
      end: 0.6,
    ).animate(CurvedAnimation(
      parent: _overlayController,
      curve: Curves.easeOut,
    ));
    
    // Button slide animations (from center)
    _leftButtonAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(-120, 0), // 120px to the left
    ).animate(CurvedAnimation(
      parent: _leftButtonController,
      curve: Curves.elasticOut,
    ));
    
    _rightButtonAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(120, 0), // 120px to the right
    ).animate(CurvedAnimation(
      parent: _rightButtonController,
      curve: Curves.elasticOut,
    ));
    
    // Button scale animations (overshoot effect)
    _leftButtonScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _leftButtonController,
      curve: Curves.elasticOut,
    ));
    
    _rightButtonScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rightButtonController,
      curve: Curves.elasticOut,
    ));
    
    // Text fade animation
    _textFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _leftButtonController,
      curve: const Interval(0.7, 1.0, curve: Curves.easeOut),
    ));
  }

  void _startAnimationSequence() async {
    // Start overlay fade
    await _overlayController.forward();
    
    // Start left button animation
    _leftButtonController.forward();
    
    // Start right button animation with 150ms delay
    await Future.delayed(const Duration(milliseconds: 150));
    _rightButtonController.forward();
  }

  void _dismissOverlay() async {
    // Reverse animations
    _leftButtonController.reverse();
    _rightButtonController.reverse();
    await _overlayController.reverse();
    
    widget.onDismiss();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final centerX = screenSize.width / 2;
    final centerY = screenSize.height - 105; // Navbar height + button center

    return AnimatedBuilder(
      animation: _overlayAnimation,
      builder: (context, child) {
        return Material(
          color: Colors.black.withValues(alpha: _overlayAnimation.value),
          child: Stack(
            children: [
              // Background tap detector (covers entire screen)
              Positioned.fill(
                child: GestureDetector(
                  onTap: _dismissOverlay,
                  behavior: HitTestBehavior.opaque, // Ensures taps are captured
                  child: Container(color: Colors.transparent),
                ),
              ),
              
              // Buttons stack
              SizedBox.expand(
                child: Stack(
                  children: [
                    // Left button (Add Note)
                    AnimatedBuilder(
                      animation: _leftButtonController,
                      builder: (context, child) {
                        return Positioned(
                          left: centerX + _leftButtonAnimation.value.dx - 30, // 30 = button radius
                          top: centerY - 30,
                          child: Transform.scale(
                            scale: _leftButtonScaleAnimation.value,
                            child: _buildActionButton(
                              'Add a Note',
                              'assets/icons/note_navbar.svg',
                              widget.onAddNote,
                            ),
                          ),
                        );
                      },
                    ),
                    
                    // Right button (Add Task)
                    AnimatedBuilder(
                      animation: _rightButtonController,
                      builder: (context, child) {
                        return Positioned(
                          left: centerX + _rightButtonAnimation.value.dx - 30, // 30 = button radius
                          top: centerY - 30,
                          child: Transform.scale(
                            scale: _rightButtonScaleAnimation.value,
                            child: _buildActionButton(
                              'Add a Task',
                              'assets/icons/task_navbar.svg',
                              widget.onAddTask,
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildActionButton(String label, String iconPath, VoidCallback onTap) {
    return GestureDetector(
      onTapDown: (_) {
        HapticFeedback.lightImpact();
      },
      onTap: () {
        HapticFeedback.lightImpact();
        _dismissOverlay();
        onTap();
      },
      behavior: HitTestBehavior.opaque, // Prevent tap from propagating to background
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Button circle
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: DesignTokens.primaryInteractiveBlue,
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Center(
              child: SvgPicture.asset(
                iconPath,
                width: 24,
                height: 24,
                colorFilter: const ColorFilter.mode(
                  Colors.white,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Button label
          AnimatedBuilder(
            animation: _textFadeAnimation,
            builder: (context, child) {
              return Opacity(
                opacity: _textFadeAnimation.value,
                child: Text(
                  label,
                  style: DesignTokens.bodyStyle.copyWith(
                    color: DesignTokens.textPrimary,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _overlayController.dispose();
    _leftButtonController.dispose();
    _rightButtonController.dispose();
    super.dispose();
  }
}
