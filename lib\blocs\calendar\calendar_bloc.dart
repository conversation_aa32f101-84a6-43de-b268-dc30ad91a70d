import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../services/api_service.dart';
import 'calendar_event.dart';
import 'calendar_state.dart';

/// BLoC for managing calendar events
class CalendarBloc extends Bloc<CalendarEvent, CalendarState> {
  final ApiService _apiService;
  
  // In-memory storage for calendar events (since we don't have Isar models for calendar)
  final List<CalendarEventModel> _events = [];

  CalendarBloc({
    required ApiService apiService,
  })  : _apiService = apiService,
        super(const CalendarInitial()) {
    
    // Register event handlers
    on<LoadCalendarEvents>(_onLoadCalendarEvents);
    on<RefreshCalendarEvents>(_onRefreshCalendarEvents);
    on<AddCalendarEvent>(_onAddCalendarEvent);
    on<UpdateCalendarEvent>(_onUpdateCalendarEvent);
    on<DeleteCalendarEvent>(_onDeleteCalendarEvent);
    on<SelectCalendarDate>(_onSelectCalendarDate);
    on<ChangeCalendarView>(_onChangeCalendarView);
    on<NavigateToPreviousPeriod>(_onNavigateToPreviousPeriod);
    on<NavigateToNextPeriod>(_onNavigateToNextPeriod);
    on<NavigateToToday>(_onNavigateToToday);
    on<SearchCalendarEvents>(_onSearchCalendarEvents);
    on<FilterEventsByType>(_onFilterEventsByType);
    on<ClearCalendarFilters>(_onClearCalendarFilters);
    on<SyncCalendarEvents>(_onSyncCalendarEvents);
    on<ExportCalendarEvents>(_onExportCalendarEvents);
  }

  /// Load calendar events for a specific month
  Future<void> _onLoadCalendarEvents(
    LoadCalendarEvents event,
    Emitter<CalendarState> emit,
  ) async {
    emit(const CalendarLoading());

    try {
      // For now, use mock data since we don't have backend calendar endpoints
      _loadMockEvents();
      
      final eventsByDate = _groupEventsByDate(_events);
      
      emit(CalendarLoaded(
        events: _events,
        filteredEvents: _events,
        currentDate: event.month,
        selectedDate: DateTime.now(),
        eventsByDate: eventsByDate,
      ));

      // Trigger background sync
      add(const SyncCalendarEvents());
    } catch (e) {
      emit(CalendarOperationError(
        error: 'Failed to load calendar events: $e',
        operation: 'load',
      ));
    }
  }

  /// Refresh calendar events from server
  Future<void> _onRefreshCalendarEvents(
    RefreshCalendarEvents event,
    Emitter<CalendarState> emit,
  ) async {
    try {
      // TODO: Implement server refresh when backend is available
      // For now, just reload local events
      add(LoadCalendarEvents(DateTime.now()));
    } catch (e) {
      emit(CalendarOperationError(
        error: 'Failed to refresh calendar events: $e',
        operation: 'refresh',
      ));
    }
  }

  /// Add a new calendar event
  Future<void> _onAddCalendarEvent(
    AddCalendarEvent event,
    Emitter<CalendarState> emit,
  ) async {
    emit(const CalendarOperationInProgress(operation: 'add'));

    try {
      final now = DateTime.now();
      final newEvent = CalendarEventModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: event.title,
        description: event.description,
        startTime: event.startTime,
        endTime: event.endTime,
        type: event.type,
        isAllDay: event.isAllDay,
        location: event.location,
        attendees: event.attendees,
        createdAt: now,
        updatedAt: now,
      );

      _events.add(newEvent);

      emit(CalendarOperationSuccess(
        message: 'Event created successfully',
        operation: 'add',
        event: newEvent,
      ));

      // Reload calendar
      add(LoadCalendarEvents(DateTime.now()));
    } catch (e) {
      emit(CalendarOperationError(
        error: 'Failed to create event: $e',
        operation: 'add',
      ));
    }
  }

  /// Update an existing calendar event
  Future<void> _onUpdateCalendarEvent(
    UpdateCalendarEvent event,
    Emitter<CalendarState> emit,
  ) async {
    emit(CalendarOperationInProgress(operation: 'update', eventId: event.eventId));

    try {
      final eventIndex = _events.indexWhere((e) => e.id == event.eventId);
      if (eventIndex == -1) {
        throw Exception('Event not found');
      }

      final existingEvent = _events[eventIndex];
      final updatedEvent = existingEvent.copyWith(
        title: event.title,
        description: event.description,
        startTime: event.startTime,
        endTime: event.endTime,
        type: event.type,
        isAllDay: event.isAllDay,
        location: event.location,
        attendees: event.attendees,
        updatedAt: DateTime.now(),
      );

      _events[eventIndex] = updatedEvent;

      emit(CalendarOperationSuccess(
        message: 'Event updated successfully',
        operation: 'update',
        event: updatedEvent,
      ));

      // Reload calendar
      add(LoadCalendarEvents(DateTime.now()));
    } catch (e) {
      emit(CalendarOperationError(
        error: 'Failed to update event: $e',
        operation: 'update',
        eventId: event.eventId,
      ));
    }
  }

  /// Delete a calendar event
  Future<void> _onDeleteCalendarEvent(
    DeleteCalendarEvent event,
    Emitter<CalendarState> emit,
  ) async {
    emit(CalendarOperationInProgress(operation: 'delete', eventId: event.eventId));

    try {
      final eventIndex = _events.indexWhere((e) => e.id == event.eventId);
      if (eventIndex == -1) {
        throw Exception('Event not found');
      }

      _events.removeAt(eventIndex);

      emit(const CalendarOperationSuccess(
        message: 'Event deleted successfully',
        operation: 'delete',
      ));

      // Reload calendar
      add(LoadCalendarEvents(DateTime.now()));
    } catch (e) {
      emit(CalendarOperationError(
        error: 'Failed to delete event: $e',
        operation: 'delete',
        eventId: event.eventId,
      ));
    }
  }

  /// Select a specific date
  Future<void> _onSelectCalendarDate(
    SelectCalendarDate event,
    Emitter<CalendarState> emit,
  ) async {
    if (state is! CalendarLoaded) return;

    final currentState = state as CalendarLoaded;
    emit(currentState.copyWith(selectedDate: event.date));
  }

  /// Change calendar view mode
  Future<void> _onChangeCalendarView(
    ChangeCalendarView event,
    Emitter<CalendarState> emit,
  ) async {
    if (state is! CalendarLoaded) return;

    final currentState = state as CalendarLoaded;
    emit(currentState.copyWith(viewMode: event.viewMode));
  }

  /// Navigate to previous period
  Future<void> _onNavigateToPreviousPeriod(
    NavigateToPreviousPeriod event,
    Emitter<CalendarState> emit,
  ) async {
    if (state is! CalendarLoaded) return;

    final currentState = state as CalendarLoaded;
    DateTime newDate;

    switch (currentState.viewMode) {
      case CalendarViewMode.month:
        newDate = DateTime(
          currentState.currentDate.year,
          currentState.currentDate.month - 1,
          1,
        );
        break;
      case CalendarViewMode.week:
        newDate = currentState.currentDate.subtract(const Duration(days: 7));
        break;
      case CalendarViewMode.day:
        newDate = currentState.currentDate.subtract(const Duration(days: 1));
        break;
      case CalendarViewMode.agenda:
        newDate = currentState.currentDate.subtract(const Duration(days: 30));
        break;
    }

    emit(currentState.copyWith(currentDate: newDate));
    add(LoadCalendarEvents(newDate));
  }

  /// Navigate to next period
  Future<void> _onNavigateToNextPeriod(
    NavigateToNextPeriod event,
    Emitter<CalendarState> emit,
  ) async {
    if (state is! CalendarLoaded) return;

    final currentState = state as CalendarLoaded;
    DateTime newDate;

    switch (currentState.viewMode) {
      case CalendarViewMode.month:
        newDate = DateTime(
          currentState.currentDate.year,
          currentState.currentDate.month + 1,
          1,
        );
        break;
      case CalendarViewMode.week:
        newDate = currentState.currentDate.add(const Duration(days: 7));
        break;
      case CalendarViewMode.day:
        newDate = currentState.currentDate.add(const Duration(days: 1));
        break;
      case CalendarViewMode.agenda:
        newDate = currentState.currentDate.add(const Duration(days: 30));
        break;
    }

    emit(currentState.copyWith(currentDate: newDate));
    add(LoadCalendarEvents(newDate));
  }

  /// Navigate to today
  Future<void> _onNavigateToToday(
    NavigateToToday event,
    Emitter<CalendarState> emit,
  ) async {
    if (state is! CalendarLoaded) return;

    final currentState = state as CalendarLoaded;
    final today = DateTime.now();
    
    emit(currentState.copyWith(
      currentDate: today,
      selectedDate: today,
    ));
    
    add(LoadCalendarEvents(today));
  }

  /// Search calendar events
  Future<void> _onSearchCalendarEvents(
    SearchCalendarEvents event,
    Emitter<CalendarState> emit,
  ) async {
    if (state is! CalendarLoaded) return;

    final currentState = state as CalendarLoaded;
    final filteredEvents = _filterEvents(
      currentState.events,
      searchQuery: event.query,
      selectedEventType: currentState.selectedEventType,
    );

    final eventsByDate = _groupEventsByDate(filteredEvents);

    emit(currentState.copyWith(
      filteredEvents: filteredEvents,
      searchQuery: event.query,
      eventsByDate: eventsByDate,
    ));
  }

  /// Filter events by type
  Future<void> _onFilterEventsByType(
    FilterEventsByType event,
    Emitter<CalendarState> emit,
  ) async {
    if (state is! CalendarLoaded) return;

    final currentState = state as CalendarLoaded;
    final filteredEvents = _filterEvents(
      currentState.events,
      searchQuery: currentState.searchQuery,
      selectedEventType: event.eventType,
    );

    final eventsByDate = _groupEventsByDate(filteredEvents);

    emit(currentState.copyWith(
      filteredEvents: filteredEvents,
      selectedEventType: event.eventType,
      eventsByDate: eventsByDate,
    ));
  }

  /// Clear calendar filters
  Future<void> _onClearCalendarFilters(
    ClearCalendarFilters event,
    Emitter<CalendarState> emit,
  ) async {
    if (state is! CalendarLoaded) return;

    final currentState = state as CalendarLoaded;
    final eventsByDate = _groupEventsByDate(currentState.events);

    emit(currentState.copyWith(
      filteredEvents: currentState.events,
      clearSearchQuery: true,
      clearSelectedEventType: true,
      eventsByDate: eventsByDate,
    ));
  }

  /// Sync calendar events with server
  Future<void> _onSyncCalendarEvents(
    SyncCalendarEvents event,
    Emitter<CalendarState> emit,
  ) async {
    emit(const CalendarSyncInProgress());

    try {
      // TODO: Implement actual sync when backend is available
      // For now, just simulate success
      emit(CalendarSyncSuccess(
        syncedCount: _events.length,
        message: 'Calendar synchronized successfully',
      ));

      // Reload calendar after sync
      add(LoadCalendarEvents(DateTime.now()));
    } catch (e) {
      emit(CalendarSyncError('Failed to sync calendar: $e'));
    }
  }

  /// Export calendar events
  Future<void> _onExportCalendarEvents(
    ExportCalendarEvents event,
    Emitter<CalendarState> emit,
  ) async {
    emit(const CalendarExportInProgress());

    try {
      List<CalendarEventModel> eventsToExport = _events;

      // Filter by date range if specified
      if (event.startDate != null || event.endDate != null) {
        eventsToExport = _events.where((e) {
          if (event.startDate != null && e.startTime.isBefore(event.startDate!)) {
            return false;
          }
          if (event.endDate != null && e.startTime.isAfter(event.endDate!)) {
            return false;
          }
          return true;
        }).toList();
      }

      // TODO: Implement actual export functionality
      // For now, just simulate success
      emit(CalendarExportSuccess(
        filePath: '/path/to/exported/calendar.${event.format}',
        exportedCount: eventsToExport.length,
        format: event.format,
      ));
    } catch (e) {
      emit(CalendarExportError('Failed to export calendar: $e'));
    }
  }

  /// Helper method to filter events
  List<CalendarEventModel> _filterEvents(
    List<CalendarEventModel> events, {
    String? searchQuery,
    String? selectedEventType,
  }) {
    var filteredEvents = List<CalendarEventModel>.from(events);

    // Apply search filter
    if (searchQuery != null && searchQuery.isNotEmpty) {
      filteredEvents = filteredEvents.where((event) {
        final query = searchQuery.toLowerCase();
        return event.title.toLowerCase().contains(query) ||
               event.description.toLowerCase().contains(query) ||
               (event.location?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Apply type filter
    if (selectedEventType != null && selectedEventType.isNotEmpty) {
      filteredEvents = filteredEvents.where((event) {
        return event.type == selectedEventType;
      }).toList();
    }

    return filteredEvents;
  }

  /// Helper method to group events by date
  Map<DateTime, List<CalendarEventModel>> _groupEventsByDate(
    List<CalendarEventModel> events,
  ) {
    final Map<DateTime, List<CalendarEventModel>> eventsByDate = {};

    for (final event in events) {
      final dateKey = DateTime(
        event.startTime.year,
        event.startTime.month,
        event.startTime.day,
      );

      if (eventsByDate[dateKey] == null) {
        eventsByDate[dateKey] = [];
      }
      eventsByDate[dateKey]!.add(event);
    }

    // Sort events within each date
    for (final dateEvents in eventsByDate.values) {
      dateEvents.sort((a, b) => a.startTime.compareTo(b.startTime));
    }

    return eventsByDate;
  }

  /// Load mock events for testing
  void _loadMockEvents() {
    final now = DateTime.now();

    _events.clear();
    _events.addAll([
      CalendarEventModel(
        id: '1',
        title: 'Team Meeting',
        description: 'Weekly team sync meeting',
        startTime: now.add(const Duration(hours: 2)),
        endTime: now.add(const Duration(hours: 3)),
        type: 'meeting',
        location: 'Conference Room A',
        attendees: ['<EMAIL>', '<EMAIL>'],
        createdAt: now,
        updatedAt: now,
      ),
      CalendarEventModel(
        id: '2',
        title: 'Doctor Appointment',
        description: 'Annual checkup',
        startTime: now.add(const Duration(days: 1, hours: 10)),
        endTime: now.add(const Duration(days: 1, hours: 11)),
        type: 'appointment',
        location: 'Medical Center',
        attendees: [],
        createdAt: now,
        updatedAt: now,
      ),
      CalendarEventModel(
        id: '3',
        title: 'Project Deadline',
        description: 'Submit final project deliverables',
        startTime: now.add(const Duration(days: 3)),
        endTime: now.add(const Duration(days: 3, hours: 1)),
        type: 'reminder',
        attendees: [],
        createdAt: now,
        updatedAt: now,
      ),
    ]);
  }
}
