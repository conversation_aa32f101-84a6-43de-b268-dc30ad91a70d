import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Centralized error handling utility for the app
class ErrorHandler {
  /// Handle Firebase Auth errors with user-friendly messages
  static String handleFirebaseAuthError(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No account found with this email address.';
      case 'wrong-password':
        return 'Incorrect password. Please try again.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak. Please use at least 6 characters.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'user-disabled':
        return 'This account has been disabled. Please contact support.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'operation-not-allowed':
        return 'This sign-in method is not enabled. Please contact support.';
      case 'account-exists-with-different-credential':
        return 'An account already exists with a different sign-in method.';
      case 'invalid-credential':
        return 'Invalid credentials. Please try again.';
      case 'missing-google-auth-token':
        return 'Google Sign-In failed. Please check your Google Play Services.';
      case 'google-signin-failed':
        return 'Google Sign-In failed. Please try again or use email/password.';
      default:
        return e.message ?? 'Authentication failed. Please try again.';
    }
  }

  /// Handle general exceptions with user-friendly messages
  static String handleGeneralError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // Handle pigeon-related errors
    if (errorString.contains('pigeon') || 
        errorString.contains('methodchannel') ||
        errorString.contains('platformexception')) {
      return 'A communication error occurred. Please restart the app and try again.';
    }
    
    // Handle network errors
    if (errorString.contains('network') || 
        errorString.contains('connection') ||
        errorString.contains('timeout')) {
      return 'Network error. Please check your internet connection.';
    }
    
    // Handle backend token exchange errors
    if (errorString.contains('failed to exchange firebase token') ||
        errorString.contains('backend') ||
        errorString.contains('api')) {
      return 'Service temporarily unavailable. Your account was created successfully.';
    }
    
    // Handle Firebase internal errors
    if (errorString.contains('firebase') && 
        (errorString.contains('internal') || errorString.contains('unknown'))) {
      return 'Authentication service error. Please try again.';
    }
    
    // Default fallback
    if (kDebugMode) {
      return 'Error: ${error.toString()}';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Check if an error is recoverable (user can retry)
  static bool isRecoverableError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // Non-recoverable errors
    if (errorString.contains('user-disabled') ||
        errorString.contains('operation-not-allowed') ||
        errorString.contains('invalid-email')) {
      return false;
    }
    
    // Most errors are recoverable
    return true;
  }

  /// Get appropriate retry message for recoverable errors
  static String getRetryMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Please check your internet connection and try again.';
    }
    
    if (errorString.contains('google')) {
      return 'Please try Google Sign-In again or use email/password.';
    }
    
    if (errorString.contains('too-many-requests')) {
      return 'Please wait a few minutes before trying again.';
    }
    
    return 'Please try again in a moment.';
  }
}
