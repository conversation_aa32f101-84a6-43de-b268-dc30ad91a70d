import 'package:flutter/foundation.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import '../services/data_service.dart' as data_service;
import '../services/contact_permission_service.dart';

/// Service for interacting with device's native contacts
class DeviceContactsService {
  static final DeviceContactsService _instance = DeviceContactsService._internal();
  factory DeviceContactsService() => _instance;
  DeviceContactsService._internal();

  final ContactPermissionService _permissionService = ContactPermissionService();

  /// Save contact to device's native contacts app
  Future<bool> saveContactToDevice(data_service.Contact contact) async {
    try {
      // Check permissions first
      final hasPermission = await _permissionService.canAccessContacts();
      if (!hasPermission) {
        debugPrint('No contacts permission granted');
        return false;
      }

      // Create Flutter contact object
      final deviceContact = Contact();
      
      // Set name with custom naming convention
      final meetingLocation = contact.location.isNotEmpty 
          ? contact.location 
          : 'where our paths crossed today';
      
      deviceContact.name = Name(
        first: contact.name,
        last: '($meetingLocation)',
      );
      
      deviceContact.displayName = '${contact.name} ($meetingLocation)';

      // Add phone number
      if (contact.phone.isNotEmpty) {
        deviceContact.phones = [
          Phone(contact.phone, label: PhoneLabel.mobile),
        ];
      }

      // Add email if available
      if (contact.email.isNotEmpty) {
        deviceContact.emails = [
          Email(contact.email, label: EmailLabel.home),
        ];
      }

      // Add organization/notes
      final notes = <String>[];
      if (contact.location.isNotEmpty) {
        notes.add('Met at: ${contact.location}');
      }
      if (contact.socialPlatform.isNotEmpty && contact.socialUsername.isNotEmpty) {
        notes.add('${contact.socialPlatform}: ${contact.socialUsername}');
      }
      if (contact.notes.isNotEmpty) {
        notes.add('Notes: ${contact.notes}');
      }
      
      if (notes.isNotEmpty) {
        deviceContact.notes = [
          Note(notes.join('\n')),
        ];
      }

      // Add social media as website
      if (contact.socialPlatform.isNotEmpty && contact.socialUsername.isNotEmpty) {
        final socialUrl = _generateSocialMediaUrl(contact.socialPlatform, contact.socialUsername);
        if (socialUrl.isNotEmpty) {
          deviceContact.websites = [
            Website(socialUrl, label: WebsiteLabel.other),
          ];
        }
      }

      // Save to device
      await deviceContact.insert();
      
      debugPrint('Contact saved to device: ${contact.name}');
      return true;
    } catch (e) {
      debugPrint('Error saving contact to device: $e');
      return false;
    }
  }

  /// Generate social media URL
  String _generateSocialMediaUrl(String platform, String username) {
    switch (platform.toLowerCase()) {
      case 'twitter':
        return 'https://twitter.com/$username';
      case 'instagram':
        return 'https://instagram.com/$username';
      case 'linkedin':
        return 'https://linkedin.com/in/$username';
      case 'whatsapp':
        return 'https://wa.me/$username';
      default:
        return '';
    }
  }

  /// Get all contacts from device
  Future<List<data_service.Contact>> getDeviceContacts() async {
    try {
      final hasPermission = await _permissionService.canAccessContacts();
      if (!hasPermission) {
        debugPrint('No contacts permission granted');
        return [];
      }

      final contacts = await FlutterContacts.getContacts(
        withProperties: true,
        withPhoto: false,
      );

      // Convert to our Contact model
      return contacts.map((contact) => _convertToAppContact(contact)).toList();
    } catch (e) {
      debugPrint('Error getting device contacts: $e');
      return [];
    }
  }

  /// Search device contacts by name
  Future<List<data_service.Contact>> searchDeviceContacts(String query) async {
    try {
      final hasPermission = await _permissionService.canAccessContacts();
      if (!hasPermission) {
        debugPrint('No contacts permission granted');
        return [];
      }

      final contacts = await FlutterContacts.getContacts(
        withProperties: true,
        withPhoto: false,
      );
      
      final filteredContacts = contacts.where((contact) {
        final name = contact.displayName.toLowerCase();
        return name.contains(query.toLowerCase());
      }).toList();

      // Convert to our Contact model
      return filteredContacts.map((contact) => _convertToAppContact(contact)).toList();
    } catch (e) {
      debugPrint('Error searching device contacts: $e');
      return [];
    }
  }

  /// Check if contact already exists in device
  Future<bool> contactExistsInDevice(data_service.Contact contact) async {
    try {
      final hasPermission = await _permissionService.canAccessContacts();
      if (!hasPermission) {
        return false;
      }

      final deviceContacts = await FlutterContacts.getContacts(
        withProperties: true,
        withPhoto: false,
      );
      
      // Check for exact name and phone match
      for (final deviceContact in deviceContacts) {
        final nameMatch = deviceContact.displayName.toLowerCase().contains(contact.name.toLowerCase());
        final phoneMatch = deviceContact.phones.any((phone) => 
          phone.number.replaceAll(RegExp(r'[^\d]'), '') == 
          contact.phone.replaceAll(RegExp(r'[^\d]'), '')
        );
        
        if (nameMatch && phoneMatch) {
          return true;
        }
      }
      
      return false;
    } catch (e) {
      debugPrint('Error checking if contact exists: $e');
      return false;
    }
  }

  /// Import contact from device to app
  Future<data_service.Contact?> importContactFromDevice(Contact deviceContact) async {
    try {
      final name = deviceContact.displayName;
      final phone = deviceContact.phones.isNotEmpty 
          ? deviceContact.phones.first.number 
          : '';
      final email = deviceContact.emails.isNotEmpty 
          ? deviceContact.emails.first.address 
          : '';
      
      // Extract notes
      String notes = '';
      String location = '';
      String socialPlatform = '';
      String socialUsername = '';
      
      if (deviceContact.notes.isNotEmpty) {
        final noteText = deviceContact.notes.first.note;
        final lines = noteText.split('\n');
        
        for (final line in lines) {
          if (line.startsWith('Met at:')) {
            location = line.substring(7).trim();
          } else if (line.contains(':') && _isSocialPlatform(line.split(':')[0].trim())) {
            socialPlatform = line.split(':')[0].trim();
            socialUsername = line.split(':')[1].trim();
          } else if (line.startsWith('Notes:')) {
            notes = line.substring(6).trim();
          }
        }
      }

      // Prepare social media data
      final socialMedia = <String, String>{};
      if (socialPlatform.isNotEmpty && socialUsername.isNotEmpty) {
        socialMedia[socialPlatform.toLowerCase()] = socialUsername;
      }

      final contact = data_service.Contact(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        phone: phone,
        email: email,
        location: location,
        metAt: location, // Use location as "met at"
        socialMedia: socialMedia,
        memoryPrompt: notes,
        createdAt: DateTime.now(),
      );

      return contact;
    } catch (e) {
      debugPrint('Error importing contact from device: $e');
      return null;
    }
  }

  /// Check if string is a social platform
  bool _isSocialPlatform(String platform) {
    final platforms = ['twitter', 'instagram', 'linkedin', 'whatsapp'];
    return platforms.contains(platform.toLowerCase());
  }

  /// Convert FlutterContact to our app's Contact model
  data_service.Contact _convertToAppContact(Contact flutterContact) {
    final name = flutterContact.displayName;
    final phone = flutterContact.phones.isNotEmpty
        ? flutterContact.phones.first.number
        : '';
    final email = flutterContact.emails.isNotEmpty
        ? flutterContact.emails.first.address
        : '';

    // Extract notes and parse for our fields
    String notes = '';
    String location = '';
    String socialPlatform = '';
    String socialUsername = '';

    if (flutterContact.notes.isNotEmpty) {
      final noteText = flutterContact.notes.first.note;
      final lines = noteText.split('\n');

      for (final line in lines) {
        if (line.startsWith('Met at:')) {
          location = line.substring(7).trim();
        } else if (line.contains(':') && _isSocialPlatform(line.split(':')[0].trim())) {
          socialPlatform = line.split(':')[0].trim();
          socialUsername = line.split(':')[1].trim();
        } else if (line.startsWith('Notes:')) {
          notes = line.substring(6).trim();
        }
      }
    }

    // Prepare social media data
    final socialMediaMap = <String, String>{};
    if (socialPlatform.isNotEmpty && socialUsername.isNotEmpty) {
      socialMediaMap[socialPlatform.toLowerCase()] = socialUsername;
    }

    return data_service.Contact(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      phone: phone,
      email: email,
      location: location,
      metAt: location, // Use location as "met at"
      socialMedia: socialMediaMap,
      memoryPrompt: notes,
      createdAt: DateTime.now(),
    );
  }

  /// Get contact statistics
  Future<Map<String, int>> getContactStats() async {
    try {
      final hasPermission = await _permissionService.canAccessContacts();
      if (!hasPermission) {
        return {'total': 0, 'withPhone': 0, 'withEmail': 0};
      }

      final contacts = await FlutterContacts.getContacts(
        withProperties: true,
        withPhoto: false,
      );
      
      int withPhone = 0;
      int withEmail = 0;
      
      for (final contact in contacts) {
        if (contact.phones.isNotEmpty) withPhone++;
        if (contact.emails.isNotEmpty) withEmail++;
      }
      
      return {
        'total': contacts.length,
        'withPhone': withPhone,
        'withEmail': withEmail,
      };
    } catch (e) {
      debugPrint('Error getting contact stats: $e');
      return {'total': 0, 'withPhone': 0, 'withEmail': 0};
    }
  }
}
