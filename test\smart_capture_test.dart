import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:darvis_app/screens/smart_capture/services/smart_capture_api_service.dart';
import 'package:darvis_app/screens/smart_capture/services/content_processing_service.dart';
import 'package:darvis_app/screens/smart_capture/models/captured_content.dart';
import 'package:darvis_app/services/api_service.dart';
import 'package:darvis_app/services/share_intent_service.dart';

// Generate mocks
@GenerateMocks([ApiService])
import 'smart_capture_test.mocks.dart';

void main() {
  group('Smart Capture Tests', () {
    late MockApiService mockApiService;
    late SmartCaptureApiService smartCaptureService;
    late ShareIntentService shareIntentService;

    setUp(() {
      mockApiService = MockApiService();
      smartCaptureService = SmartCaptureApiService(mockApiService);
      shareIntentService = ShareIntentService();
    });

    group('Content Processing Service', () {
      test('should process URL successfully', () async {
        // Arrange
        const testUrl = 'https://example.com';
        final mockResponse = {
          'id': '123',
          'url': testUrl,
          'title': 'Test Article',
          'summary': 'This is a test article',
          'content_type': 'link',
          'processing_status': 'completed',
          'created_at': DateTime.now().toIso8601String(),
          'tags': ['test'],
        };

        when(mockApiService.captureContent(url: testUrl))
            .thenAnswer((_) async => mockResponse);
        when(mockApiService.getProcessingStatus(any))
            .thenAnswer((_) async => {
              'content_id': '123',
              'status': 'completed',
              'progress': 100,
            });

        // Act
        final result = await smartCaptureService.processUrl(testUrl);

        // Assert
        expect(result.url, equals(testUrl));
        expect(result.title, equals('Test Article'));
        expect(result.summary, equals('This is a test article'));
        expect(result.status, equals(ProcessingStatus.completed));
        verify(mockApiService.captureContent(url: testUrl)).called(1);
      });

      test('should handle URL processing errors gracefully', () async {
        // Arrange
        const testUrl = 'https://invalid-url.com';
        when(mockApiService.captureContent(url: testUrl))
            .thenThrow(Exception('Network error'));

        // Act & Assert
        expect(
          () => smartCaptureService.processUrl(testUrl),
          throwsException,
        );
      });

      test('should load existing content', () async {
        // Arrange
        final mockResponse = {
          'items': [
            {
              'id': '1',
              'url': 'https://example1.com',
              'title': 'Article 1',
              'summary': 'Summary 1',
              'content_type': 'link',
              'processing_status': 'completed',
              'created_at': DateTime.now().toIso8601String(),
            },
            {
              'id': '2',
              'url': 'https://example2.com',
              'title': 'Article 2',
              'summary': 'Summary 2',
              'content_type': 'link',
              'processing_status': 'completed',
              'created_at': DateTime.now().toIso8601String(),
            },
          ],
          'total': 2,
        };

        when(mockApiService.getCapturedContent())
            .thenAnswer((_) async => mockResponse);

        // Act
        final result = await smartCaptureService.loadExistingContent();

        // Assert
        expect(result.length, equals(2));
        expect(result[0].title, equals('Article 1'));
        expect(result[1].title, equals('Article 2'));
        verify(mockApiService.getCapturedContent()).called(1);
      });
    });

    group('Share Intent Service', () {
      test('should validate URLs correctly', () {
        // Test valid URLs
        expect(shareIntentService.isUrl('https://example.com'), isTrue);
        expect(shareIntentService.isUrl('http://test.org'), isTrue);
        expect(shareIntentService.isUrl('https://www.google.com/search?q=test'), isTrue);

        // Test invalid URLs
        expect(shareIntentService.isUrl('not a url'), isFalse);
        expect(shareIntentService.isUrl('just text'), isFalse);
        expect(shareIntentService.isUrl(''), isFalse);
      });

      test('should extract URLs from text', () {
        const textWithUrls = 'Check out this article: https://example.com and also http://test.org';
        final urls = shareIntentService.extractUrls(textWithUrls);
        
        expect(urls.length, equals(2));
        expect(urls[0], equals('https://example.com'));
        expect(urls[1], equals('http://test.org'));
      });

      test('should clean shared text properly', () {
        const messyText = '  This   is   messy   text  ';
        final cleanedText = shareIntentService.cleanSharedText(messyText);
        
        expect(cleanedText, equals('This is messy text'));
      });

      test('should validate shared content', () {
        // Valid content
        expect(shareIntentService.isValidSharedContent('https://example.com'), isTrue);
        expect(shareIntentService.isValidSharedContent('This is meaningful text'), isTrue);

        // Invalid content
        expect(shareIntentService.isValidSharedContent(''), isFalse);
        expect(shareIntentService.isValidSharedContent('   '), isFalse);
        expect(shareIntentService.isValidSharedContent('ab'), isFalse); // Too short
      });

      test('should determine content type correctly', () {
        expect(shareIntentService.getContentType('https://example.com'), equals('url'));
        expect(shareIntentService.getContentType('Text with https://example.com link'), equals('text_with_urls'));
        expect(shareIntentService.getContentType('Just plain text'), equals('text'));
      });
    });

    group('Mock Content Service', () {
      late MockContentService mockService;

      setUp(() {
        mockService = MockContentService();
      });

      test('should process URL with mock data', () async {
        const testUrl = 'https://youtube.com/watch?v=test';
        final result = await mockService.processUrl(testUrl);

        expect(result.url, equals(testUrl));
        expect(result.title, isNotEmpty);
        expect(result.summary, isNotEmpty);
        expect(result.status, equals(ProcessingStatus.completed));
        expect(result.type, equals(ContentType.link));
      });

      test('should load existing mock content', () async {
        final result = await mockService.loadExistingContent();

        expect(result.isNotEmpty, isTrue);
        expect(result.every((content) => content.status == ProcessingStatus.completed), isTrue);
      });
    });

    group('CapturedContent Model', () {
      test('should create from API response', () {
        final apiResponse = {
          'id': '123',
          'url': 'https://example.com',
          'title': 'Test Title',
          'summary': 'Test Summary',
          'content_type': 'link',
          'processing_status': 'completed',
          'created_at': '2024-01-01T00:00:00Z',
          'tags': ['test', 'example'],
          'favicon_url': 'https://example.com/favicon.ico',
        };

        final content = CapturedContent.fromApi(apiResponse);

        expect(content.id, equals('123'));
        expect(content.url, equals('https://example.com'));
        expect(content.title, equals('Test Title'));
        expect(content.summary, equals('Test Summary'));
        expect(content.type, equals(ContentType.link));
        expect(content.status, equals(ProcessingStatus.completed));
        expect(content.faviconUrl, equals('https://example.com/favicon.ico'));
      });

      test('should handle missing fields in API response', () {
        final apiResponse = {
          'id': '123',
          'url': 'https://example.com',
          'title': 'Test Title',
        };

        final content = CapturedContent.fromApi(apiResponse);

        expect(content.id, equals('123'));
        expect(content.url, equals('https://example.com'));
        expect(content.title, equals('Test Title'));
        expect(content.summary, equals('No summary available'));
        expect(content.type, equals(ContentType.link));
        expect(content.status, equals(ProcessingStatus.processing));
      });
    });
  });
}
