import 'dart:io';
import 'dart:math';
import '../models/captured_content.dart';

abstract class ContentProcessingService {
  Future<CapturedContent> processUrl(String url);
  Future<CapturedContent> processImage(File image);
  Future<List<CapturedContent>> loadExistingContent();
}

class MockContentService implements ContentProcessingService {
  final Random _random = Random();
  
  // Mock data for realistic testing
  final List<Map<String, dynamic>> _mockUrlData = [
    {
      'domain': 'youtube.com',
      'titleTemplate': 'How to Build Amazing Flutter Apps',
      'summaryTemplate': 'Video tutorial covering advanced Flutter development techniques, state management, and UI design patterns.',
      'tag': 'Tutorial',
      'favicon': '🎥',
    },
    {
      'domain': 'medium.com',
      'titleTemplate': 'The Future of Mobile Development',
      'summaryTemplate': 'Article discussing emerging trends in mobile app development, including cross-platform frameworks and AI integration.',
      'tag': 'Article',
      'favicon': '📝',
    },
    {
      'domain': 'github.com',
      'titleTemplate': 'Flutter Awesome UI Components',
      'summaryTemplate': 'Code repository containing reusable Flutter widgets and components for building beautiful user interfaces.',
      'tag': 'Code',
      'favicon': '💻',
    },
    {
      'domain': 'stackoverflow.com',
      'titleTemplate': 'Solving Complex Flutter State Management',
      'summaryTemplate': 'Q&A discussion about implementing efficient state management patterns in large Flutter applications.',
      'tag': 'Help',
      'favicon': '❓',
    },
    {
      'domain': 'flutter.dev',
      'titleTemplate': 'Flutter 3.0 New Features Guide',
      'summaryTemplate': 'Official documentation covering the latest features and improvements in Flutter 3.0 release.',
      'tag': 'Documentation',
      'favicon': '📚',
    },
    {
      'domain': 'techcrunch.com',
      'titleTemplate': 'AI Revolution in Mobile Apps',
      'summaryTemplate': 'News article exploring how artificial intelligence is transforming mobile application development and user experiences.',
      'tag': 'News',
      'favicon': '📰',
    },
  ];

  final List<CapturedContent> _existingContent = [
    CapturedContent(
      id: '1',
      url: 'https://flutter.dev/docs/development/ui/widgets',
      title: 'Flutter Widget Catalog',
      summary: 'Comprehensive guide to Flutter widgets including layout, input, and styling components for building beautiful UIs.',
      tag: 'Documentation',
      type: ContentType.link,
      status: ProcessingStatus.completed,
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      faviconUrl: '📚',
    ),
    CapturedContent(
      id: '2',
      url: 'https://www.youtube.com/watch?v=example',
      title: 'Advanced Flutter Animation Techniques',
      summary: 'Video tutorial demonstrating complex animation patterns, custom transitions, and performance optimization tips.',
      tag: 'Tutorial',
      type: ContentType.link,
      status: ProcessingStatus.completed,
      timestamp: DateTime.now().subtract(const Duration(hours: 5)),
      faviconUrl: '🎥',
    ),
    CapturedContent(
      id: '3',
      url: 'https://medium.com/@developer/flutter-tips',
      title: '10 Flutter Tips Every Developer Should Know',
      summary: 'Article sharing practical tips and best practices for Flutter development, covering performance and code organization.',
      tag: 'Article',
      type: ContentType.link,
      status: ProcessingStatus.completed,
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
      faviconUrl: '📝',
    ),
    CapturedContent(
      id: '4',
      url: 'https://github.com/flutter/flutter',
      title: 'Flutter Framework Repository',
      summary: 'Official Flutter framework source code repository with latest updates, bug fixes, and community contributions.',
      tag: 'Code',
      type: ContentType.link,
      status: ProcessingStatus.completed,
      timestamp: DateTime.now().subtract(const Duration(days: 2)),
      faviconUrl: '💻',
    ),
    CapturedContent(
      id: '5',
      url: 'https://stackoverflow.com/questions/flutter-state',
      title: 'Flutter State Management Best Practices',
      summary: 'Community discussion about choosing the right state management solution for different Flutter app architectures.',
      tag: 'Help',
      type: ContentType.link,
      status: ProcessingStatus.completed,
      timestamp: DateTime.now().subtract(const Duration(days: 3)),
      faviconUrl: '❓',
    ),
  ];

  @override
  Future<CapturedContent> processUrl(String url) async {
    // Simulate processing delay
    await Future.delayed(Duration(milliseconds: 2000 + _random.nextInt(3000)));
    
    // Simulate 10% failure rate
    if (_random.nextDouble() < 0.1) {
      throw Exception('Failed to process content');
    }
    
    // Generate realistic content based on URL
    final mockData = _getMockDataForUrl(url);
    
    return CapturedContent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      url: url,
      title: mockData['title'] as String,
      summary: mockData['summary'] as String,
      tag: mockData['tag'] as String,
      type: ContentType.link,
      status: ProcessingStatus.completed,
      timestamp: DateTime.now(),
      faviconUrl: mockData['favicon'] as String?,
    );
  }

  @override
  Future<CapturedContent> processImage(File image) async {
    // Simulate processing delay
    await Future.delayed(Duration(milliseconds: 1500 + _random.nextInt(2000)));
    
    // Simulate 5% failure rate for images
    if (_random.nextDouble() < 0.05) {
      throw Exception('Failed to process image');
    }
    
    return CapturedContent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      url: image.path,
      title: 'Captured Image',
      summary: 'Image processed and analyzed by Darvis AI for content extraction and organization.',
      tag: 'Image',
      type: ContentType.image,
      status: ProcessingStatus.completed,
      timestamp: DateTime.now(),
      thumbnailUrl: image.path,
    );
  }

  @override
  Future<List<CapturedContent>> loadExistingContent() async {
    // Simulate loading delay
    await Future.delayed(const Duration(milliseconds: 500));
    return List.from(_existingContent);
  }

  Map<String, dynamic> _getMockDataForUrl(String url) {
    // Try to match URL to mock data
    for (final mockData in _mockUrlData) {
      if (url.toLowerCase().contains(mockData['domain'] as String)) {
        return {
          'title': mockData['titleTemplate'],
          'summary': mockData['summaryTemplate'],
          'tag': mockData['tag'],
          'favicon': mockData['favicon'],
        };
      }
    }
    
    // Default fallback for unknown URLs
    return {
      'title': 'Interesting Content Found',
      'summary': 'Darvis has processed this content and extracted key information for easy reference and organization.',
      'tag': 'General',
      'favicon': '🔗',
    };
  }
}
