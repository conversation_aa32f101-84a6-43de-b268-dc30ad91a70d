import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import '../../utils/design_tokens.dart';

class AboutDarvisScreen extends StatefulWidget {
  const AboutDarvisScreen({super.key});

  @override
  State<AboutDarvisScreen> createState() => _AboutDarvisScreenState();
}

class _AboutDarvisScreenState extends State<AboutDarvisScreen>
    with TickerProviderStateMixin {
  
  late AnimationController _pageAnimationController;
  late Animation<Offset> _pageSlideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 350),
      vsync: this,
    );
    
    _pageSlideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _pageAnimationController,
      curve: Curves.easeOut,
    ));
    
    // Delay animation start until after page route transition completes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _pageAnimationController.forward();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: SafeArea(
        child: SlideTransition(
          position: _pageSlideAnimation,
          child: CustomScrollView(
            physics: const ClampingScrollPhysics(),
            slivers: [
              _buildHeader(),
              _buildAppInfoSection(),
              _buildVersionSection(),
              _buildTeamSection(),
              _buildLegalSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return SliverToBoxAdapter(
      child: Container(
        height: 80,
        padding: const EdgeInsets.fromLTRB(
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingLg,
          DesignTokens.spacingSm,
        ),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                Icons.arrow_back,
                color: DesignTokens.iconPrimary,
              ),
            ),
            Expanded(
              child: Center(
                child: ShaderMask(
                  shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
                  child: Text(
                    'About Drix',
                    style: DesignTokens.therapySessionTitleStyle.copyWith(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 48), // Balance the back button
          ],
        ),
      ),
    );
  }

  Widget _buildAppInfoSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Container(
          padding: const EdgeInsets.all(DesignTokens.spacingLg),
          decoration: BoxDecoration(
            color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.08),
              width: 1,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Column(
                children: [
                  // Darvis logo
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: DesignTokens.primaryInteractiveBlue,
                        width: 3,
                      ),
                    ),
                    child: ClipOval(
                      child: Container(
                        color: DesignTokens.backgroundCard,
                        child: Center(
                          child: Image.asset(
                            'assets/images/darvis_main.PNG',
                            width: 80,
                            height: 80,
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: DesignTokens.spacingLg),
                  
                  // App name
                  ShaderMask(
                    shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
                    child: Text(
                      'DARVIS',
                      style: DesignTokens.therapySessionTitleStyle.copyWith(
                        color: Colors.white,
                        fontSize: 32,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: DesignTokens.spacingSm),
                  
                  // Tagline
                  Text(
                    'Your AI-Powered Mental Wellness Companion',
                    style: DesignTokens.bodyStyle.copyWith(
                      color: DesignTokens.textSecondary,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: DesignTokens.spacingLg),
                  
                  // Description
                  Text(
                    'Drix is a revolutionary AI assistant designed to be your second brain. It helps you capture, organize, and connect your thoughts, tasks, and knowledge in a seamless, conversational interface. Our mission is to empower you to think clearer, remember better, and achieve more.',
                    style: DesignTokens.bodyStyle.copyWith(
                      color: DesignTokens.textPrimary,
                      fontSize: 14,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVersionSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Version Information',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            
            _buildInfoItem('Version', '1.0.0'),
            _buildInfoItem('Build', '2024.08.12'),
            _buildInfoItem('Platform', 'Flutter 3.24.0'),
            _buildInfoItem('Last Updated', 'August 12, 2024'),
          ],
        ),
      ),
    );
  }

  Widget _buildTeamSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Development Team',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            
            _buildInfoItem('Lead Developer', 'David'),
            _buildInfoItem('AI Research', 'Darvis Intelligence Team'),
            _buildInfoItem('UI/UX Design', 'Darvis Design Studio'),
            _buildInfoItem('Quality Assurance', 'Darvis QA Team'),
          ],
        ),
      ),
    );
  }

  Widget _buildLegalSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Legal & Support',
              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 18),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            
            _buildActionItem(
              'Privacy Policy',
              'View our privacy policy',
              Icons.privacy_tip_outlined,
              () => _showComingSoon('Privacy Policy'),
            ),
            
            _buildActionItem(
              'Terms of Service',
              'Read our terms of service',
              Icons.description_outlined,
              () => _showComingSoon('Terms of Service'),
            ),
            
            _buildActionItem(
              'Open Source Licenses',
              'View third-party licenses',
              Icons.code,
              () => _showComingSoon('Open Source Licenses'),
            ),
            
            const SizedBox(height: DesignTokens.spacingLg),
            
            // Copyright
            Center(
              child: Text(
                '© 2024 Darvis Intelligence. All rights reserved.',
                style: DesignTokens.bodyStyle.copyWith(
                  color: DesignTokens.textMuted,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            
            const SizedBox(height: DesignTokens.spacingXl),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Container(
      margin: const EdgeInsets.only(bottom: DesignTokens.spacingSm),
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: const EdgeInsets.all(DesignTokens.spacingSm),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  label,
                  style: DesignTokens.bodyStyle.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  value,
                  style: DesignTokens.bodyStyle.copyWith(
                    color: DesignTokens.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionItem(String title, String subtitle, IconData icon, VoidCallback onTap) {
    return Container(
      margin: const EdgeInsets.only(bottom: DesignTokens.spacingSm),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          child: Container(
            padding: const EdgeInsets.all(DesignTokens.spacingMd),
            decoration: BoxDecoration(
              color: DesignTokens.backgroundCard.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.08),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Padding(
                  padding: const EdgeInsets.all(DesignTokens.spacingSm),
                  child: Row(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                        ),
                        child: Icon(
                          icon,
                          color: DesignTokens.primaryInteractiveBlue,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: DesignTokens.spacingMd),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              title,
                              style: DesignTokens.cardTitleStyle.copyWith(fontSize: 16),
                            ),
                            const SizedBox(height: DesignTokens.spacingXs),
                            Text(
                              subtitle,
                              style: DesignTokens.bodyStyle.copyWith(
                                color: DesignTokens.textSecondary,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Icon(
                        Icons.chevron_right,
                        color: DesignTokens.iconSecondary,
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature coming soon!'),
        backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  void dispose() {
    _pageAnimationController.dispose();
    super.dispose();
  }
}
