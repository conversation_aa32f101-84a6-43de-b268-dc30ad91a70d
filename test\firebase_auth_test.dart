import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:google_sign_in/google_sign_in.dart';

import 'package:darvis_app/services/firebase_auth_service.dart';
import 'package:darvis_app/services/api_service.dart';

import 'firebase_auth_test.mocks.dart';

@GenerateMocks([
  FirebaseAuth,
  User,
  UserCredential,
  ApiService,
  FlutterSecureStorage,
  GoogleSignIn,
  GoogleSignInAccount,
  GoogleSignInAuthentication,
])
void main() {
  group('FirebaseAuthService Tests', () {
    late FirebaseAuthService authService;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockApiService mockApiService;
    late MockFlutterSecureStorage mockSecureStorage;
    late MockGoogleSignIn mockGoogleSignIn;
    late MockUser mockUser;
    late MockUserCredential mockUserCredential;

    setUp(() {
      mockFirebaseAuth = MockFirebaseAuth();
      mockApiService = MockApiService();
      mockSecureStorage = MockFlutterSecureStorage();
      mockGoogleSignIn = MockGoogleSignIn();
      mockUser = MockUser();
      mockUserCredential = MockUserCredential();

      authService = FirebaseAuthService(
        apiService: mockApiService,
        secureStorage: mockSecureStorage,
        firebaseAuth: mockFirebaseAuth,
        googleSignIn: mockGoogleSignIn,
      );
    });

    group('Authentication State', () {
      test('should return true when user is authenticated', () {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);

        // Act
        final result = authService.isAuthenticated;

        // Assert
        expect(result, true);
      });

      test('should return false when user is not authenticated', () {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(null);

        // Act
        final result = authService.isAuthenticated;

        // Assert
        expect(result, false);
      });

      test('should return current user', () {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);

        // Act
        final result = authService.currentUser;

        // Assert
        expect(result, mockUser);
      });
    });

    group('Email/Password Authentication', () {
      test('should sign in with email and password successfully', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const idToken = 'mock_id_token';
        
        when(mockFirebaseAuth.signInWithEmailAndPassword(
          email: email,
          password: password,
        )).thenAnswer((_) async => mockUserCredential);
        
        when(mockUserCredential.user).thenReturn(mockUser);
        when(mockUser.getIdToken()).thenAnswer((_) async => idToken);
        
        when(mockApiService.exchangeFirebaseToken(idToken))
            .thenAnswer((_) async => {
              'access_token': 'mock_access_token',
              'refresh_token': 'mock_refresh_token',
            });

        when(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
            .thenAnswer((_) async {});

        // Act
        final result = await authService.signInWithEmailAndPassword(
          email: email,
          password: password,
        );

        // Assert
        expect(result, mockUserCredential);
        verify(mockApiService.exchangeFirebaseToken(idToken)).called(1);
        verify(mockSecureStorage.write(key: 'access_token', value: 'mock_access_token')).called(1);
        verify(mockSecureStorage.write(key: 'refresh_token', value: 'mock_refresh_token')).called(1);
      });

      test('should create user with email and password successfully', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const name = 'Test User';
        const idToken = 'mock_id_token';
        
        when(mockFirebaseAuth.createUserWithEmailAndPassword(
          email: email,
          password: password,
        )).thenAnswer((_) async => mockUserCredential);
        
        when(mockUserCredential.user).thenReturn(mockUser);
        when(mockUser.updateDisplayName(name)).thenAnswer((_) async {});
        when(mockUser.getIdToken()).thenAnswer((_) async => idToken);
        
        when(mockApiService.exchangeFirebaseToken(idToken))
            .thenAnswer((_) async => {
              'access_token': 'mock_access_token',
              'refresh_token': 'mock_refresh_token',
            });

        when(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
            .thenAnswer((_) async {});

        // Act
        final result = await authService.createUserWithEmailAndPassword(
          email: email,
          password: password,
          name: name,
        );

        // Assert
        expect(result, mockUserCredential);
        verify(mockUser.updateDisplayName(name)).called(1);
        verify(mockApiService.exchangeFirebaseToken(idToken)).called(1);
      });
    });

    group('Token Management', () {
      test('should get access token from secure storage', () async {
        // Arrange
        const accessToken = 'mock_access_token';
        when(mockSecureStorage.read(key: 'access_token'))
            .thenAnswer((_) async => accessToken);

        // Act
        final result = await authService.getAccessToken();

        // Assert
        expect(result, accessToken);
        verify(mockSecureStorage.read(key: 'access_token')).called(1);
      });

      test('should refresh access token successfully', () async {
        // Arrange
        const refreshToken = 'mock_refresh_token';
        const newAccessToken = 'new_access_token';
        
        when(mockSecureStorage.read(key: 'refresh_token'))
            .thenAnswer((_) async => refreshToken);
        
        when(mockApiService.refreshToken(refreshToken))
            .thenAnswer((_) async => {
              'access_token': newAccessToken,
              'refresh_token': 'new_refresh_token',
            });

        when(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
            .thenAnswer((_) async {});

        // Act
        await authService.refreshAccessToken();

        // Assert
        verify(mockApiService.refreshToken(refreshToken)).called(1);
        verify(mockSecureStorage.write(key: 'access_token', value: newAccessToken)).called(1);
      });
    });

    group('Sign Out', () {
      test('should sign out successfully', () async {
        // Arrange
        when(mockFirebaseAuth.signOut()).thenAnswer((_) async {});
        when(mockGoogleSignIn.signOut()).thenAnswer((_) async => null);
        when(mockSecureStorage.delete(key: anyNamed('key')))
            .thenAnswer((_) async {});

        // Act
        await authService.signOut();

        // Assert
        verify(mockFirebaseAuth.signOut()).called(1);
        verify(mockGoogleSignIn.signOut()).called(1);
        verify(mockSecureStorage.delete(key: 'access_token')).called(1);
        verify(mockSecureStorage.delete(key: 'refresh_token')).called(1);
      });
    });

    group('Password Reset', () {
      test('should send password reset email successfully', () async {
        // Arrange
        const email = '<EMAIL>';
        when(mockFirebaseAuth.sendPasswordResetEmail(email: email))
            .thenAnswer((_) async {});

        // Act
        await authService.sendPasswordResetEmail(email);

        // Assert
        verify(mockFirebaseAuth.sendPasswordResetEmail(email: email)).called(1);
      });
    });
  });
}
