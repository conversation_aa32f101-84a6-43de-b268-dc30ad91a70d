# UI Branding Audit Results

This report details all user-visible instances of the name "<PERSON><PERSON>" that need to be updated to "Drix" to ensure brand consistency across the application. The audit excludes technical names (packages, files, classes) and focuses only on what the user sees.

## 🔥 HIGH PRIORITY - Primary User Touchpoints:

### Authentication & Welcome Screens:
- **File:** `lib/screens/auth/signup_screen.dart`
  - **Line 155:** `title: 'What Should Darvis Call You',`
  - **Suggested Replacement:** `title: 'What Should Drix Call You',`
  - **Context:** This is a field label on the main sign-up form, a primary interaction for new users.
  - **Priority:** 🔥 HIGH

- **File:** `lib/screens/home/<USER>
  - **Line 453:** `const Text('Welcome to <PERSON><PERSON>!', style: DesignTokens.bodyStyle),`
  - **Suggested Replacement:** `const Text('Welcome to Drix!', style: DesignTokens.bodyStyle),`
  - **Context:** This is a notification text in the slide-out drawer, one of the first messages a user might see.
  - **Priority:** 🔥 HIGH

### Main Navigation & Core Features:
- **File:** `lib/screens/home/<USER>
  - **Line 275:** `title: 'Chat with Drix',`
  - **Note:** The title is already "Drix", but the `onTap` callback prints `'Chat with Drix Tapped'`. The `ButtonReport.md` also lists this as "Chat with Darvis". This is a high-priority action button.
  - **Suggested Replacement:** The `onTap` handler should be updated to reflect the correct name, though this is a logic change, not a UI text change. The visible text is correct.
  - **Context:** This is one of the four main mode selection cards on the home screen.
  - **Priority:** 🔥 HIGH

### App Metadata (Web & PWA):
- **File:** `web/index.html`
  - **Line 26:** `<meta name="apple-mobile-web-app-title" content="darvis_app">`
  - **Suggested Replacement:** `<meta name="apple-mobile-web-app-title" content="Drix">`
  - **Context:** The app's title when added to the home screen on iOS devices.
  - **Priority:** 🔥 HIGH

- **File:** `web/index.html`
  - **Line 32:** `<title>darvis_app</title>`
  - **Suggested Replacement:** `<title>Drix</title>`
  - **Context:** The title of the browser tab when running the app on the web.
  - **Priority:** 🔥 HIGH

- **File:** `web/manifest.json`
  - **Line 2:** `"name": "darvis_app",`
  - **Suggested Replacement:** `"name": "Drix",`
  - **Context:** The full name of the Progressive Web App (PWA) when installed on a device.
  - **Priority:** 🔥 HIGH

- **File:** `web/manifest.json`
  - **Line 3:** `"short_name": "darvis_app",`
  - **Suggested Replacement:** `"short_name": "Drix",`
  - **Context:** The short name of the PWA, often used under the app icon on a home screen.
  - **Priority:** 🔥 HIGH

## ⚡ MEDIUM PRIORITY - Secondary Interfaces:

### Settings Screens:
- **File:** `lib/screens/profile/about_darvis_screen.dart`
  - **Line 11:** `class AboutDarvisScreen extends StatelessWidget {`
  - **Note:** While this is a class name, the screen it represents is "About Darvis". The file and class name should eventually be changed, but for a UI-only audit, the title is the key part.
  - **Line 20:** `title: 'About Darvis',`
  - **Suggested Replacement:** `title: 'About Drix',`
  - **Context:** The main title of the "About" screen.
  - **Priority:** ⚡ MEDIUM

- **File:** `lib/screens/profile/about_darvis_screen.dart`
  - **Line 35:** `text: 'Darvis is a revolutionary AI assistant...',`
  - **Suggested Replacement:** `text: 'Drix is a revolutionary AI assistant...',`
  - **Context:** The main descriptive text on the "About" screen.
  - **Priority:** ⚡ MEDIUM

- **File:** `lib/screens/profile/profile_screen.dart`
  - **Line 288:** `title: 'About Darvis',`
  - **Suggested Replacement:** `title: 'About Drix',`
  - **Context:** The navigation list item text that leads to the "About" screen.
  - **Priority:** ⚡ MEDIUM

### App Description:
- **File:** `pubspec.yaml`
  - **Line 2:** `description: "Darvis AI Assistant - The Conversational AI Second Brain"`
  - **Suggested Replacement:** `description: "Drix AI Assistant - The Conversational AI Second Brain"`
  - **Context:** This description is used by app stores and system utilities to describe the application.
  - **Priority:** ⚡ MEDIUM

## 📱 LOW PRIORITY - Tertiary Content & Placeholders:

### Contact & Help Text:
- **File:** `lib/screens/profile/contact_us_screen.dart`
  - **Line 300:** `() => _showComingSoon('Email Support'),`
  - **Note:** The `_showComingSoon` function could be updated to show a `<EMAIL>` email, but this is a logic change. The current UI doesn't display the email directly.
  - **Context:** Placeholder for the email support button.
  - **Priority:** 📱 LOW

### Image Assets:
- **File:** `lib/screens/home/<USER>
  - **Line 10:** `const String _kDarvisCardLogo = 'assets/images/darvisintelligence.png';`
  - **Note:** The asset `darvisintelligence.png` likely contains the word "Darvis". This image asset should be updated or replaced with a new "Drix" logo.
  - **Context:** This logo is displayed prominently on the "Today's Plate" card on the home screen.
  - **Priority:** 📱 LOW (as it requires a new asset, not just a text change)
