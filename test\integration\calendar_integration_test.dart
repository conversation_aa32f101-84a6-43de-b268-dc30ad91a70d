import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:darvis_app/blocs/calendar/calendar_bloc.dart';
import 'package:darvis_app/blocs/calendar/calendar_event.dart';
import 'package:darvis_app/blocs/calendar/calendar_state.dart';
import 'package:darvis_app/screens/calendar/calendar_screen.dart';
import 'package:darvis_app/services/navigation_service.dart';
import 'package:table_calendar/table_calendar.dart';

import 'calendar_integration_test.mocks.dart';

@GenerateMocks([CalendarBloc, NavigationService])
void main() {
  group('Calendar Screen Integration Tests', () {
    late MockCalendarBloc mockCalendarBloc;
    late MockNavigationService mockNavigationService;

    setUp(() {
      mockCalendarBloc = MockCalendarBloc();
      mockNavigationService = MockNavigationService();
      
      // Register mocks in GetIt
      GetIt.instance.reset();
      GetIt.instance.registerSingleton<CalendarBloc>(mockCalendarBloc);
      GetIt.instance.registerSingleton<NavigationService>(mockNavigationService);
    });

    tearDown(() {
      GetIt.instance.reset();
    });

    testWidgets('should display calendar widget', (WidgetTester tester) async {
      // Arrange
      final now = DateTime.now();
      final testEvents = [
        CalendarEventModel(
          id: '1',
          title: 'Team Meeting',
          description: 'Weekly standup meeting',
          startTime: now,
          endTime: now.add(const Duration(hours: 1)),
          type: 'meeting',
          isAllDay: false,
          createdAt: now,
          updatedAt: now,
        ),
        CalendarEventModel(
          id: '2',
          title: 'Project Review',
          description: 'Review project progress',
          startTime: now.add(const Duration(days: 1)),
          endTime: now.add(const Duration(days: 1, hours: 2)),
          type: 'review',
          isAllDay: false,
          createdAt: now,
          updatedAt: now,
        ),
      ];

      final eventsByDate = <DateTime, List<CalendarEventModel>>{
        DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day): [testEvents[0]],
        DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day + 1): [testEvents[1]],
      };

      final loadedState = CalendarLoaded(
        events: testEvents,
        filteredEvents: testEvents,
        currentDate: DateTime.now(),
        selectedDate: DateTime.now(),
        eventsByDate: eventsByDate,
      );

      when(mockCalendarBloc.state).thenReturn(loadedState);
      when(mockCalendarBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<CalendarBloc>.value(
            value: mockCalendarBloc,
            child: const CalendarScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Team Meeting'), findsOneWidget);
      expect(find.byType(TableCalendar), findsOneWidget);
    });

    testWidgets('should trigger LoadCalendarEvents on initialization', (WidgetTester tester) async {
      // Arrange
      when(mockCalendarBloc.state).thenReturn(const CalendarLoading());
      when(mockCalendarBloc.stream).thenAnswer((_) => Stream.value(const CalendarLoading()));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<CalendarBloc>.value(
            value: mockCalendarBloc,
            child: const CalendarScreen(),
          ),
        ),
      );

      // Assert
      verify(mockCalendarBloc.add(any)).called(1);
    });

    testWidgets('should display events for selected date', (WidgetTester tester) async {
      // Arrange
      final today = DateTime.now();
      final testEvents = [
        CalendarEventModel(
          id: '1',
          title: 'Morning Meeting',
          description: 'Daily standup',
          startTime: DateTime(today.year, today.month, today.day, 9, 0),
          endTime: DateTime(today.year, today.month, today.day, 10, 0),
          type: 'meeting',
          isAllDay: false,
          createdAt: today,
          updatedAt: today,
        ),
        CalendarEventModel(
          id: '2',
          title: 'Lunch Break',
          description: 'Team lunch',
          startTime: DateTime(today.year, today.month, today.day, 12, 0),
          endTime: DateTime(today.year, today.month, today.day, 13, 0),
          type: 'break',
          isAllDay: false,
          createdAt: today,
          updatedAt: today,
        ),
      ];

      final eventsByDate = <DateTime, List<CalendarEventModel>>{
        DateTime(today.year, today.month, today.day): testEvents,
      };

      final loadedState = CalendarLoaded(
        events: testEvents,
        filteredEvents: testEvents,
        currentDate: today,
        selectedDate: today,
        eventsByDate: eventsByDate,
      );

      when(mockCalendarBloc.state).thenReturn(loadedState);
      when(mockCalendarBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<CalendarBloc>.value(
            value: mockCalendarBloc,
            child: const CalendarScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Morning Meeting'), findsOneWidget);
      expect(find.text('Lunch Break'), findsOneWidget);
      expect(find.text('9:00 AM'), findsOneWidget);
      expect(find.text('12:00 PM'), findsOneWidget);
    });

    testWidgets('should handle calendar navigation', (WidgetTester tester) async {
      // Arrange
      final now = DateTime.now();
      final loadedState = CalendarLoaded(
        events: [],
        filteredEvents: [],
        currentDate: now,
        selectedDate: now,
        eventsByDate: {},
      );

      when(mockCalendarBloc.state).thenReturn(loadedState);
      when(mockCalendarBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<CalendarBloc>.value(
            value: mockCalendarBloc,
            child: const CalendarScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Find calendar widget
      final calendar = find.byType(TableCalendar);
      expect(calendar, findsOneWidget);

      // Test calendar interaction by tapping on a date
      // This tests that the calendar responds to user interaction
      await tester.tap(calendar);
      await tester.pumpAndSettle();

      // The calendar should handle the tap
      expect(calendar, findsOneWidget);
    });

    testWidgets('should display empty state when no events', (WidgetTester tester) async {
      // Arrange
      final now = DateTime.now();
      final loadedState = CalendarLoaded(
        events: [],
        filteredEvents: [],
        currentDate: now,
        selectedDate: now,
        eventsByDate: {},
      );

      when(mockCalendarBloc.state).thenReturn(loadedState);
      when(mockCalendarBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<CalendarBloc>.value(
            value: mockCalendarBloc,
            child: const CalendarScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('No events scheduled'), findsOneWidget);
      expect(find.text('Tap + to add an event'), findsOneWidget);
    });

    testWidgets('should handle add event navigation', (WidgetTester tester) async {
      // Arrange
      final now = DateTime.now();
      final loadedState = CalendarLoaded(
        events: [],
        filteredEvents: [],
        currentDate: now,
        selectedDate: now,
        eventsByDate: {},
      );

      when(mockCalendarBloc.state).thenReturn(loadedState);
      when(mockCalendarBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<CalendarBloc>.value(
            value: mockCalendarBloc,
            child: const CalendarScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Find and tap the add event button in the header
      final addButton = find.byIcon(Icons.add);
      if (addButton.evaluate().isNotEmpty) {
        await tester.tap(addButton.first);
        await tester.pumpAndSettle();

        // Assert - should trigger navigation to add event
        verify(mockNavigationService.navigateToAddEvent()).called(1);
      }
    });

    testWidgets('should handle add contact navigation', (WidgetTester tester) async {
      // Arrange
      final now = DateTime.now();
      final loadedState = CalendarLoaded(
        events: [],
        filteredEvents: [],
        currentDate: now,
        selectedDate: now,
        eventsByDate: {},
      );

      when(mockCalendarBloc.state).thenReturn(loadedState);
      when(mockCalendarBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<CalendarBloc>.value(
            value: mockCalendarBloc,
            child: const CalendarScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Find and tap the menu button to access add contact
      final menuButton = find.byIcon(Icons.more_vert);
      if (menuButton.evaluate().isNotEmpty) {
        await tester.tap(menuButton);
        await tester.pumpAndSettle();

        // Find add contact option in the popup menu
        final addContactOption = find.text('Add Contact');
        if (addContactOption.evaluate().isNotEmpty) {
          await tester.tap(addContactOption);
          await tester.pumpAndSettle();

          // Assert - should trigger navigation to add contact
          verify(mockNavigationService.navigateToAddContact()).called(1);
        }
      }
    });

    testWidgets('should handle calendar format changes', (WidgetTester tester) async {
      // Arrange
      final now = DateTime.now();
      final loadedState = CalendarLoaded(
        events: [],
        filteredEvents: [],
        currentDate: now,
        selectedDate: now,
        eventsByDate: {},
      );

      when(mockCalendarBloc.state).thenReturn(loadedState);
      when(mockCalendarBloc.stream).thenAnswer((_) => Stream.value(loadedState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<CalendarBloc>.value(
            value: mockCalendarBloc,
            child: const CalendarScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Find the calendar toggle button
      final toggleButton = find.byIcon(Icons.expand_more);
      if (toggleButton.evaluate().isNotEmpty) {
        await tester.tap(toggleButton);
        await tester.pumpAndSettle();

        // The calendar should expand/collapse
        expect(find.byType(TableCalendar), findsOneWidget);
      }
    });

    testWidgets('should display loading state', (WidgetTester tester) async {
      // Arrange
      when(mockCalendarBloc.state).thenReturn(const CalendarLoading());
      when(mockCalendarBloc.stream).thenAnswer((_) => Stream.value(const CalendarLoading()));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<CalendarBloc>.value(
            value: mockCalendarBloc,
            child: const CalendarScreen(),
          ),
        ),
      );

      // Assert
      // The calendar should still render with empty state while loading
      expect(find.byType(TableCalendar), findsOneWidget);
    });

    testWidgets('should handle error state', (WidgetTester tester) async {
      // Arrange
      const errorState = CalendarOperationError(
        error: 'Failed to load events',
        operation: 'load',
      );
      when(mockCalendarBloc.state).thenReturn(errorState);
      when(mockCalendarBloc.stream).thenAnswer((_) => Stream.value(errorState));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<CalendarBloc>.value(
            value: mockCalendarBloc,
            child: const CalendarScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      // The calendar should still render even in error state
      expect(find.byType(TableCalendar), findsOneWidget);
    });
  });
}
