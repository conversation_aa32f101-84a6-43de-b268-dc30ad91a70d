import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:google_sign_in/google_sign_in.dart';

import 'auth_service_interface.dart';
import 'api_service.dart';

/// Firebase implementation of AuthServiceInterface
class FirebaseAuthService implements AuthServiceInterface {
  final FirebaseAuth _firebaseAuth;
  final ApiService _apiService;
  final FlutterSecureStorage _secureStorage;
  final GoogleSignIn _googleSignIn;

  FirebaseAuthService({
    required ApiService apiService,
    required FlutterSecureStorage secureStorage,
    FirebaseAuth? firebaseAuth,
    GoogleSignIn? googleSignIn,
  })  : _apiService = apiService,
        _secureStorage = secureStorage,
        _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance,
        _googleSignIn = googleSignIn ?? GoogleSignIn();

  @override
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  @override
  User? get currentUser => _firebaseAuth.currentUser;

  @override
  bool get isAuthenticated => _firebaseAuth.currentUser != null;

  @override
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      print('🔐 Attempting Firebase sign-in for: $email');
      
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      print('✅ Firebase sign-in successful for: $email');

      // EXTRA DEBUG: Print the raw Firebase ID token immediately after sign-in
      try {
        final idToken = await credential.user?.getIdToken();
        print('🔥 [DEBUG] Firebase ID Token (post sign-in): $idToken');
        print('🔥 [DEBUG] Token length: ${idToken?.length ?? 0}');
      } catch (e) {
        print('⚠️ [DEBUG] Failed to fetch ID token immediately after sign-in: $e');
      }
      
      // Exchange Firebase token for our backend tokens
      await _exchangeFirebaseTokenForBackendTokens(credential.user!);
      
      return credential;
    } on FirebaseAuthException catch (e) {
      print('❌ Firebase sign-in failed for: $email - ${e.code}: ${e.message}');
      rethrow;
    }
  }

  @override
  Future<UserCredential> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      print('📝 Attempting Firebase sign-up for: $email with name: $name');
      
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      // Update display name
      await credential.user!.updateDisplayName(name);
      
      print('✅ Firebase sign-up successful for: $email');
      
      // Exchange Firebase token for our backend tokens
      await _exchangeFirebaseTokenForBackendTokens(credential.user!);
      
      return credential;
    } on FirebaseAuthException catch (e) {
      print('❌ Firebase sign-up failed for: $email - ${e.code}: ${e.message}');
      rethrow;
    }
  }

  @override
  Future<UserCredential?> signInWithGoogle() async {
    try {
      print('🔍 Starting Google Sign-In process...');

      // Sign out from Google first to ensure clean state
      await _googleSignIn.signOut();

      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        print('❌ Google Sign-In cancelled by user');
        return null; // User cancelled the sign-in
      }

      print('✅ Google account selected: ${googleUser.email}');

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        throw FirebaseAuthException(
          code: 'missing-google-auth-token',
          message: 'Failed to get Google authentication tokens',
        );
      }

      print('✅ Google authentication tokens obtained');

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      print('🔥 Signing in with Firebase...');
      final userCredential = await _firebaseAuth.signInWithCredential(credential);

      print('✅ Firebase sign-in successful: ${userCredential.user?.email}');

      // Exchange Firebase token for our backend tokens - wrap in try-catch to handle pigeon errors
      try {
        await _exchangeFirebaseTokenForBackendTokens(userCredential.user!);
        print('✅ Google Sign-In completed successfully with backend token exchange');
      } catch (tokenError) {
        // Don't fail the entire Google Sign-In if token exchange fails
        print('⚠️ Google Sign-In successful, but token exchange failed: $tokenError');
        // Continue with Firebase-only authentication
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      print('❌ Firebase Auth error during Google Sign-In: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      print('❌ Unexpected error during Google Sign-In: $e');
      throw FirebaseAuthException(
        code: 'google-signin-failed',
        message: 'Google Sign-In failed: $e',
      );
    }
  }

  @override
  Future<UserCredential?> signUpWithGoogle() async {
    // Google sign-up is the same as sign-in for OAuth providers
    return await signInWithGoogle();
  }

  @override
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException {
      rethrow;
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await Future.wait([
        _firebaseAuth.signOut(),
        _googleSignIn.signOut(),
        _secureStorage.delete(key: 'access_token'),
        _secureStorage.delete(key: 'refresh_token'),
      ]);
    } catch (e) {
      throw Exception('Failed to sign out: $e');
    }
  }

  @override
  Future<String?> getAccessToken() async {
    try {
      // First try to get from secure storage
      String? token = await _secureStorage.read(key: 'access_token');
      
      if (token != null) {
        return token;
      }
      
      // If not in storage, get Firebase token and exchange it
      final user = _firebaseAuth.currentUser;
      if (user != null) {
        await _exchangeFirebaseTokenForBackendTokens(user);
        return await _secureStorage.read(key: 'access_token');
      }
      
      return null;
    } catch (e) {
      throw Exception('Failed to get access token: $e');
    }
  }

  @override
  Future<void> refreshAccessToken() async {
    try {
      final refreshToken = await _secureStorage.read(key: 'refresh_token');
      if (refreshToken == null) {
        throw Exception('No refresh token available');
      }
      
      // Call backend to refresh token
      final response = await _apiService.refreshToken(refreshToken);
      
      await _secureStorage.write(
        key: 'access_token',
        value: response['access_token'],
      );
      
      if (response['refresh_token'] != null) {
        await _secureStorage.write(
          key: 'refresh_token',
          value: response['refresh_token'],
        );
      }
    } catch (e) {
      throw Exception('Failed to refresh access token: $e');
    }
  }

  Future<String?> getRefreshToken() async {
    return await _secureStorage.read(key: 'refresh_token');
  }

  /// Exchange Firebase ID token for backend access/refresh tokens and sync user profile
  Future<void> _exchangeFirebaseTokenForBackendTokens(User user) async {
    try {
      final idToken = await user.getIdToken();

      // ===== DEBUG: Print Firebase ID Token =====
      print('🔥 FIREBASE ID TOKEN FOR TESTING 🔥');
      print('User: ${user.email}');
      print('UID: ${user.uid}');
      print('ID Token: $idToken');
      print('Token length: ${idToken?.length ?? 0} characters');
      print('==========================================');

      try {
        // Call backend to exchange Firebase token for our tokens
        final response = await _apiService.exchangeFirebaseToken(idToken!);

        await _secureStorage.write(
          key: 'access_token',
          value: response['access_token'],
        );

        await _secureStorage.write(
          key: 'refresh_token',
          value: response['refresh_token'],
        );

        print('✅ Backend token exchange successful');

        // Sync user profile to backend after successful token exchange
        await _syncUserProfileToBackend(user);

      } catch (backendError) {
        // Backend is not available - continue with Firebase-only auth
        print('⚠️ Backend token exchange failed, continuing with Firebase-only auth: $backendError');

        // Store Firebase token as fallback
        await _secureStorage.write(
          key: 'firebase_token',
          value: idToken!,
        );

        // Generate mock tokens for development
        await _secureStorage.write(
          key: 'access_token',
          value: 'firebase_fallback_${DateTime.now().millisecondsSinceEpoch}',
        );

        await _secureStorage.write(
          key: 'refresh_token',
          value: 'firebase_refresh_${DateTime.now().millisecondsSinceEpoch}',
        );

        print('✅ Using Firebase fallback tokens');
      }
    } catch (e) {
      print('❌ Critical error in token exchange: $e');
      throw Exception('Failed to exchange Firebase token: $e');
    }
  }

  /// Sync user profile data to backend
  Future<void> _syncUserProfileToBackend(User user) async {
    try {
      // Prepare user profile data
      final profileData = {
        'firebase_uid': user.uid,
        'email': user.email,
        'display_name': user.displayName,
        'photo_url': user.photoURL,
        'email_verified': user.emailVerified,
        'created_at': user.metadata.creationTime?.toIso8601String(),
        'last_sign_in': user.metadata.lastSignInTime?.toIso8601String(),
      };

      // Call backend to create/update user profile
      await _apiService.syncUserProfile(profileData);
      print('✅ User profile synced to backend');

    } catch (e) {
      print('⚠️ Failed to sync user profile to backend: $e');
      // Don't throw - profile sync failure shouldn't break authentication
    }
  }
}
